analyzer:
  errors:
    duplicate_ignore: ignore# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
# include: package:flutter_lints/flutter.yaml

# linter:
#   # The lint rules applied to this project can be customized in the
#   # section below to disable rules from the `package:flutter_lints/flutter.yaml`
#   # included above or to enable additional rules. A list of all available lints
#   # and their documentation is published at https://dart.dev/lints.
#   #
#   # Instead of disabling a lint rule for the entire project in the
#   # section below, it can also be suppressed for a single line of code
#   # or a specific dart file by using the `// ignore: name_of_lint` and
#   # `// ignore_for_file: name_of_lint` syntax on the line or in the file
#   # producing the lint.
#   rules:
#     # Use 'const' with the constructor to improve performance.
#     prefer_const_constructors_in_immutables: false
#     prefer_const_constructors: false  # Uncomment to enable the `prefer_const_constructors` rule
#     constant_identifier_names: false  # Uncomment to enable the `constant_identifier_names` rule
#     avoid_print: false  # Uncomment to disable the `avoid_print` rule
#     prefer_single_quotes: false  # Uncomment to enable the `prefer_single_quotes` rule
# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
