// ignore_for_file:

import 'dart:io';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';

class ApiLogger {
 

  static Future<File?> _getLogFile() async {
    try {

      // Create SFM folder if it doesn't exist
      Directory sfmDir = Directory('storage/emulated/0/SFM');
      if (!await sfmDir.exists()) {
        await sfmDir.create();
      }
      print("sfmDir: $sfmDir");

     
      Directory reportDir = Directory('${sfmDir.path}/AppLogs');
      if (!await reportDir.exists()) {
        await reportDir.create();
      }
      print("reportDir: $reportDir");

      String fileName = "logs.txt";
      String filePath = '${reportDir.path}/$fileName';

     
      return File(filePath);
    } catch (e) {
      print("log file creation errorL: $e");
      showToastMessage("Failed to create log file: $e");
      return null; 
    }
  }

  static Future<void> logError({
    required String apiName,
    Map<String, dynamic>? apiRequestData,
    required String apiResponse,
  }) async {
    try {
      final file = await _getLogFile();
      if (file == null) return;

      final now = DateTime.now();
      final formattedDate = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);
      final logEntry = '''
[$formattedDate] API Name: $apiName
Request Data: ${apiRequestData.toString()}
Response: $apiResponse
---------------------------------
''';
      await file.writeAsString(logEntry, mode: FileMode.append);
    } catch (e) {
      showToastMessage("Failed to write log data: $e");
    }
  }

}
