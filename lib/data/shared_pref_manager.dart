import 'dart:convert';

import 'package:sfm_new/sfmDatabase/models/target_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SharedPrefManager {
  SharedPrefManager._(); 

  static final SharedPrefManager _instance =
      SharedPrefManager._(); 

  static SharedPrefManager get instance => _instance;

  // Future methods for asynchronous operations with Shared Preferences
  Future<void> setDouble(String key, double value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(key, value);
  }

  Future<double?> getDouble(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getDouble(key);
  }

  Future<void> setBool(String key, bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(key, value);
  }

  Future<bool?> getBool(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(key);
  }

  Future<void> setString(String key, String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, value);
  }

  Future<String?> getString(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(key);
  }

  Future<void> setTargets(List<Target> targets) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> targetsJson =
        targets.map((target) => json.encode(target.toJson())).toList();
    await prefs.setStringList('targets', targetsJson);
  }

  Future<List<Target>> getTargets() async {
    final prefs = await SharedPreferences.getInstance();
    List<String>? targetsJson = prefs.getStringList('targets');
    if (targetsJson != null) {
      return targetsJson.map((targetJson) {
        return Target.fromJson(json.decode(targetJson));
      }).toList();
    } else {
      return [];
    }
  }

  Future<void> setInt(String key, int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(key, value);
  }

  Future<int?> getInt(String key) async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(key);
  }

  // Clear all stored data
  Future<void> clearAll() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }
}
