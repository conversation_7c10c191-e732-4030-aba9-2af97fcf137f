
dynamic convertValue(dynamic value, [Type targetType = String]) {
  try {
    if (targetType == int) {
      String stringValue = value is String ? value : value.toString();
      return int.tryParse(stringValue) ?? 0;
    } else if (targetType == double) {
      String stringValue = value is String ? value : value.toString();
      return double.tryParse(stringValue) ?? 0.0;
    } else {
      return value;
    }
  } catch (e) {
    print("Error converting value to $targetType: $e");
    return targetType == int
        ? 0
        : targetType == double
            ? 0.0
            : "";
  }
}
