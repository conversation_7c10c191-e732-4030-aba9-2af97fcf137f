import 'package:flutter/material.dart';
import 'package:sfm_new/core/utils/size_utils.dart';
import 'package:sfm_new/theme/theme_helper.dart';

class FilterOption extends StatelessWidget {
  final String text;
  final bool isSelected;
  final Function() onTap;

  const FilterOption({
    Key? key,
    required this.text,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? theme.colorScheme.primary : Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.black),
        ),
        child: Text(
          text,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black,
            fontSize: 13.fSize,
          ),
        ),
      ),
    );
  }
}
