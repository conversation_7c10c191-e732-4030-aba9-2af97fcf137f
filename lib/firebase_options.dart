// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;


class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBaVSkTXU3fca-1xL8Q-a0OiCVl_tcURKQ',
    appId: '1:581095443422:web:548f92c3f424a38192ab4f',
    messagingSenderId: '581095443422',
    projectId: 'gopi-1b48c',
    authDomain: 'gopi-1b48c.firebaseapp.com',
    databaseURL: 'https://gopi-1b48c.firebaseio.com',
    storageBucket: 'gopi-1b48c.appspot.com',
    measurementId: 'G-FTHYYGMFJP',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBH6eB7l6iSbrYFixRJGgz4W4UewVHa7SI',
    appId: '1:581095443422:android:43cdfee663d0a05192ab4f',
    messagingSenderId: '581095443422',
    projectId: 'gopi-1b48c',
    databaseURL: 'https://gopi-1b48c.firebaseio.com',
    storageBucket: 'gopi-1b48c.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDhK2_OBtPFS8MNbuEdsQ5EulkKtyOcqTU',
    appId: '1:581095443422:ios:514c04d42db1b0e492ab4f',
    messagingSenderId: '581095443422',
    projectId: 'gopi-1b48c',
    databaseURL: 'https://gopi-1b48c.firebaseio.com',
    storageBucket: 'gopi-1b48c.appspot.com',
    iosBundleId: 'com.example.sfmV2Flutter',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDhK2_OBtPFS8MNbuEdsQ5EulkKtyOcqTU',
    appId: '1:581095443422:ios:f553d9bdff3b942992ab4f',
    messagingSenderId: '581095443422',
    projectId: 'gopi-1b48c',
    databaseURL: 'https://gopi-1b48c.firebaseio.com',
    storageBucket: 'gopi-1b48c.appspot.com',
    iosBundleId: 'com.nxccontrols.sfmNew',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBaVSkTXU3fca-1xL8Q-a0OiCVl_tcURKQ',
    appId: '1:581095443422:web:cc24df3f350e3b6292ab4f',
    messagingSenderId: '581095443422',
    projectId: 'gopi-1b48c',
    authDomain: 'gopi-1b48c.firebaseapp.com',
    databaseURL: 'https://gopi-1b48c.firebaseio.com',
    storageBucket: 'gopi-1b48c.appspot.com',
    measurementId: 'G-NDR8QTYCZ0',
  );
}
