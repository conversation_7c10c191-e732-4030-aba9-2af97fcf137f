import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class LocalizationService {
  final Map<String, String> enUs = {
    // Login Screen
    "lbl_welcome_to": "Welcome to",
    "lbl_app_name": "MyEBooks SFM",
    "lbl_company_id": "Company Id",
    "lbl_enter_your_code": "Enter your Code",
    "lbl_employee_code": "Employee code",
    "lbl_password": "Password",
    "lbl_enter_your_password": "Enter your password",
    "lbl_login": "LOGIN ",
    "lbl_product_by": "Product By :",
    "lbl_nxc_controls_pvt": "NXC Controls Pvt. Ltd.",

    //Sync Screen
    "lbl_sync_data": "Sync Data",
    "lbl_syncing_data": "Syncing Data",
    "lbl_sync_completed": "Sync Completed",
    "lbl_sync_failed": "Sync Failed",
    "lbl_completed": "Completed",
    "lbl_processing": "Processing",
    "lbl_failed": "Failed",
    "lbl_sync_again": "Sync Again",
    "lbl_go_to_home_screen": "Go to Home",

    "lbl_add_customer": "Add Customer",
    "lbl_add_order": "Add Order",
    "lbl_add_expense": "Add Expense",
    "lbl_add_leave": "Add Leave",
    "lbl_add_complain": "Add Complain",
    "lbl_add_payment": "Add Payment",
    "lbl_territory": "Territory",
    "lbl_product_brand": "Product Brand",
    "lbl_product_type": "Product Type",
    "lbl_uom": "UOM",
    "lbl_product": "Product",
    "lbl_market_type": "Market Type",
    "lbl_price_group": "Price Group",
    "lbl_customer_category": "Customer Category",
    "lbl_grade": "Grade",
    "lbl_beat": "Beat",
    "lbl_customer": "Customer",
    "lbl_bank": "Bank",
    "lbl_claim_complain_type": "Claim Complain Type",
    "lbl_claim_complain": "Claim Complain",
    "lbl_expense_type": "Expense Type",
    "lbl_expense": "Expense",
    "lbl_non_productive_reason": "Non-Productive Reason",
    "lbl_payment_type": "Payment Type",
    "lbl_payment_condition": "Payment Condition",
    "lbl_payment_collection": "Payment Collection",
    "lbl_leave": "Leave",
    "lbl_call": "Call",
    "lbl_order": "Order",
    "lbl_route_plan": "Route Plan",
    "lbl_target": "Target",
    "lbl_config": "Config",

    //Home Screen Drawer
    "lbl_re_sync_data": "Re-sync Data",
    "lbl_language": "Language",
    "lbl_help_support": "Help & Support",
    "lbl_logout": "Logout",

    //Help & Support Drawer
    "lbl_app_version": "App Version : ",
    "lbl_for_technical_help": "For technical help",
    "lbl_please_contact_on": "Please contact on:",
    "lbl_set_location_to_high_accuracy":
        "Set location to high accuracy for better app performance",
    "lbl_keep_map_updated": "Always keep GoogleMaps updated",

    // Home Screen
    "lbl_home": "Home",
    "lbl_add_your_order": "Add your order",
    "lbl_order_history": "Order History",
    "lbl_view_order_history": "View order history",
    "lbl_payment": "Payment",
    "lbl_complaint": "Complaint",
    "lbl_report": "Report",
    "lbl_team_report": "Team Report",
    "lbl_total_sale_amount": "Total Sale Amount (Last 7 days)",

    "msg_get_the_latest_company":
        "Get the latest Company stocks announcements......",

    // Report Screen
    "lbl_download_report": "Download Report",
    "lbl_export": "Export",

    //Payment
    "lbl_search_payment": "Search Payment",
    "lbl_total_payment": "Total Payment",
    "lbl_no_payment_found": "No payment found",
    "lbl_amount_placeholder": "Amount : ",

    //Add Payment
    "lbl_cash": "Cash",
    "lbl_cheque": "Cheque",
    "lbl_net_banking": "Net Banking",
    "lbl_submit": "Submit",
    "lbl_select_client_type": "Select Client Type",
    "lbl_select_client_name": "Select Client Name",
    "lbl_select_payment_rules": "Select Payment Rules",
    "lbl_amount": "Amount",
    "lbl_description": "Description",

    //Expense
    "lbl_search_expense": "Search Expense",
    "lbl_no_expense_found": "No expense found",
    "lbl_pending": "Pending",
    "lbl_approved": "Approved",

    //Add Expense
    "lbl_select_expense_type": "Select Expense Type",
    "lbl_details": "Details",
    "lbl_expense_photo_file": "Expense Photo/File",

    //Leave
    "lbl_search_leave": "Search Leave",
    "lbl_no_leave_found": "No leave found",

    //Leave Filter
    "lbl_all": "All",
    "lbl_date": "Date",
    "lbl_rejected": "Rejected",
    "lbl_full": "Full",
    "lbl_half": "Half",
    "lbl_cancel": "Cancel",
    "lbl_apply": "Apply",
    "lbl_filter": "Filters",
    "lbl_sort_by": "Sort By",

    //Add Leave
    "lbl_select_leave_type": "Select Leave Type",
    "lbl_enter_reason": "Enter Reason",

    //Complain
    "lbl_search_complain": "Search Complain",
    "lbl_no_complains_found": "No Complains Found",

    //Complain Filter
    "lbl_claim": "Claim",
    "lbl_complain": "Complain",

    //Add Complain
    "lbl_select_claim_complain": "All",
    "lbl_select_complain_type": "Select Complain Type",
    "lbl_select_claim_type": "Select Claim Type",
    "lbl_please_add_description": "Please add description",
    "lbl_claim_complain_photo": "Claim/Complain Photo",

    //Order History
    "lbl_search_orders": "Search Orders",
    "lbl_no_orders_found": "No orders found",
    "lbl_grand_total": "Grand Total",
    "lbl_total_orders": "Total Orders",
    "lbl_ss": "SS",
    "lbl_distributor": "Distributor",
    "lbl_dealer": "Dealer",
    "lbl_retailer": "Retailer",
    "lbl_today": "Today",
    "lbl_last_7_days": "Last 7 Days",
    "lbl_last_30_days": "Last 30 Days",

    //******************************************** */

    "lbl_history": "History",
    "lbl_info": "Info",

    "lbl_products_list": "Product List",
    "lbl_no_order": "No Order",
    "lbl_total_products": "Total Products ",

    "lbl_show_order": "Show Order",
    "lbl_confirm_order": "Confirm Order",

    "lbl_place_order": "Place Order",
    "lbl_select_distributor": "Select Distributor",
    "lbl_select_ss": "Select SS",
    "lbl_select_company": "Select Company",

    "lbl_unit_size": "Size",
    "lbl_bunch": "Bunch",
    "lbl_box": "Box",
    "lbl_innercase": "InnerCase",
    "lbl_outercase": "OuterCase",
    "lbl_pcs": "Pcs",

    // Sync data Six Screen
    "msg_downloading": "Downloading......",
    "msg_downloading_leave": "Downloading : Leave List -> Done",
    "msg_your_data_is_sync": "Your Data is Sync, please wait a moment",

    // Super stockist Screen
    "lbl_telephone_order": "Telephone Order",
    "msg_super_stockist_shop": "Super Stockist Shop name",

    // Distributors Screen
    "msg_distributor_shop": "Distributor Shop name",

    // Retailer Screen
    "msg_retailer_shop_name": "Retailer Shop name",

    // Retailer - Tab Container Screen
    "lbl_telephone_order2": "Telephone Order",

    // Shop Details Screen
    "lbl_23_march_2020": "23 March 2020",
    "lbl_dom": "DOM",
    "lbl_gst_number": "GST Number",
    "lbl_jp000av6t7y88": "JP000AV6T7Y88",
    "lbl_jp000gt": "JP000gt",
    "lbl_mobile": "Mobile",

    // Cash Payment Screen
    "msg_select_type_of_rule": "Select type of Rule",

    // Add Order Screen
    "lbl_520": "520",
    "lbl_barcode_0_0": "Barcode : 0.0",
    "lbl_box_0_001": "Box : 0.001",
    // "lbl_bunch": "Bunch",
    "lbl_kg_ltr_0_001": "KG/Ltr : 0.001",
    "lbl_mrp_0_001": "MRP : 0.001",
    // "lbl_products_list": "Products List",
    "lbl_pts": "PTS : ",
    "lbl_pts_0_001": "PTS : 0.001",
    "lbl_size_0_001": "Size : 0.001",
    "lbl_total_0": "Total : 0",
    "lbl_total_124563": "Total : 124563",

    // Order History Screen
    "lbl_52320_0": "52320.0",
    "lbl_non_productive": "Non-Productive",
    "lbl_total_order": "Total Order :",
    "lbl_total_qty": "Total Qty ",

    // Order Details Screen
    "lbl_delete_order": "Delete Order",
    "lbl_order_details": "Order Details",
    "msg_amee_saffron_1_gm2": "Amee Saffron 1 GM",
    "msg_transection_details": "Transaction Details",

    // Show Order Screen
    "lbl": "/",
    "lbl2": ".",
    "lbl3": ":",
    "lbl4": " :",
    "lbl_0_001": "0.001",
    "lbl_30_00": "30,00",
    "lbl_3_5": "3,5",
    "lbl_53": "53",
    "lbl_8_00": "8,00",
    "lbl_barcode": "Barcode",
    "lbl_box2": "Box :",
    "lbl_bunch2": "Bunch :",
    // "lbl_confirm_order": "Confirm Order",
    "lbl_free_qty": "Free QTY",
    "lbl_kg": "KG",
    "lbl_kg_lt": "KG/Lt",
    "lbl_lt": "Lt",
    "lbl_pcs2": "Pcs. :",
    "lbl_pcs3": "Pcs",
    "lbl_pts2": "PTS",
    "lbl_size": "Size",
    "msg_amee_saffron_2_gm": "Amee saffron 2 GM Box",
    "msg_amee_saffron_2_gm2": "Amee saffron 2 GM",

    // Place Payment Screen
    "lbl_notes": "Notes",
    // "lbl_place_order": "Place Order",
    // "lbl_select_company": "Select Company",
    "msg_trasportation_name": "Transporter Name",

    // No Order Screen
    "lbl_select_reason": "Select Reason",

    // View Complain Screen
    "lbl_claim_type": "Claim Type",
    "msg_defective_product": "Defective Product",
    "msg_navkar_sales_corporation2": "Navkar sales Corporation",

    // Add Complain Screen
    "lbl_select_client": "Select Client",
    "msg_claim_complain_photo": "Claim/Complain Photo",
    "msg_select_claim_complain": "Select Claim/Complaint",
    "msg_your_claim_complain": "Your Claim/Complaint",

    // Leave Screen
    "lbl_full_leave": "Full Leave ", "lbl_half_leave": "Half Leave",
    "lbl_reason": "Reason : ", "msg_reason_full_leave": "Reason : Full Leave ",

    // Add Leave Screen
    "lbl_reason2": "Reason",
    "msg_select_leave_type": "Select Leave Type",

    // Expense Screen
    "lbl_mobile_bill": "Mobile Bill",

    // View Expense Details Screen
    "lbl_13_march_2020": "13 March 2020",
    "lbl_expense_details": "Expense Details", "lbl_mobile_bill2": "Mobile bill",

    // Add Expense Screen

    "lbl_bill_photo": "Bill  Photo",
    "msg_select_expense_type": "Select Expense type",

    // Attendance Screen
    "lbl_absent": "Absent",
    "lbl_other_work": "Other Work",
    "lbl_good_morning": "Good Morning",
    "lbl_joint_call": "Joint Call",
    "lbl_employee_name": "Employee Name",
    "lbl_skip": "Skip",
    "lbl_solo_call": "Solo call",
    "msg_you_can_add_joint": "You can add joint call",
    "msg_you_can_add_solo": "You can add solo call",
    "msg_you_can_add_absent": "You can add absent",
    "msg_you_can_add_other_work": "You can add other work",

    // Graph Screen
    "lbl_apr": "Apr", "lbl_aug": "Aug", "lbl_jul": "Jul", "lbl_jun": "Jun",
    "lbl_may": "May", "lbl_oct": "Oct", "lbl_sep": "Sep",

    // Sync data Four Screen
    "msg_downloging_data": "Downloging Data......",

    // Sync data Screen

    // Sync data One Screen

    // Payment filter Screen
    // "lbl_last_7_days": "Last 7 days",
    // "lbl_today": "Today",

    // Complain filter Screen
    "lbl_distributors": "Distributors",

    // Leave filter Screen
    "lbl_sort_by2": "Sort by",
    "msg_company_activity": "Company Activity",

    // Payment filter One Screen
    "lbl_rtgs_neft_imps": "RTGS/NEFT/IMPS",

    // Cheque payment Screen
    "lbl_banck_name": "Banck Name", "lbl_cheque_payment": "Cheque Payment ",

    // RTGS/NEFT/IMPS Payment Screen
    "lbl_812547": "812547", "lbl_bank_name": "Bank Name",
    "lbl_utr_no": "UTR No",
    "msg_rtgs_neft_imps_payment": "RTGS/NEFT/IMPS Payment",

    // Cash Payment  One Screen
    "lbl_5000": "5000", "lbl_cash_payment": "Cash Payment ",

    // Inventory Screen
    "lbl_barcode_0": "Barcode : 0",
    "lbl_inventory": "Inventory",
    "lbl_mrp_600": "MRP : 600",
    "lbl_new_stock": "New Stock",
    "lbl_old_stock": "Old Stock",
    "lbl_size_12": "Size : 12",

    // Retailer shop list Screen
    "lbl_shop_list": "Shop List",
    "msg_kabir_general_store": "Kabir general store",
    "msg_shree_khodiyar_dairy": "Shree khodiyar dairy farm",

    // Retailer Shop Details Screen
    "lbl_3892350": "3892350",
    "lbl_ahmedabad": "Ahmedabad",
    "lbl_area": "Area",
    "lbl_birthdate": "Birthdate",
    "lbl_categary": "Categary",
    "lbl_landline_no": "Landline No.",
    "lbl_nikol": "Nikol",
    "lbl_pincode": "Pincode",
    "lbl_provision_store": "Provision store",
    "msg_kapwin_international_private": "Kapwin international\nPrivate limited",

    // notificaton Two Screen
    "lbl_clear_all": "Clear All", "lbl_done": "Done",
    "msg_mark_all_as_read": "Mark all as read",

    // notification Three Screen
    "lbl_view_more": "View More",

    // Common String
    "lbl_0": "0",
    "lbl_07": "07",
    "lbl_09": "09",
    "lbl_10": " 10",
    "lbl_100": "100",
    "lbl_10000": "10000",
    "lbl_11": "11",
    "lbl_11_aug": "11 Aug",
    "lbl_12": "12",
    "lbl_13": "13",
    "lbl_14": "14",
    "lbl_15": "15",
    "lbl_16_000": "16,000",
    "lbl_18_aug": "18 Aug",
    "lbl_18_seconds_left": "18 seconds left",
    "lbl_200": "200",
    "lbl_2021_04_01": "2021-04-01",
    "lbl_2021_04_22": "2021-04-22",
    "lbl_20_000": "20,000",
    "lbl_225": "225",
    "lbl_22_752": "22,752",
    "lbl_24_000": "24,000",
    "lbl_24_march_2021": "24 March 2021",
    "lbl_28_000": "28,000",
    "lbl_3": "3",
    "lbl_300_000": "300,000",
    "lbl_360_000": "360,000",
    "lbl_36_000": "36,000",
    "lbl_40_000": "40,000",
    "lbl_4586696_00": "4586696.00",
    "lbl_500": "500",
    "lbl_5231_00": "5231.00",
    "lbl_550": "550",
    "lbl_6": "6",
    "lbl_70": "70%",
    "lbl_7_jan_2021": "7 Jan 2021 ",
    "lbl_7_jan_20212": "7 Jan 2021",
    "lbl_84_927_83": "84,927.83",
    "lbl_9": "9",
    "lbl_91523_00": "91523.00",
    "lbl_98": "98",
    "lbl_9807_9807_25": "9807 9807 25",
    "lbl_a": "A",
    "lbl_account_no": "Account No.",
    "lbl_achieve": "Achieve",
    "lbl_acount_no": "Acount No",
    "lbl_address": "Address",

    "lbl_anniversary": "Anniversary",

    "lbl_balance": "Balance",
    // "lbl_box": "Box",
    "lbl_bunch_0_001": "Bunch : 0.001",
    "lbl_c_f": "C & F",
    "lbl_chaque": "Cheque",
    "lbl_cheque_no": " Cheque No",
    "lbl_cheque_no2": "Cheque No.",
    "lbl_cheque_photo": "Cheque Photo",
    "lbl_code": "Code",

    "lbl_date_of_cheque": "Date of cheque",
    "lbl_date_of_cheque2": "Date of Cheque",
    "lbl_dbr": "DBR",
    "lbl_default_page": "Default Page",

    // "lbl_distributor": "Distributor",
    "lbl_dsr": "DSR",
    "lbl_english": "English",
    "lbl_filters": "Filters",
    // "lbl_grand_total": "Grand Total",
    // "lbl_history": "History",
    "lbl_index": "Index",
    "lbl_kilogram": "Kilogram",
    "lbl_kipl": "KIPL",
    "lbl_liter": "Liter",
    "lbl_meeting": "Meeting",
    "lbl_mrp": "MRP",
    "lbl_n": "N",
    // "lbl_no_order": "No Order",
    "lbl_note": "Note",
    "lbl_notification": "Notification",
    "lbl_notifications": "Notifications",
    "lbl_pc": "PC :",
    "lbl_primary": "Primary",
    "lbl_primary_target": "Primary Target",
    "lbl_productive": "Productive",
    "lbl_r": "R",
    "lbl_remaining": "Remaining",
    // "lbl_retailer": "Retailer",
    "lbl_sales_report": "Sales Report",
    "lbl_secondary": "Secondary",
    "lbl_select_bank": "Select Bank",
    "lbl_service": "Service",
    "lbl_settings": "Settings",
    "lbl_shop_details": "Shop Details",
    "lbl_shop_name": "Shop Name",
    // "lbl_show_order": "Show Order",
    "lbl_status": "Status",
    "lbl_task": "Task",
    "lbl_add_beat": "Add Beat",
    "lbl_tc": "TC :",
    "lbl_test_note": "Test note",
    "lbl_testing_note": "Testing note",
    "lbl_this_month": "This Month",
    "lbl_this_week": "This Week",
    "lbl_to": "To",
    "lbl_total": "Total",
    "lbl_total_price": "Total Price",
    "lbl_total_qty2": "Total Qty",
    "lbl_total_sale": "Total Sale",
    "lbl_type": "Type",
    "lbl_user_name": "User Name",
    "lbl_view_bill": "View Bill",
    "msg_102_atma_house_nr":
        "102/Atma House,Nr Times Of India Ashram Road, Ahmedabad-382350",
    "msg_2021_04_22_13_49": "2021-04-22 13:49",
    "msg_amee_saffron_1_gm": "Amee saffron 1 GM",
    "msg_ananya_enterprices": "Ananya enterprices hubli",
    "msg_downloading_data": "Downloading Data.........",
    "msg_downloading_leave2": "Downloading : Leave List -> Done Leave List",
    "msg_downloading_leave3": "Downloading : Leave List ->Leave List Done",
    "msg_downloading_leave4": "Downloading : Leave List  Leave List-> Done",
    "msg_downloading_leave5": "Downloading : Leave List Leave List -> Done",
    "msg_employee_code": "Employee Code :  KP0001",
    "msg_forgot_password": "Forgot password?",
    "msg_navkar_sales_corporation": "Navkar sales corporation",
    "msg_nxc_controls_pvt": "NXC Controls Pvt. Ltd.",
    "msg_packaging_charge": "Packaging Charge",
    "msg_product_by_nxc": "Product By : NXC Controls Pvt. Ltd.",
    "msg_secondary_target": "Secondary Target",
    "msg_select_type_rules": "Select Type Rules",
    "msg_sku_sales_current": "SKU Sales\nCurrent Month",
    "msg_sku_sales_current2": "SKU Sales Current \nMonth",
    "msg_view_claim_complain": "View Claim/Complaint Photo",
    "msg_you_have_successfully":
        "You have successfully login a new device You have successfully login a new device",
    "msg_your_data_is_sync2": "Your Data is sync, please wait a moment",

// Network Error String
    "msg_network_err": "Network Error",
    "msg_something_went_wrong": "Something Went Wrong!",

    // Validation Error String
    "err_msg_please_enter_valid_password": "Please enter valid password",
    "err_msg_please_enter_valid_text": "Please enter valid text",
  };

  Future<void> loadLocalizationFromDb(String langCode) async {
    final List<Map<String, dynamic>> dbData =
        await getValues("${TableValues.tableLanguage}", langCode);

    dbData.forEach((item) {
      enUs[item['${TableValues.langTranslateKey}']] =
          item['${TableValues.langTranslateValue}'];
    });
  }

  Future<List<Map<String, dynamic>>> getValues(
      String table, String langCode) async {
    final db = await DatabaseProvider.database;
    return await db.query(table,
        where: '${TableValues.langCode} = ?', whereArgs: [langCode]);
  }
}
