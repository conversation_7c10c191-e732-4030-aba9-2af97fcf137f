import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:sfm_new/core/app_export.dart';

void openImageDialog(String typeName, String imageUrl, int type) {
  print("imageUrl: ${imageUrl}");
  Get.dialog(
    AlertDialog(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      title: Text(
        typeName,
        style: CustomTextStyles.titleMediumPrimary.copyWith(
          color: theme.colorScheme.primary,
        ),
      ),
      content: SizedBox(
        height: 250,
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (type == 1)
              InAppWebView(
                initialUrlRequest: URLRequest(url: WebUri(imageUrl)),
                onLoadStart: (controller, url) => print("Loading started"),
                onLoadStop: (controller, url) => print("Loading completed"),
              )
            else
              Image.memory(
                base64Decode(imageUrl),
                fit: BoxFit.cover, 
                height: 200, 
                width: 300, 
              ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: Text(
            'Close',
            style: CustomTextStyles.bodyMediumPoppinsBluegray900.copyWith(
              color: theme.colorScheme.primary,
            ),
          ),
        ),
      ],
    ),
  );
}
