import 'package:sqflite/sqflite.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/order_reason_model.dart';

class OrderReasonRepository {
  final Database _database;

  OrderReasonRepository(this._database);

  Future<void> insertOrderReason(OrderReason orderReason) async {
    try {
      await _database.insert(
        '${TableValues.tableOrderReason}',
        orderReason.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to insert order reason: $e');
    }
  }

  Future<List<OrderReason>> getAllOrderReasons() async {
    final List<Map<String, dynamic>> maps =
        await _database.query('${TableValues.tableOrderReason}');

    return List.generate(maps.length, (i) {
      return OrderReason.fromMap(maps[i]);
    });
  }
}
