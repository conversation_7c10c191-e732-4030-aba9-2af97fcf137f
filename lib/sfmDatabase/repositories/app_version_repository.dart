import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sqflite/sqflite.dart';

class AppVersionRepository {
  final Database _database;

  /// Constructor to initialize the repository with the database instance.
  AppVersionRepository(this._database);

  /// Inserts a new app version into the database.
  Future<void> insertAppVersion(String version) async {
    try {
      await _database.insert(
        TableValues.tableVersion,
        {TableValues.syncVersion: version},
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to insert app version: $e');
    }
  }

  /// Retrieves the current app version from the database.
  Future<String?> getAppVersion() async {
    try {
      final List<Map<String, dynamic>> maps =
          await _database.query(TableValues.tableVersion);
      if (maps.isNotEmpty) {
        return maps.first[TableValues.syncVersion] as String?;
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get app version: $e');
    }
  }

  /// Updates the current app version in the database.
  Future<void> updateAppVersion(String version) async {
    try {
      await _database.update(
        TableValues.tableVersion,
        {TableValues.syncVersion: version},
      );
    } catch (e) {
      throw Exception('Failed to update app version: $e');
    }
  }
}

