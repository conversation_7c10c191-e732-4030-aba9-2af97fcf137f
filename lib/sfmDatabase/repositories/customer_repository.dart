import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';

class CustomerRepository {
  static final CustomerRepository _instance = CustomerRepository._internal();

  factory CustomerRepository() {
    return _instance;
  }

  CustomerRepository._internal();

  List<Customer> _customerList = [];
  bool _isLoading = false;

  List<Customer> get customerList => _customerList;
  bool get isLoading => _isLoading;

  Future<void> fetchCustomers() async {
    _isLoading = true;

    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCustomer}");
    _customerList = List.generate(maps.length, (index) {
      return Customer.fromMap(maps[index]);
    });

    _isLoading = false;
  }
}
