import 'package:sqflite/sqflite.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/claim_complain_type_model.dart';

class ClaimComplainTypeRepository {
  final Database _database;

  ClaimComplainTypeRepository(this._database);

  Future<void> insertClaimComplainType(
      ClaimComplainType claimComplainType) async {
    try {
      await _database.insert(
        '${TableValues.tableClaimComplainType}',
        claimComplainType.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to insert claim/complain type: $e');
    }
  }

  Future<List<ClaimComplainType>> getAllClaimComplainTypes() async {
    final List<Map<String, dynamic>> maps =
        await _database.query('${TableValues.tableClaimComplainType}');

    return List.generate(maps.length, (i) {
      return ClaimComplainType.fromMap(maps[i]);
    });
  }
}
