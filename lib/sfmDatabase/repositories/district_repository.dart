import 'package:sqflite/sqflite.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class DistrictRepository {
  final Database _database;

  /// Constructor to initialize the repository with the database instance.
  DistrictRepository(this._database);

  /// Inserts a new bank into the database.
  Future<void> insertDistrictsAndTowns(
      List<Map<String, dynamic>> districtsAndTowns) async {
    try {

      await _database.transaction(
        (txn) async {
          for (var districtTown in districtsAndTowns) {
            print("districtTown: ${districtTown}");
            int districtId = await txn.insert(
                '${TableValues.tableDistrict}',
                {
                  '${TableValues.dmID}': districtTown['${TableValues.dmID}'],
                  '${TableValues.stateID}':
                      districtTown['${TableValues.stateID}'],
                  '${TableValues.dmName}':
                      districtTown['${TableValues.dmName}'],
                },
                conflictAlgorithm: ConflictAlgorithm.replace);

            // Insert towns
            List<Map<String, dynamic>> towns =
                List<Map<String, dynamic>>.from(districtTown['towns']);
            print("towns: ${towns}");
            for (var town in towns) {
              await txn.insert(
                  '${TableValues.tableTown}',
                  {
                    '${TableValues.townID}': town['${TableValues.townID}'],
                    '${TableValues.townName}': town['${TableValues.townName}'],
                    '${TableValues.districtID}': districtId,
                  },
                  conflictAlgorithm: ConflictAlgorithm.replace);
            }
          }
        },
      );
    } catch (e) {
      throw Exception('Failed to insert bank: $e');
    }
  }

  /// Retrieves all banks from the database.
  Future<List<String>> getAllDistricts() async {
    try {
      final List<Map<String, dynamic>> maps =
          await _database.query(TableValues.tableDistrict);
      return List<String>.from(
          maps.map((district) => district[TableValues.dmName]));
    } catch (e) {
      throw Exception('Failed to get banks: $e');
    }
  }
}

