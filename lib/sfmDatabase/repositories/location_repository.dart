import 'package:sqflite/sqflite.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/location_model.dart';

class LocationRepository {
  final Database _database;

  LocationRepository(this._database);

  Future<void> insertLocation(Location location) async {
    try {
      await _database.insert(
        '${TableValues.tableLocation}',
        location.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to insert location: $e');
    }
  }

  Future<List<Location>> getAllLocations() async {
    final List<Map<String, dynamic>> maps =
        await _database.query('${TableValues.tableLocation}');

    return List.generate(maps.length, (i) {
      return Location.fromMap(maps[i]);
    });
  }
}
