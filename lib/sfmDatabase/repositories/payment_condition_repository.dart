import 'package:sqflite/sqflite.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/payment_condition_model.dart';

class PaymentCondtionRepository {
  final Database _database;

  PaymentCondtionRepository(this._database);

  Future<void> insertPaymentCondition(PaymentCondition paymentCondition) async {
    try {
      await _database.insert(
        '${TableValues.tablePaymentCondition}',
        paymentCondition.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to insert payment rule: $e');
    }
  }

  Future<List<PaymentCondition>> getAllPaymentCondition() async {
    final List<Map<String, dynamic>> maps =
        await _database.query('${TableValues.tablePaymentCondition}');

    return List.generate(maps.length, (i) {
      return PaymentCondition.fromMap(maps[i]);
    });
  }
}
