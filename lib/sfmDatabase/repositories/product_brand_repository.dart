import 'package:sqflite/sqflite.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/product_brand_model.dart';

class ProductBrandRepository {
  final Database _database;

  ProductBrandRepository(this._database);

  Future<void> insertProductBrand(ProductBrand productBrand) async {
    try {
      await _database.insert(
        '${TableValues.tableProductBrand}',
        productBrand.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to insert product brand: $e');
    }
  }

  Future<List<ProductBrand>> getAllProductBrands() async {
    final List<Map<String, dynamic>> maps =
        await _database.query('${TableValues.tableProductBrand}');

    return List.generate(maps.length, (i) {
      return ProductBrand.fromMap(maps[i]);
    });
  }
}
