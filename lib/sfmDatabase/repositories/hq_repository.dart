import 'package:sqflite/sqflite.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/hq_model.dart';

class HeadQuarterRepository {
  final Database _database;

  HeadQuarterRepository(this._database);

  Future<void> insertHeadQuarters(
      List<Map<String, dynamic>> headquarters) async {
    try {
      await _database.transaction((txn) async {
        for (var headquarter in headquarters) {
          await txn.insert('${TableValues.tableHeadQuarter}', headquarter,
              conflictAlgorithm: ConflictAlgorithm.replace);
        }
      });
    } catch (e) {
      throw Exception('Failed to insert headquarter: $e');
    }
  }

  Future<List<HeadQuarter>> getAllHeadQuarters() async {
    final List<Map<String, dynamic>> maps =
        await _database.query('${TableValues.tableHeadQuarter}');

    return List.generate(maps.length, (i) {
      return HeadQuarter.fromMap(maps[i]);
    });
  }
}





