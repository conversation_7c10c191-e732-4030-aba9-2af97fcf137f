import 'package:sqflite/sqflite.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/shop_category_model.dart';

class ShopCategoryRepository {
  final Database _database;

  ShopCategoryRepository(this._database);

  Future<void> insertShopCategory(ShopCategory shopCategory) async {
    try {
      await _database.insert(
        '${TableValues.tableShopCategory}',
        shopCategory.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to insert shop category: $e');
    }
  }

  Future<List<ShopCategory>> getAllShopCategories() async {
    final List<Map<String, dynamic>> maps =
        await _database.query('${TableValues.tableShopCategory}');

    return List.generate(maps.length, (i) {
      return ShopCategory.fromMap(maps[i]);
    });
  }
}
