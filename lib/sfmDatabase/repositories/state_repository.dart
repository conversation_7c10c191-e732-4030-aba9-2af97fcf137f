import 'package:sqflite/sqflite.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/state_model.dart';

class StateRepository {
  final Database? _database;

  StateRepository(this._database);

  Future<void> insertState(List<Map<String, dynamic>> states) async {
    try {
      await _database!.transaction((txn) async {
        for (var state in states) {
          await txn.insert('${TableValues.tableState}', state,
              conflictAlgorithm: ConflictAlgorithm.replace);
        }
      });
    } catch (e) {
      throw Exception('Failed to insert state: $e');
    }
  }

  Future<List<StateModel>> getAllStates() async {
    final List<Map<String, dynamic>> maps =
        await _database!.query('${TableValues.tableState}');

    return List.generate(maps.length, (i) {
      return StateModel.fromJson(maps[i]);
    });
  }
}

