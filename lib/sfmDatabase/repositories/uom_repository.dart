import 'package:sqflite/sqflite.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/uom_model.dart';

class UOMRepository {
  final Database _database;

  UOMRepository(this._database);

  Future<void> insertUOM(UOM uom) async {
    try {
      await _database.insert(
        '${TableValues.tableUOM}',
        uom.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to insert UOM: $e');
    }
  }

  Future<List<UOM>> getAllUOMs() async {
    final List<Map<String, dynamic>> maps =
        await _database.query('${TableValues.tableUOM}');

    return List.generate(maps.length, (i) {
      return UOM.fromMap(maps[i]);
    });
  }
}
