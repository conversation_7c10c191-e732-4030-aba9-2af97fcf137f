import 'package:sqflite/sqflite.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class BankRepository {
  final Database _database;

  /// Constructor to initialize the repository with the database instance.
  BankRepository(this._database);

  /// Inserts a new bank into the database.
  Future<void> insertBank(String bankName) async {
    try {
      await _database.insert(
        TableValues.tableBank,
        {TableValues.bankName: bankName},
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to insert bank: $e');
    }
  }

  /// Retrieves all banks from the database.
  Future<List<String>> getAllBanks() async {
    try {
      final List<Map<String, dynamic>> maps =
          await _database.query(TableValues.tableBank);
      return List<String>.from(maps.map((bank) => bank[TableValues.bankName]));
    } catch (e) {
      throw Exception('Failed to get banks: $e');
    }
  }
}
