import 'package:sqflite/sqflite.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/market_type_model.dart';

class MarketTypeRepository {
  final Database _database;

  MarketTypeRepository(this._database);

  Future<void> insertMarketType(MarketType marketType) async {
    try {
      await _database.insert(
        '${TableValues.tableMarketType}',
        marketType.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to insert market type: $e');
    }
  }

  Future<List<MarketType>> getAllMarketTypes() async {
    final List<Map<String, dynamic>> maps =
        await _database.query('${TableValues.tableMarketType}');

    return List.generate(maps.length, (i) {
      return MarketType.fromMap(maps[i]);
    });
  }
}
