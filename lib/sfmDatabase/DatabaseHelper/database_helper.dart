import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class TableValues {
  //AppVersion
  static const String tableVersion = 'AppVersion';
  static const String syncVersion = 'sync_version';

  //Bank
  static const String tableBank = 'Bank';
  static const String bankID = 'bank_id';
  static const String bankName = 'bank_name';

  //ClaimCompainType
  static const String tableClaimComplainType = 'ClaimComplainType';
  static const String claimComplainID = 'cct_id';
  static const String claimComplainTypeName = 'cct_type_name';
  static const String claimComplainType = 'cct_type';

  //ExpenseType
  static const String tableExpenseType = 'ExpenseType';
  static const String expenseTypeID = 'et_id';
  static const String expenseTypeName = 'et_name';

  //HeadQuarter
  static const String tableHeadQuarter = 'HeadQuarter';
  static const String headquarterID = 'hq_id';
  static const String headquarterName = 'hq_name';

  //Location
  static const String tableLocation = 'Location';
  static const String locTimeStamp = 'loc_timestamp';
  static const String locLongitude = 'loc_longitude';
  static const String locLatitude = 'loc_latitude';
  static const String locAccuracy = 'loc_accuracy';
  static const String locSyncStatus = 'loc_sync_status';

  //Notification
  static const String tableNotification = 'Notification';
  static const String notificationID = 'notification_id';
  static const String notificationTime = 'notification_time';
  static const String notificationType = 'notification_type';
  static const String notificationImage = 'notification_image';
  static const String notificationTitle = 'notification_title';
  static const String notificationMessage = 'notification_message';
  static const String notificationReadStatus = 'notification_read_status';
  static const String notificationSyncStatus = 'notification_sync_status';

  //OrderReason
  static const String tableOrderReason = 'OrderReason';
  static const String reasonID = 'reason_id';
  static const String reasonName = 'reason_name';

  //PaymentCondition
  static const String tablePaymentCondition = 'PaymentCondition';
  static const String pcID = 'pc_id';
  static const String pcName = 'pc_name';

  static const String tableGrade = 'Grade';
  static const String gmID = 'gm_id';
  static const String gmName = 'gm_name';

  //ProductBrand
  static const String tableProductBrand = 'ProductBrand';
  static const String brandID = 'brand_id';
  static const String brandName = 'brand_name';

  //ShopCategory
  static const String tableShopCategory = 'ShopCategory';
  static const String categoryID = 'category_id';
  static const String categoryName = 'category_name';

  //State
  static const String tableState = 'State';
  static const String stateID = 'state_id';
  static const String stateName = 'state_name';
  static const String stateZone = 'state_zone';
  static const String stateGST = 'state_gst_code';

  //Config
  static const String tableConfig = 'Config';
  static const String configID = 'config_id';
  static const String configKey = 'config_key';
  static const String configValue = 'config_value';
  static const String configType = 'config_type';

  //*************************************************************
  //District
  static const String tableDistrict = 'District';
  static const String dmID = 'dm_id';
  static const String districtID = 'district_id';
  static const String dmName = 'dm_name';

  //Town
  static const String tableTown = 'Town';
  static const String townID = 'town_id';
  static const String townName = 'town_name';

  //ProductType
  static const String tableProductType = 'ProductType';
  static const String productTypeID = 'pt_id';
  static const String productTypeName = 'pt_name';

  //ProductType
  static const String tablePaymentType = 'PaymentType';
  static const String paymentTypeID = 'pt_id';
  static const String paymenTypeName = 'pt_name';

  //Localization
  static const String tableLanguage = 'Language';
  static const String langID = 'lang_id';
  static const String langCode = 'lang_code';
  static const String langName = 'lang_name';
  static const String langTranslateKey = 'lang_key';
  static const String langTranslateValue = 'lang_value';

  //LeaveRequest
  static const String tableLeaveRequest = 'LeaveRequest';
  static const String leaveCode = 'la_code';
  static const String leaveType =
      'la_type_id'; // Pending = 1, Completed = 2, Denied = 3
  static const String leaveFromDate = 'la_from_date';
  static const String leaveToDate = 'la_to_date';
  static const String leaveTotalDays = 'la_total_day';
  static const String leaveApprovedStatus = 'la_approved_status';
  static const String leaveReason = 'la_reason';
  static const String leaveEmpCode = 'la_emp_code';
  static const String leaveNotificationStatus = 'la_notification_status';
  static const String leaveApprovedBy = 'la_approved_by';
  static const String leaveCreatedAt = 'created_at';
  static const String leaveSyncStatus = 'leave_sync_status';

  //AddExpense
  static const String tableExpense = 'Expense';
  static const String expenseType = 'em_expense_type';
  static const String expenseCode = 'em_code';
  static const String expenseDetail = 'em_expense_detail';
  static const String expenseAmount = 'em_amount';
  static const String expenseApprovedAmount = 'em_approved_amount';
  static const String expenseBillImage = 'em_bill_picture';
  static const String expenseFileExtension = 'file_extension';
  static const String expenseLat = 'latitude';
  static const String expenseLong = 'longitude';
  static const String expenseEmpCode = 'emp_code';
  static const String expenseStatus = 'em_expense_status';
  static const String expenseDate = 'em_date';
  static const String expenseKM = 'em_km';
  static const String expenseSyncStatus = 'em_sync_status';

  //Target
  static const String tableTarget = 'Target';
  static const String targetEmpCode = 'employee_code';
  static const String targetEmpName = 'employee_name';
  static const String targetDesignation = 'designation';
  static const String targetStartDate = 'start_date';
  static const String targetEndDate = 'end_date';
  static const String targetType = 'type';
  static const String targetPrimaryTarget = 'primary_target';
  static const String targetPrimaryAchievement = 'primary_achievement';
  static const String targetPrimaryPercent = 'primary_percent';
  static const String targetSecondaryTarget = 'secondary_target';
  static const String targetSecondaryAchievement = 'secondary_achievement';
  static const String targetSecondaryPercent = 'secondary_percent';


  //CustomerMaster
  static const String tableCustomer = 'Customer';
  static const String customerCode = 'cm_code';
  static const String customerName = 'cm_name';
  static const String customerMobile = 'cm_mobile';
  static const String customerMobile2 = 'cm_mobile2';
  static const String customerEmail = 'cm_email';
  static const String customerAddress = 'cm_address';
  static const String customerPincode = 'cm_pincode';
  static const String customerGST = 'cm_gst';
  static const String customerPAN = 'cm_pan';
  static const String customerLatitude = 'cm_lat';
  static const String customerLongitude = 'cm_long';
  static const String customerMarketType = 'cm_market_type';
  static const String customerStatus = 'cm_status'; // '1-active, 0-inactive'
  static const String customerTownID = 'cm_town_id';
  static const String customerOutstandingAmount = 'cm_outstanding_amount';
  static const String customerContactPerson = 'cm_contact_person';
  static const String customerArea = 'cm_area';
  static const String customerRelationCode = 'cm_relation_code';
  static const String customerType =
      'cm_type'; //'1-company, 2-ss, 3-dist, 4-dealer, 5-retailer'
  static const String customerCreatedAt = 'cm_created_at';
  static const String customerAnniversary = 'cm_anniversary';
  static const String customerDOB = 'cm_dob';

  static const String customerImageRelation = 'cm_image_relation';
  static const String customerBeatRelationCode = 'cm_beat_relation';
  static const String customerCategoryRelationID = 'cm_category_relation';
  static const String customerGradeRelationID = 'cm_grade_relation';
  static const String customerSyncStatus = 'syncStatus';

  //MarketType
  static const String tableMarketType = 'MarketType';
  static const String marketID = 'mtm_id';
  static const String marketType = 'mtm_type';

  //PaymentCollection
  static const String tablePaymentCollection = 'PaymentCollection';
  static const String pcCode = 'pc_code';
  static const String pcCMCode = 'pc_cm_code';
  static const String pcType = 'pc_type';
  static const String pcChequeDate = 'pc_cheque_date';
  static const String pcAmount = 'pc_amount';
  static const String pcBankID = 'pc_bank_id';
  static const String pcChequeNo = 'pc_cheque_no';
  static const String pcNote = 'pc_note';
  static const String pcEmpCode = 'pc_emp_code';
  static const String pcAccountNo = 'pc_account_no';
  static const String pcChequePhoto = 'pc_cheque_photo';
  static const String pcConditionID = 'pc_condition_id';
  static const String pcStatus = 'pc_status';
  static const String pcCreatedDate = 'created_at';
  static const String pcUpdatedDate = 'updated_at';
  static const String pcSyncStatus = 'pc_sync_status';

  //ClaimComplain
  static const String tableClaimComplain = 'ClaimComplain';
  static const String ccCode = 'ccm_code';
  static const String ccTypeID = 'ccm_type_id';
  static const String ccNote = 'ccm_note';
  static const String ccImage = 'ccm_image';
  static const String ccCMCode = 'cm_code';
  static const String ccEmpCode = 'emp_code';
  static const String ccType = 'ccm_cc_type';
  static const String ccStatus = 'ccm_status';
  static const String ccRemarks = 'ccm_remarks';
  static const String ccCreatedAt = 'created_at';
  static const String ccUpdatedAt = 'updated_at';
  static const String ccSyncStatus = 'ccm_sync_status';

  //UOM
  static const String tableUOM = 'UOM';
  static const String uomID = 'uom_id';
  static const String uomName = 'uom_name';

  //NonProductive Reason
  static const String tableNonProductiveReason = 'NonProductiveReason';
  static const String nprID = 'npr_id';
  static const String nprReason = 'npr_reason';

  //Product
  static const String tableProduct = 'Product';
  static const String productCode = 'product_code';
  static const String productProductTypeID = 'pm_pt_id';
  static const String productBrand = 'product_brand';
  static const String productName = 'product_name';
  static const String productUOM = 'uom';
  static const String productUnitSize = 'unit_size';
  static const String productInnerCaseSize = 'inner_case_size';
  static const String productOuterCaseSize = 'outer_case_size';
  static const String productMRP = 'product_mrp';
  static const String productGST = 'product_gst';
  static const String productHSNCode = 'product_hsn_sac_code';
  static const String productImage = 'product_image';
  static const String productStatus = 'product_status';
  static const String productBarcode = 'barcode';
  static const String productMarketType = 'market_type';

  //PriceGroup
  static const String tablePriceGroup = 'PriceGroup';
  static const String pgID = 'id';
  static const String pgMarketType = 'market_type';
  static const String productPTS = 'product_ptss';
  static const String productPTDist = 'product_ptdistributor';
  static const String productPTD = 'product_ptdealer';
  static const String productPTR = 'product_ptretailer';

  //CustomerCategory
  static const String tableCustomerCategory = 'CustomerCategory';
  static const String ccmID = 'ccm_id';
  static const String ccmName = 'ccm_name';

  //StockDetails
  static const String tableStockDetails = 'StockDetails';
  static const String stockID = 'stock_id';
  static const String stockCustomerID = 'stock_customer_id';
  static const String stockProductCode = 'stock_product_code';
  static const String stockQTY = 'stock_qty';
  static const String stockDate = 'stock_date';
  static const String stockInTransit = 'stock_in_transit';
  static const String stockClosingStock = 'stock_closing_stock';
  static const String stockSyncStatus = 'stock_sync_status';

  //type,day,period
  //Beats
  static const String tableBeats = 'Beats';
  static const String beatCode = 'beat_code';
  static const String beatName = 'beat_name';
  static const String beatStatus = 'beat_status';
  static const String beatBDMDate = 'bdm_date';
  static const String beatBDMNumberwise = 'bdm_numberwise';
  static const String beatBDMType = 'bdm_type';
  static const String beatBDMDay = 'bdm_day';
  static const String beatBDMPeriod = 'bdm_period';
  static const String beatSyncStatus = 'beat_sync_status';

  //Call
  static const String tableCall = 'CallMaster';
  static const String callCode = 'call_code';
  static const String callClientCode = 'call_client_code';
  static const String callLat = 'call_latitude';
  static const String callLong = 'call_longitude';
  static const String callEmpCode = 'call_emp_code';

  //
  static const String callAccuracy = 'call_accuracy';
  static const String callPartyCode = 'call_party_code';
  static const String callTotalQTY = 'call_total_qty';
  static const String callGrandTotal = 'call_grand_total';
  static const String callOrderReasonID = 'call_order_reason_id';
  static const String callOrderType = 'call_order_type';
  static const String callRemark = 'call_remark';
  static const String callPackagingCharge = 'call_packaging_charge';
  static const String callTransportationCharge = 'call_transportation_charge';
  static const String callTransporterName = 'call_transporter_name';
  static const String callStartTime = 'call_start_time';
  static const String callStopTime = 'call_stop_time';
  static const String callOrderSign = 'call_order_sign';
  static const String callIsTelephonic = 'call_is_telephonic';
  static const String callCreatedAt = 'call_created_at';
  static const String callSyncStatus = 'call_order_sync_status';

  //Order
  static const String tableOrder = 'Orders';
  static const String orderCallCode = 'order_call_code';
  static const String orderCode = 'order_code';
  static const String orderProductCode = 'order_product_code';
  static const String orderProductQTY = 'order_product_qty';
  static const String orderProductMRP = 'order_product_mrp';
  static const String orderProductPTS = 'order_product_pts';
  static const String orderProductBaseRate = 'order_product_base_rate';
  static const String orderProductTotalBeforeTax =
      'order_product_total_before_tax';
  static const String orderProductTotalPriceWithTax =
      'order_product_total_price_with_tax';
  static const String orderProductTax = 'order_product_tax';
  static const String orderProductTaxAmount = 'order_product_tax_amount';

  static const String orderPiece = 'order_piece';
  static const String orderBunch = 'order_bunch';
  static const String orderBox = 'order_box';
  static const String orderProductSyncStatus = 'order_product_sync_status';
}

class DatabaseProvider {
  static const _databaseName = 'sfm.db';
  static const _version = 1;

  static Database? _database;

  static Future<Database> get database async {
    if (_database != null) {
      return _database!;
    } else {
      _database = await _initialize();
      return _database!;
    }
  }

  static Future<Database> _initialize() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, _databaseName);
    return await openDatabase(
      path,
      version: _version,
      onCreate: _onCreate,
      // onUpgrade: _onUpgrade,
    );
  }

 

  static Future<void> _onCreate(Database db, int version) async {
    await db.execute(_createAppVersionTable);
    await db.execute(_createBankTable);
    await db.execute(_createClaimComplainTypeTable);
    await db.execute(_createExpenseTypeTable);
    await db.execute(_createHeadQuarterTable);
    await db.execute(_createLocationTable);
    await db.execute(_createNotificationTable);
    await db.execute(_createOrderReasonTable);
    await db.execute(_createPaymentConditionTable);
    await db.execute(_createGradeTable);
    await db.execute(_createProductBrandTable);
    await db.execute(_createShopCategoryTable);
    await db.execute(_createStateTable);
    await db.execute(_createUOMTable);
    await db.execute(_createNonProductiveReasonTable);
    await db.execute(_createPaymentTypeTable);
    await db.execute(_createCustomerCategoryTable);
    await db.execute(_createTargetTable);
    await db.execute(_createConfigTable);
    await db.execute(_createLanguageTable);

//*******************************************************************************

    await db.execute(_createDistrictTable);
    await db.execute(_createTownTable);
    await db.execute(_createProductTypeTable);
    await db.execute(_createLeaveRequestTable);
    await db.execute(_createAddExpenseTable);
    await db.execute(_createMarketTypeTable);
    await db.execute(_createCustomerTable);
    await db.execute(_createPaymentCollectionTable);
    await db.execute(_createClaimComplainTable);
    await db.execute(_createProductTable);
    await db.execute(_createPriceGroup);
    await db.execute(_createStockDetailsTable);
    await db.execute(_createBeatTable);
    await db.execute(_createCallTable);
    await db.execute(_createOrderTable);
  }

  static const _createAppVersionTable = '''
    CREATE TABLE ${TableValues.tableVersion} (
      ${TableValues.syncVersion} TEXT DEFAULT '1.0'
    )
  ''';

  static const _createBankTable = '''
    CREATE TABLE ${TableValues.tableBank} (
      ${TableValues.bankID} INTEGER PRIMARY KEY,
      ${TableValues.bankName} TEXT DEFAULT NULL
    ) 
  ''';

  static const _createClaimComplainTypeTable = '''
    CREATE TABLE ${TableValues.tableClaimComplainType} (
      ${TableValues.claimComplainID} INTEGER PRIMARY KEY,
      ${TableValues.claimComplainTypeName} TEXT DEFAULT NULL,
      ${TableValues.claimComplainType} INTEGER DEFAULT 0
    )
  ''';

  static const _createExpenseTypeTable = '''
    CREATE TABLE ${TableValues.tableExpenseType} (
      ${TableValues.expenseTypeID} INTEGER PRIMARY KEY,
      ${TableValues.expenseTypeName} TEXT DEFAULT NULL
    )
  ''';

  static const _createHeadQuarterTable = '''
    CREATE TABLE ${TableValues.tableHeadQuarter} (
      ${TableValues.headquarterID} INTEGER PRIMARY KEY,
      ${TableValues.headquarterName} TEXT DEFAULT NULL
    )
  ''';

  static const _createLocationTable = '''
    CREATE TABLE ${TableValues.tableLocation} (
      ${TableValues.locTimeStamp} DATETIME DEFAULT CURRENT_TIMESTAMP,
      ${TableValues.locLongitude} REAL DEFAULT 0.0,
      ${TableValues.locLatitude} REAL DEFAULT 0.0, 
      ${TableValues.locAccuracy} REAL DEFAULT 0.0,
      ${TableValues.locSyncStatus} INTEGER DEFAULT 0
    )
  ''';

  static const _createNotificationTable = '''
    CREATE TABLE ${TableValues.tableNotification} (
      ${TableValues.notificationID} INTEGER PRIMARY KEY,
      ${TableValues.notificationTime} DATETIME DEFAULT CURRENT_TIMESTAMP,
      ${TableValues.notificationType} INTEGER DEFAULT 0,
      ${TableValues.notificationImage} TEXT DEFAULT NULL,
      ${TableValues.notificationTitle} TEXT DEFAULT NULL,
      ${TableValues.notificationMessage} TEXT DEFAULT NULL,
      ${TableValues.notificationReadStatus} INTEGER DEFAULT 0,
      ${TableValues.notificationSyncStatus} INTEGER DEFAULT 0
    )
  ''';

  static const _createOrderReasonTable = '''
    CREATE TABLE ${TableValues.tableOrderReason} (
      ${TableValues.reasonID} INTEGER PRIMARY KEY, 
      ${TableValues.reasonName} TEXT DEFAULT NULL
    )
  ''';

  static const _createPaymentConditionTable = '''
    CREATE TABLE ${TableValues.tablePaymentCondition} (
      ${TableValues.pcID} INTEGER PRIMARY KEY,
      ${TableValues.pcName} TEXT DEFAULT NULL
    )
  ''';

  static const _createGradeTable = '''
    CREATE TABLE ${TableValues.tableGrade} (
      ${TableValues.gmID} INTEGER PRIMARY KEY,
      ${TableValues.gmName} TEXT DEFAULT NULL
    )
  ''';

  static const _createProductBrandTable = '''
    CREATE TABLE ${TableValues.tableProductBrand} (
      ${TableValues.brandID} INTEGER PRIMARY KEY,
      ${TableValues.brandName} TEXT DEFAULT NULL
    )
  ''';

  static const _createShopCategoryTable = '''
    CREATE TABLE ${TableValues.tableShopCategory} (
      ${TableValues.categoryID} INTEGER PRIMARY KEY,
      ${TableValues.categoryName} TEXT DEFAULT NULL
    )
  ''';

  static const _createStateTable = '''
    CREATE TABLE ${TableValues.tableState} (
      ${TableValues.stateID} INTEGER PRIMARY KEY,
      ${TableValues.stateName} TEXT DEFAULT NULL,
      ${TableValues.stateZone} TEXT DEFAULT NULL,
      ${TableValues.stateGST} TEXT DEFAULT NULL
    )
  ''';

  static const _createLeaveRequestTable = '''
    CREATE TABLE ${TableValues.tableLeaveRequest} (
      ${TableValues.leaveCode} TEXT PRIMARY KEY,
      ${TableValues.leaveType} INTEGER DEFAULT 0,
      ${TableValues.leaveFromDate} DATE DEFAULT NULL,
      ${TableValues.leaveToDate} DATE DEFAULT NULL,
      ${TableValues.leaveTotalDays} INTEGER DEFAULT 0,
      ${TableValues.leaveApprovedStatus} INTEGER DEFAULT 0,
      ${TableValues.leaveReason} TEXT DEFAULT NULL,
      ${TableValues.leaveEmpCode} INTEGER DEFAULT 0,
      ${TableValues.leaveNotificationStatus} INTEGER DEFAULT 0,
      ${TableValues.leaveApprovedBy} INTEGER DEFAULT 0,
      ${TableValues.leaveCreatedAt} TEXT DEFAULT NULL,
      ${TableValues.leaveSyncStatus} INTEGER DEFAULT 0
    )
  ''';

  static const _createMarketTypeTable = '''
    CREATE TABLE ${TableValues.tableMarketType} (
      ${TableValues.marketID} INTEGER PRIMARY KEY,
      ${TableValues.marketType} TEXT DEFAULT NULL
    )
  ''';

  static const _createUOMTable = '''
    CREATE TABLE ${TableValues.tableUOM} (
      ${TableValues.uomID} INTEGER PRIMARY KEY,
      ${TableValues.uomName} TEXT DEFAULT NULL
    )
  ''';

  static const _createNonProductiveReasonTable = '''
    CREATE TABLE ${TableValues.tableNonProductiveReason} (
      ${TableValues.nprID} INTEGER PRIMARY KEY,
      ${TableValues.nprReason} TEXT DEFAULT NULL
    )
  ''';

  static const _createCustomerCategoryTable = '''
    CREATE TABLE ${TableValues.tableCustomerCategory} (
      ${TableValues.ccmID} INTEGER PRIMARY KEY,
      ${TableValues.ccmName} TEXT DEFAULT NULL
    )
  ''';

  static const _createTargetTable = '''
    CREATE TABLE ${TableValues.tableTarget} (
      ${TableValues.targetEmpCode} TEXT DEFAULT NULL,
      ${TableValues.targetEmpName} TEXT DEFAULT NULL,
      ${TableValues.targetDesignation} TEXT DEFAULT NULL,
      ${TableValues.targetStartDate} DATE DEFAULT NULL,
      ${TableValues.targetEndDate} DATE DEFAULT NULL,
      ${TableValues.targetType} TEXT DEFAULT NULL,
      ${TableValues.targetPrimaryTarget} TEXT DEFAULT NULL,
      ${TableValues.targetPrimaryAchievement} TEXT DEFAULT NULL,
      ${TableValues.targetPrimaryPercent} TEXT DEFAULT NULL,
      ${TableValues.targetSecondaryTarget} TEXT DEFAULT NULL,
      ${TableValues.targetSecondaryAchievement} TEXT DEFAULT NULL,
      ${TableValues.targetSecondaryPercent} TEXT DEFAULT NULL
    )
  ''';

  static const _createLanguageTable = '''
    CREATE TABLE ${TableValues.tableLanguage} (
      ${TableValues.langID} INTEGER PRIMARY KEY,
      ${TableValues.langCode} TEXT DEFAULT NULL,
      ${TableValues.langName} TEXT DEFAULT NULL,
      ${TableValues.langTranslateKey} TEXT DEFAULT NULL,
      ${TableValues.langTranslateValue} TEXT DEFAULT NULL
    )
  ''';

  //*******************************************************************************

  static const _createDistrictTable = '''
    CREATE TABLE ${TableValues.tableDistrict} (
      ${TableValues.dmID} INTEGER PRIMARY KEY,
      ${TableValues.dmName} TEXT DEFAULT NULL,
      ${TableValues.stateID} INTEGER DEFAULT 0,
      FOREIGN KEY (${TableValues.stateID}) REFERENCES ${TableValues.tableState}(${TableValues.stateID})
    )
  ''';

  static const _createTownTable = '''
    CREATE TABLE ${TableValues.tableTown} (
      ${TableValues.townID} INTEGER PRIMARY KEY,
      ${TableValues.townName} TEXT DEFAULT NULL,
      ${TableValues.districtID} INTEGER DEFAULT 0,
      FOREIGN KEY (${TableValues.districtID}) REFERENCES ${TableValues.tableDistrict}(${TableValues.dmID})
    )
  ''';

  static const _createProductTypeTable = '''
    CREATE TABLE ${TableValues.tableProductType} (
      ${TableValues.productTypeID} INTEGER PRIMARY KEY,
      ${TableValues.productTypeName} TEXT DEFAULT NULL,
      ${TableValues.brandID} INTEGER DEFAULT 0,
      FOREIGN KEY (${TableValues.brandID}) REFERENCES ${TableValues.tableProductBrand}(${TableValues.brandID})
    )
  ''';

  static const _createPaymentTypeTable = '''
    CREATE TABLE ${TableValues.tablePaymentType} (
      ${TableValues.paymentTypeID} INTEGER PRIMARY KEY,
      ${TableValues.paymenTypeName} TEXT DEFAULT NULL
    )
  ''';

  static const _createConfigTable = '''
    CREATE TABLE ${TableValues.tableConfig} (
      ${TableValues.configID} INTEGER PRIMARY KEY,
      ${TableValues.configKey} TEXT DEFAULT NULL,
      ${TableValues.configValue} TEXT DEFAULT NULL,
      ${TableValues.configType} TEXT DEFAULT NULL
    )
  ''';

  static const _createAddExpenseTable = '''
    CREATE TABLE ${TableValues.tableExpense} (
      ${TableValues.expenseType} INTEGER DEFAULT 0,
      ${TableValues.expenseCode} TEXT PRIMARY KEY,      
      ${TableValues.expenseDetail} TEXT DEFAULT NULL,
      ${TableValues.expenseAmount} DECIMAL,
      ${TableValues.expenseApprovedAmount} DECIMAL,
      ${TableValues.expenseBillImage} TEXT DEFAULT NULL,
      ${TableValues.expenseFileExtension} TEXT DEFAULT NULL,
      ${TableValues.expenseLat} REAL DEFAULT 0.0,
      ${TableValues.expenseLong} REAL DEFAULT 0.0,
      ${TableValues.expenseEmpCode} INTEGER DEFAULT 0,
      ${TableValues.expenseStatus} INTEGER DEFAULT 0,       -- '0-pending, 1-confirm, 2-rejected, 3-partial approved'
      ${TableValues.expenseDate} DATE DEFAULT NULL,
      ${TableValues.expenseKM} TEXT DEFAULT NULL,
      ${TableValues.expenseSyncStatus} INTEGER DEFAULT 0,
      FOREIGN KEY (${TableValues.expenseType}) REFERENCES ${TableValues.tableExpenseType}(${TableValues.expenseTypeID})
    )
  ''';

  static const _createCustomerTable = '''
    CREATE TABLE ${TableValues.tableCustomer} (
      ${TableValues.customerCode} TEXT PRIMARY KEY,
      ${TableValues.customerName} TEXT DEFAULT NULL,
      ${TableValues.customerMobile} INTEGER DEFAULT 0,
      ${TableValues.customerMobile2} INTEGER DEFAULT 0,
      ${TableValues.customerEmail} TEXT DEFAULT NULL,
      ${TableValues.customerAddress} TEXT DEFAULT NULL,
      ${TableValues.customerPincode} TEXT DEFAULT NULL,
      ${TableValues.customerGST} TEXT DEFAULT NULL,
      ${TableValues.customerPAN} TEXT DEFAULT NULL,
      ${TableValues.customerLatitude} REAL DEFAULT 0.0,
      ${TableValues.customerLongitude} REAL DEFAULT 0.0,
      ${TableValues.customerMarketType} INTEGER DEFAULT 0,
      ${TableValues.customerStatus} INTEGER DEFAULT 0,        -- '1-active, 0-inactive'   
      ${TableValues.customerTownID} INTEGER DEFAULT 0,
      ${TableValues.customerOutstandingAmount} DECIMAL,
      ${TableValues.customerContactPerson} TEXT DEFAULT NULL,
      ${TableValues.customerArea} TEXT DEFAULT NULL,
      ${TableValues.customerRelationCode} TEXT DEFAULT NULL,
      ${TableValues.customerType} INTEGER DEFAULT 0,          -- '1-company, 2-ss, 3-dist, 4-dealer, 5-retailer'
      ${TableValues.customerCreatedAt} TEXT DEFAULT NULL,
      ${TableValues.customerAnniversary} TEXT DEFAULT NULL,
      ${TableValues.customerDOB} TEXT DEFAULT NULL,
      ${TableValues.customerImageRelation} TEXT DEFAULT NULL,
      ${TableValues.customerBeatRelationCode} TEXT DEFAULT NULL,
      ${TableValues.customerCategoryRelationID} INTEGER DEFAULT 0, 
      ${TableValues.customerGradeRelationID} INTEGER DEFAULT 0, 
      ${TableValues.customerSyncStatus} INTEGER DEFAULT 0,
      FOREIGN KEY (${TableValues.customerMarketType}) REFERENCES ${TableValues.tableMarketType}(${TableValues.marketID}),
      FOREIGN KEY (${TableValues.customerTownID}) REFERENCES ${TableValues.tableTown}(${TableValues.townID}),
      FOREIGN KEY (${TableValues.customerRelationCode}) REFERENCES ${TableValues.tableCustomer}(${TableValues.customerCode}),
      FOREIGN KEY (${TableValues.customerBeatRelationCode}) REFERENCES ${TableValues.tableBeats}(${TableValues.beatCode}),
      FOREIGN KEY (${TableValues.customerCategoryRelationID}) REFERENCES ${TableValues.tableCustomerCategory}(${TableValues.ccmID}),
      FOREIGN KEY (${TableValues.customerGradeRelationID}) REFERENCES ${TableValues.tableGrade}(${TableValues.gmID})
    )
  ''';

//'1-Cash, 2-Cheque, 3-NEFT'
  static const _createPaymentCollectionTable = '''
    CREATE TABLE ${TableValues.tablePaymentCollection} (
      ${TableValues.pcCode} TEXT PRIMARY KEY, 
      ${TableValues.pcCMCode} TEXT DEFAULT NULL,
      ${TableValues.pcType} INTEGER DEFAULT 0,  
      ${TableValues.pcChequeDate} DATE DEFAULT NULL,
      ${TableValues.pcAmount} DECIMAL,
      ${TableValues.pcBankID} INTEGER DEFAULT 0,
      ${TableValues.pcChequeNo} TEXT DEFAULT NULL,
      ${TableValues.pcNote} TEXT DEFAULT NULL,
      ${TableValues.pcEmpCode} INTEGER DEFAULT 0,
      ${TableValues.pcAccountNo} TEXT DEFAULT NULL,
      ${TableValues.pcChequePhoto} TEXT DEFAULT NULL,
      ${TableValues.pcConditionID} INTEGER DEFAULT 0,
      ${TableValues.pcStatus} INTEGER DEFAULT 0,
      ${TableValues.pcCreatedDate} DATE DEFAULT NULL,
      ${TableValues.pcUpdatedDate} DATE DEFAULT NULL,
      ${TableValues.pcSyncStatus} INTEGER DEFAULT 0,
      FOREIGN KEY (${TableValues.pcCMCode}) REFERENCES ${TableValues.tableCustomer}(${TableValues.customerCode}),
      FOREIGN KEY (${TableValues.pcConditionID}) REFERENCES ${TableValues.tablePaymentCondition}(${TableValues.pcID}),
      FOREIGN KEY (${TableValues.pcBankID}) REFERENCES ${TableValues.tableBank}(${TableValues.bankID})
    )
  ''';

//'1-company, 2-ss, 3-dist, 4-dealer, 5-retailer'
//ccTypeID = '1-claim, 0-complain'
  static const _createClaimComplainTable = '''
    CREATE TABLE ${TableValues.tableClaimComplain} (
      ${TableValues.ccCode} TEXT PRIMARY KEY, 
      ${TableValues.ccTypeID} INTEGER DEFAULT 0,  
      ${TableValues.ccNote} TEXT DEFAULT NULL,  
      ${TableValues.ccImage} TEXT DEFAULT NULL,
      ${TableValues.ccCMCode} TEXT DEFAULT NULL,
      ${TableValues.ccEmpCode} TEXT DEFAULT NULL,
      ${TableValues.ccType}  INTEGER DEFAULT 0,
      ${TableValues.ccStatus} INTEGER DEFAULT 0,
      ${TableValues.ccRemarks} TEXT DEFAULT NULL,  
      ${TableValues.ccCreatedAt} DATE DEFAULT NULL,
      ${TableValues.ccUpdatedAt} DATE DEFAULT NULL,
      ${TableValues.ccSyncStatus} INTEGER DEFAULT 0,
      FOREIGN KEY (${TableValues.ccCode}) REFERENCES ${TableValues.tableCustomer}(${TableValues.customerCode}),
      FOREIGN KEY (${TableValues.ccType}) REFERENCES ${TableValues.tableCustomer}(${TableValues.customerType})
    )
  ''';

  static const _createProductTable = '''
    CREATE TABLE ${TableValues.tableProduct} (
      ${TableValues.productCode} TEXT PRIMARY KEY,
      ${TableValues.productProductTypeID} INTEGER DEFAULT 0,  
      ${TableValues.productBrand} INTEGER DEFAULT 0,
      ${TableValues.productName} TEXT DEFAULT NULL,
      ${TableValues.productUOM} INTEGER DEFAULT 0,
      ${TableValues.productUnitSize} REAL DEFAULT 0.0,
      ${TableValues.productInnerCaseSize} INTEGER DEFAULT 0,
      ${TableValues.productOuterCaseSize} INTEGER DEFAULT 0,
      ${TableValues.productMRP} DECIMAL,
      ${TableValues.productGST} DECIMAL,
      ${TableValues.productHSNCode} TEXT DEFAULT NULL,
      ${TableValues.productImage} TEXT DEFAULT NULL,
      ${TableValues.productStatus} INTEGER DEFAULT 0,
      ${TableValues.productBarcode} TEXT DEFAULT NULL,
      ${TableValues.productMarketType} INTEGER DEFAULT 0,
      ${TableValues.productPTS} DECIMAL,
      ${TableValues.productPTDist} DECIMAL,
      ${TableValues.productPTD} DECIMAL,
      ${TableValues.productPTR} DECIMAL,
      FOREIGN KEY (${TableValues.productProductTypeID}) REFERENCES ${TableValues.tableProductType}(${TableValues.productTypeID}),
      FOREIGN KEY (${TableValues.productBrand}) REFERENCES ${TableValues.tableProductBrand}(${TableValues.brandID}),
      FOREIGN KEY (${TableValues.productUOM}) REFERENCES ${TableValues.tableUOM}(${TableValues.uomID})
    )
  ''';

  static const _createPriceGroup = '''
    CREATE TABLE ${TableValues.tablePriceGroup} (
      ${TableValues.pgID} INTEGER PRIMARY KEY,
      ${TableValues.productCode} TEXT DEFAULT NULL,
      ${TableValues.pgMarketType} INTEGER DEFAULT 0, 
      ${TableValues.productPTS} DECIMAL,
      ${TableValues.productPTDist} DECIMAL,
      ${TableValues.productPTD} DECIMAL,
      ${TableValues.productPTR} DECIMAL,
      FOREIGN KEY (${TableValues.productCode}) REFERENCES ${TableValues.tableProduct}(${TableValues.productCode}),
      FOREIGN KEY (${TableValues.pgMarketType}) REFERENCES ${TableValues.tableMarketType}(${TableValues.marketID})
    )
  ''';

  static const _createStockDetailsTable = '''
    CREATE TABLE ${TableValues.tableStockDetails} (
      ${TableValues.stockID} TEXT PRIMARY KEY,
      ${TableValues.stockCustomerID} TEXT DEFAULT NULL,
      ${TableValues.stockProductCode} TEXT DEFAULT NULL,
      ${TableValues.stockQTY} INTEGER DEFAULT 0,
      ${TableValues.stockDate} DATE DEFAULT NULL,
      ${TableValues.stockInTransit} TEXT DEFAULT NULL,
      ${TableValues.stockClosingStock} INTEGER DEFAULT 0,
       ${TableValues.stockSyncStatus} INTEGER DEFAULT 0,
      FOREIGN KEY (${TableValues.stockCustomerID}) REFERENCES ${TableValues.tableCustomer}(${TableValues.customerCode}),
      FOREIGN KEY (${TableValues.stockProductCode}) REFERENCES ${TableValues.tableProduct}(${TableValues.productCode})
    )
  ''';



  static const _createBeatTable = '''
    CREATE TABLE ${TableValues.tableBeats} (
      ${TableValues.beatCode} TEXT PRIMARY KEY,
      ${TableValues.beatName} TEXT DEFAULT NULL,  
      ${TableValues.beatStatus} INTEGER DEFAULT 0,        -- '0-Pending, 1-Approved, 2-Reject'
      ${TableValues.beatBDMDate} DATE DEFAULT NULL,  
      ${TableValues.beatBDMNumberwise} INTEGER DEFAULT 0,
      ${TableValues.beatBDMType} INTEGER DEFAULT 0,        -- '1-Fixed, 2-Daywise, 3-Datewise, 4-Period'
      ${TableValues.beatBDMDay} TEXT DEFAULT NULL,  
      ${TableValues.beatBDMPeriod} INTEGER DEFAULT 0,
      ${TableValues.beatSyncStatus} INTEGER DEFAULT 0
    )
  ''';

  static const _createCallTable = '''
    CREATE TABLE ${TableValues.tableCall} (
      ${TableValues.callCode} TEXT PRIMARY KEY,
      ${TableValues.callClientCode} TEXT DEFAULT NULL,  
      ${TableValues.callLat} REAL DEFAULT 0.0,  
      ${TableValues.callLong} REAL DEFAULT 0.0,
      ${TableValues.callEmpCode} INTEGER DEFAULT 0,
      ${TableValues.callAccuracy} REAL DEFAULT 0.0,
      ${TableValues.callPartyCode} TEXT DEFAULT NULL,  
      ${TableValues.callTotalQTY} INTEGER DEFAULT 0,
      ${TableValues.callGrandTotal} DECIMAL,
      ${TableValues.callOrderReasonID} INTEGER DEFAULT 0,
      ${TableValues.callOrderType} INTEGER DEFAULT 0,
      ${TableValues.callRemark} TEXT DEFAULT NULL,  
      ${TableValues.callPackagingCharge} DECIMAL,
      ${TableValues.callTransportationCharge} DECIMAL,
      ${TableValues.callTransporterName} TEXT DEFAULT NULL,
      ${TableValues.callStartTime} TEXT DEFAULT NULL,
      ${TableValues.callStopTime} TEXT DEFAULT NULL,
      ${TableValues.callOrderSign} TEXT DEFAULT NULL,
      ${TableValues.callIsTelephonic} INTEGER DEFAULT 0,
      ${TableValues.callCreatedAt} DATE DEFAULT NULL,
      ${TableValues.callSyncStatus} INTEGER DEFAULT 0,
      FOREIGN KEY (${TableValues.callClientCode}) REFERENCES ${TableValues.tableCustomer}(${TableValues.customerCode}),
      FOREIGN KEY (${TableValues.callPartyCode}) REFERENCES ${TableValues.tableCustomer}(${TableValues.customerCode})
    )
  ''';

  static const _createOrderTable = '''
    CREATE TABLE ${TableValues.tableOrder} (
      ${TableValues.orderCallCode} TEXT DEFAULT NULL,
      ${TableValues.orderCode} TEXT PRIMARY KEY,  
      ${TableValues.orderProductCode} TEXT DEFAULT NULL,  
      ${TableValues.orderProductQTY} INTEGER DEFAULT 0,
      ${TableValues.orderProductMRP} DECIMAL,
      ${TableValues.orderProductPTS} DECIMAL,
      ${TableValues.orderProductBaseRate} DECIMAL,
      ${TableValues.orderProductTotalBeforeTax} DECIMAL,
      ${TableValues.orderProductTotalPriceWithTax} DECIMAL,
      ${TableValues.orderProductTax} REAL DEFAULT 0.0,
      ${TableValues.orderProductTaxAmount} DECIMAL,
      ${TableValues.orderPiece} INTEGER DEFAULT 0,
      ${TableValues.orderBunch} INTEGER DEFAULT 0,  
      ${TableValues.orderBox} INTEGER DEFAULT 0,  
      ${TableValues.orderProductSyncStatus} INTEGER DEFAULT 0,
      FOREIGN KEY (${TableValues.orderCallCode}) REFERENCES ${TableValues.tableCall}(${TableValues.callCode}),
      FOREIGN KEY (${TableValues.orderProductCode}) REFERENCES ${TableValues.tableProduct}(${TableValues.productCode})
    )
  ''';
}

