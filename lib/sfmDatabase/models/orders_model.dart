import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class OrderData {
  String orderCallCode;
  String? orderCode;
  String? orderProductCode;
  int? orderProductQty;
  double? orderProductMrp;
  double? orderProductPts;
  double? orderProductBaseRate;
  double? orderProductTotalBeforeTax;
  double? orderProductTotalPriceWithTax;
  double? orderProductTax;
  double? orderProductTaxAmount;
  int? orderPiece;
  int? orderBunch;
  int? orderBox;
  int? orderProductSyncStatus;

  OrderData({
    required this.orderCallCode,
    this.orderCode,
    this.orderProductCode,
    this.orderProductQty,
    this.orderProductMrp,
    this.orderProductPts,
    this.orderProductBaseRate,
    this.orderProductTotalBeforeTax,
    this.orderProductTotalPriceWithTax,
    this.orderProductTax,
    this.orderProductTaxAmount,
    this.orderPiece,
    this.orderBunch,
    this.orderBox,
    this.orderProductSyncStatus,
  });

  factory OrderData.fromMap(Map<String, dynamic> map) {
    double orderProductTotalBeforeTax =
        (map['${TableValues.orderProductTotalBeforeTax}'] ?? 0.0).toDouble();
    orderProductTotalBeforeTax =
        double.parse(orderProductTotalBeforeTax.toStringAsFixed(2));

    return OrderData(
      orderCallCode: map['${TableValues.orderCallCode}'],
      orderCode: map['${TableValues.orderCode}'],
      orderProductCode: map['${TableValues.orderProductCode}'],
      orderProductQty: map['${TableValues.orderProductQTY}'],
      orderProductMrp:
          (map['${TableValues.orderProductMRP}'] ?? 0.0).toDouble(),
      orderProductPts:
          (map['${TableValues.orderProductPTS}'] ?? 0.0).toDouble(),
      orderProductBaseRate:
          (map['${TableValues.orderProductBaseRate}'] ?? 0.0).toDouble(),
      orderProductTotalBeforeTax: orderProductTotalBeforeTax,
      orderProductTotalPriceWithTax:
          (map['${TableValues.orderProductTotalPriceWithTax}'] ?? 0.0)
              .toDouble(),
      orderProductTax:
          (map['${TableValues.orderProductTax}'] ?? 0.0).toDouble(),
      orderProductTaxAmount:
          (map['${TableValues.orderProductTaxAmount}'] ?? 0.0).toDouble(),
      orderPiece: map['${TableValues.orderPiece}'],
      orderBunch: map['${TableValues.orderBunch}'],
      orderBox: map['${TableValues.orderBox}'],
      orderProductSyncStatus: map['${TableValues.orderProductSyncStatus}'],
    );
  }

  // (map['product_gst ?? 0).toDouble(),

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.orderCallCode}': orderCallCode,
      '${TableValues.orderCode}': orderCode,
      '${TableValues.orderProductCode}': orderProductCode,
      '${TableValues.orderProductQTY}': orderProductQty,
      '${TableValues.orderProductMRP}': orderProductMrp,
      '${TableValues.orderProductPTS}': orderProductPts,
      '${TableValues.orderProductBaseRate}': orderProductBaseRate,
      '${TableValues.orderProductTotalBeforeTax}': orderProductTotalBeforeTax,
      '${TableValues.orderProductTotalPriceWithTax}':
          orderProductTotalPriceWithTax,
      '${TableValues.orderProductTax}': orderProductTax,
      '${TableValues.orderProductTaxAmount}': orderProductTaxAmount,
      '${TableValues.orderPiece}': orderPiece,
      '${TableValues.orderBunch}': orderBunch,
      '${TableValues.orderBox}': orderBox,
      '${TableValues.orderProductSyncStatus}': orderProductSyncStatus,
    };
  }

  @override
  String toString() {
    return 'OrderProduct{'
        'orderCallCode: $orderCallCode, '
        'orderCode: $orderCode, '
        'orderProductCode: $orderProductCode, '
        'orderProductQty: $orderProductQty, '
        'orderProductMrp: $orderProductMrp, '
        'orderProductPts: $orderProductPts, '
        'orderProductBaseRate: $orderProductBaseRate, '
        'orderProductTotalBeforeTax: $orderProductTotalBeforeTax, '
        'orderProductTotalPriceWithTax: $orderProductTotalPriceWithTax, '
        'orderProductTax: $orderProductTax, '
        'orderProductTaxAmount: $orderProductTaxAmount, '
        'orderPiece: $orderPiece, '
        'orderBunch: $orderBunch, '
        'orderBox: $orderBox, '
        'orderProductSyncStatus: $orderProductSyncStatus'
        '}';
  }
}
