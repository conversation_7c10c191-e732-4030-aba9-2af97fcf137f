import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class HeadQuarter {
  int hq_id;
  String? hq_name;

  HeadQuarter({
    required this.hq_id,
    this.hq_name,
  });

  factory HeadQuarter.fromMap(Map<String, dynamic> map) {
    return HeadQuarter(
      hq_id: map['${TableValues.headquarterID}'] ?? 0,
      hq_name: map['${TableValues.headquarterName}'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.headquarterID}': hq_id,
      '${TableValues.headquarterName}': hq_name,
    };
  }
}
