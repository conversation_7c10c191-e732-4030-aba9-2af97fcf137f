class Expense {
  final int? expenseType;
  final String? expenseCode;
  final String? expenseDetail;
  final double amount;
  final int? approvedAmount;
 String? billPicture;
  final String? fileExtension;
  final double? longitude;
  final double? latitude;
  final String? employeeCode;
  final int? expenseStatus;
  final String? date;
  final String? kilometers;
  final int syncStatus;

  Expense({
    this.expenseType,
    this.expenseCode,
    this.expenseDetail,
    required this.amount,
    this.approvedAmount,
    this.billPicture,
    this.fileExtension,
    this.longitude,
    this.latitude,
    this.employeeCode,
    this.expenseStatus,
    this.date,
    this.kilometers,
    required this.syncStatus,
  });

  factory Expense.fromMap(Map<String, dynamic> map) {
    return Expense(
      expenseType: map['em_expense_type'],
      expenseCode: map['em_code'] ?? '', 
      expenseDetail:
          map['em_expense_detail'] ?? '', 
      amount: (map['em_amount'] ?? 0)
          .toDouble(), 
      approvedAmount: map['em_approved_amount'] ?? 0,
      billPicture: map['em_bill_picture'] ?? '',
      fileExtension: map['file_extension'] ?? '',
      longitude: map['longitude'] ?? 0.0,
      latitude: map['latitude'] ?? 0.0,
      employeeCode: map['emp_code'] ?? '',
      expenseStatus: map['em_expense_status'] ?? 0,
      date: map['em_date'] ?? '',
      kilometers: map['em_km'] ?? '',
      syncStatus: map['em_sync_status'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'em_expense_type': expenseType,
      'em_code': expenseCode,
      'em_expense_detail': expenseDetail,
      'em_amount': amount,
      'em_approved_amount': approvedAmount,
      'em_bill_picture': billPicture,
      'file_extension': fileExtension,
      'longitude': longitude,
      'latitude': latitude,
      'emp_code': employeeCode,
      'em_expense_status': expenseStatus,
      'em_date': date,
      'em_km': kilometers,
      'em_sync_status': syncStatus,
    };
  }

  @override
  String toString() {
    return 'ExpenseModel{'
        'expenseType: $expenseType, '
        'expenseCode: $expenseCode, '
        'expenseDetail: $expenseDetail, '
        'amount: $amount, '
        'approvedAmount: $approvedAmount, '
        'billPicture: $billPicture, '
        'fileExtension: $fileExtension, '
        'longitude: $longitude, '
        'latitude: $latitude, '
        'employeeCode: $employeeCode, '
        'expenseStatus: $expenseStatus, '
        'date: $date, '
        'kilometers: $kilometers, '
        'syncStatus: $syncStatus}';
  }
}
