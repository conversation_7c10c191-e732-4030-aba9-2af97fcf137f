import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class CallData {
  String callCode;
  String? callClientCode;
  String? callEmpCode;
  double? callLat;
  double? callLong;
  double? callAccuracy;
  String? callPartyCode;
  int? callTotalQTY;
  double? callGrandTotal;
  int? callOrderReasonID;
  int? callOrderType;
  String? callRemark;
  double? callPackagingCharge;
  double? callTransportationCharge;
  String? callTransporterName;
  String? callStartTime;
  String? callStopTime;
  String? callOrderSign;
  String? callCreatedAt;
  int? callIsTelephonic;
  int? callSyncStatus;

  CallData({
    required this.callCode,
    this.callClientCode,
    this.callEmpCode,
    this.callLat,
    this.callLong,
    this.callAccuracy,
    this.callPartyCode,
    this.callTotalQTY,
    this.callGrandTotal,
    this.callOrderReasonID,
    this.callOrderType,
    this.callRemark,
    this.callPackagingCharge,
    this.callTransportationCharge,
    this.callTransporterName,
    this.callStartTime,
    this.callStopTime,
    this.callOrderSign,
    this.callCreatedAt,
    this.callIsTelephonic,
    this.callSyncStatus,
  });


  factory CallData.fromMap(Map<String, dynamic> map) {
    return CallData(
      callCode: map['${TableValues.callCode}'] ?? '',
      callClientCode: map['${TableValues.callClientCode}'] ?? '',
      callEmpCode: map['${TableValues.callEmpCode}'] ?? 0,
      callLat: map['${TableValues.callLat}'] ?? 0.0,
      callLong: map['${TableValues.callLong}'] ?? 0.0,
      callAccuracy: map['${TableValues.callAccuracy}'] ?? 0.0,
      callPartyCode: map['${TableValues.callPartyCode}'] ?? '',
      callTotalQTY: map['${TableValues.callTotalQTY}'] ?? 0,
      callGrandTotal: (map['${TableValues.callGrandTotal}'] ?? 0).toDouble(),
      callOrderReasonID: map['${TableValues.callOrderReasonID}'] ?? 0,
      callOrderType: map['${TableValues.callOrderType}'] ?? 0,
      callRemark: map['${TableValues.callRemark}'] ?? '',
      callPackagingCharge:
          (map['${TableValues.callPackagingCharge}'] ?? 0).toDouble(),
      callTransportationCharge:
          (map['${TableValues.callTransportationCharge}'] ?? 0).toDouble(),
      callTransporterName: map['${TableValues.callTransporterName}'] ?? '',
      callStartTime: map['${TableValues.callStartTime}'] ?? '',
      callStopTime: map['${TableValues.callStopTime}'] ?? '',
      callOrderSign: map['${TableValues.callOrderSign}'] ?? '',
      callCreatedAt: map['${TableValues.callCreatedAt}'] ?? '',
      callIsTelephonic: map['${TableValues.callIsTelephonic}'] ?? 0,
      callSyncStatus: map['${TableValues.callSyncStatus}'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.callCode}': callCode,
      '${TableValues.callClientCode}': callClientCode,
      '${TableValues.callEmpCode}': callEmpCode,
      '${TableValues.callLat}': callLat,
      '${TableValues.callLong}': callLong,
      '${TableValues.callAccuracy}': callAccuracy,
      '${TableValues.callPartyCode}': callPartyCode,
      '${TableValues.callTotalQTY}': callTotalQTY,
      '${TableValues.callGrandTotal}': callGrandTotal,
      '${TableValues.callOrderReasonID}': callOrderReasonID,
      '${TableValues.callOrderType}': callOrderType,
      '${TableValues.callRemark}': callRemark,
      '${TableValues.callPackagingCharge}': callPackagingCharge,
      '${TableValues.callTransportationCharge}': callTransportationCharge,
      '${TableValues.callTransporterName}': callTransporterName,
      '${TableValues.callStartTime}': callStartTime,
      '${TableValues.callStopTime}': callStopTime,
      '${TableValues.callOrderSign}': callOrderSign,
      '${TableValues.callCreatedAt}': callCreatedAt,
      '${TableValues.callIsTelephonic}': callIsTelephonic,
      '${TableValues.callSyncStatus}': callSyncStatus,
    };
  }

  @override
  String toString() {
    return 'CallData{'
        'callCode: $callCode, '
        'callClientCode: $callClientCode, '
        'callEmpCode: $callEmpCode, '
        'callLat: $callLat, '
        'callLong: $callLong, '
        'callAccuracy: $callAccuracy, '
        'callPartyCode: $callPartyCode, '
        'callTotalQTY: $callTotalQTY, '
        'callGrandTotal: $callGrandTotal, '
        'callOrderReasonID: $callOrderReasonID, '
        'callOrderType: $callOrderType, '
        'callRemark: $callRemark, '
        'callPackagingCharge: $callPackagingCharge, '
        'callTransportationCharge: $callTransportationCharge, '
        'callTransporterName: $callTransporterName, '
        'callStartTime: $callStartTime, '
        'callStopTime: $callStopTime, '
        'callOrderSign: $callOrderSign, '
        'callCreatedAt: $callCreatedAt, '
        'callIsTelephonic: $callIsTelephonic, '
        'callSyncStatus: $callSyncStatus'
        '}';
  }
}
