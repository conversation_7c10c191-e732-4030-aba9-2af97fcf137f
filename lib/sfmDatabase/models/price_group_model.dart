// product_ptdistributor

class PriceGroup {
  int id;
  String productCode;
  int marketType;
  double productPtss;
  double productPtdistributor;
  double productPtdealer;
  double productPtretailer;

  PriceGroup({
    required this.id,
    required this.productCode,
    required this.marketType,
    required this.productPtss,
    required this.productPtdistributor,
    required this.productPtdealer,
    required this.productPtretailer,
  });

  factory PriceGroup.fromMap(Map<String, dynamic> map) {
    return PriceGroup(
      id: map['id'] ?? -1,
      productCode: map['product_code'] ?? -1,
      marketType: map['market_type'] ?? -1,
      productPtss: double.tryParse(map['product_ptss'].toString()) ?? 0.0,
      productPtdistributor:
          double.tryParse(map['product_ptdistributor'].toString()) ?? 0.0,
      productPtdealer:
          double.tryParse(map['product_ptdealer'].toString()) ?? 0.0,
      productPtretailer:
          double.tryParse(map['product_ptretailer'].toString()) ?? 0.0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_code': productCode,
      'market_type': marketType,
      'product_ptss': productPtss,
      'product_ptdistributor': productPtdistributor,
      'product_ptdealer': productPtdealer,
      'product_ptretailer': productPtretailer,
    };
  }

  @override
  String toString() {
    return 'ProductPrice{id: $id, product_code: $productCode, market_type: $marketType, product_ptss: $productPtss, product_ptdistributor: $productPtdistributor, product_ptdealer: $productPtdealer, product_ptretailer: $productPtretailer}';
  }
}
