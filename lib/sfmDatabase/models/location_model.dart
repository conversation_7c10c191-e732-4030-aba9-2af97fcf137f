import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class Location {
  int? loc_timestamp;
  double? longitude;
  double? latitude;
  double? loc_accuracy;
  int? loc_sync_status;

  Location({
    this.loc_timestamp,
    this.longitude,
    this.latitude,
    this.loc_accuracy,
    this.loc_sync_status,
  });

  factory Location.fromMap(Map<String, dynamic> map) {
    return Location(
      loc_timestamp: map['${TableValues.locTimeStamp}'],
      longitude: map['${TableValues.locLongitude}'],
      latitude: map['${TableValues.locLatitude}'],
      loc_accuracy: map['${TableValues.locAccuracy}'],
      loc_sync_status: map['${TableValues.locSyncStatus}'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.locTimeStamp}': loc_timestamp,
      '${TableValues.locLongitude}': longitude,
      '${TableValues.locLatitude}': latitude,
      '${TableValues.locAccuracy}': loc_accuracy,
      '${TableValues.locSyncStatus}': loc_sync_status,
    };
  }
}
