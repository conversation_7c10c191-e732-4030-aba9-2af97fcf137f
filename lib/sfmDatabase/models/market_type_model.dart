import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class MarketType {
  int mtm_id;
  String? mtm_type;

  MarketType({
    required this.mtm_id,
    this.mtm_type,
  });

  factory MarketType.fromMap(Map<String, dynamic> map) {
    return MarketType(
      mtm_id: map['${TableValues.marketID}'] ?? 0,
      mtm_type: map['${TableValues.marketType}'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.marketID}': mtm_id,
      '${TableValues.marketType}': mtm_type,
    };
  }
}
