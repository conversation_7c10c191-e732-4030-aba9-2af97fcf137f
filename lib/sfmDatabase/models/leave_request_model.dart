class Leave {
  final String? code;
  final int? typeId;
  final String? fromDate;
  final String? toDate;
  final int? totalDays;
  final int? approvedStatus;
  final String? reason;
  final String? employeeCode;
  final int? notificationStatus;
  final int? approvedBy;
  final String? createdAt;
  final int? syncStatus;

  Leave({
    this.code,
    this.typeId,
    this.fromDate,
    this.toDate,
    this.totalDays,
    this.approvedStatus,
    this.reason,
    this.employeeCode,
    this.notificationStatus,
    this.approvedBy,
    this.createdAt,
    this.syncStatus,
  });

  factory Leave.fromMap(Map<String, dynamic> map) {
    return Leave(
      code: map['la_code'] ?? '',
      typeId: map['la_type_id'] ?? 0,
      fromDate: map['la_from_date'] ?? '',
      toDate: map['la_to_date'] ?? '',
      totalDays: map['la_total_day'] ?? 0,
      approvedStatus: map['la_approved_status'] ?? 0,
      reason: map['la_reason'] ?? '',
      employeeCode: map['la_emp_code'] ?? 0,
      notificationStatus: map['la_notification_status'] ?? 0,
      approvedBy: map['la_approved_by'] ?? 0,
      createdAt: map['created_at'] ?? '',
      syncStatus: map['leave_sync_status'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'la_code': code,
      'la_type_id': typeId,
      'la_from_date': fromDate,
      'la_to_date': toDate,
      'la_total_day': totalDays,
      'la_approved_status': approvedStatus,
      'la_reason': reason,
      'la_emp_code': employeeCode,
      'la_notification_status': notificationStatus,
      'la_approved_by': approvedBy,
      'created_at': createdAt,
      'leave_sync_status': syncStatus,
    };
  }

  @override
  String toString() {
    return 'Leave{code: $code, typeId: $typeId, fromDate: $fromDate, toDate: $toDate, '
        'totalDays: $totalDays, approvedStatus: $approvedStatus, reason: $reason, '
        'employeeCode: $employeeCode, notificationStatus: $notificationStatus, '
        'approvedBy: $approvedBy, syncStatus: $syncStatus}';
  }
}
