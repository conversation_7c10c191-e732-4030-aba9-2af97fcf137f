import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class StateModel {
  int state_id;
  String? state_name;
  String? state_zone;

  StateModel({
    required this.state_id,
    this.state_name,
    this.state_zone,
  });

  factory StateModel.fromJson(Map<String, dynamic> json) {
    return StateModel(
      state_id: json['${TableValues.stateID}'],
      state_name: json['${TableValues.stateName}'],
      state_zone: json['${TableValues.stateZone}'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '${TableValues.stateID}': state_id,
      '${TableValues.stateName}': state_name,
      '${TableValues.stateZone}': state_zone,
    };
  }

  @override
  String toString() {
    return 'State{state_id: $state_id, state_name: $state_name, state_zone: $state_zone}';
  }
}
