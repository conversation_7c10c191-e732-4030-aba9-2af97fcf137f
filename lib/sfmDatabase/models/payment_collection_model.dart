class Payment {
  String? code;
  String? cmCode;
  int? type;
  String? chequeDate;
  double? amount;
  int? bankId;
  String? chequeNo;
  String? note;
  String? empCode;
  String? accountNo;
  String? chequePhoto;
  int? conditionId;
  int? status;
  String? createdAt;
  String? updatedAt;
  final int syncStatus;


  Payment({
    this.code,
    this.cmCode,
    this.type,
    this.chequeDate,
    this.amount,
    this.bankId,
    this.chequeNo,
    this.note,
    this.empCode,
    this.accountNo,
    this.chequePhoto,
    this.conditionId,
    this.status,
    this.createdAt,
    this.updatedAt,
    required this.syncStatus,
  });

  factory Payment.fromMap(Map<String, dynamic> map) {
    return Payment(
      code: map['pc_code'] ?? '',
      cmCode: map['pc_cm_code'] ?? '',
      type: map['pc_type'] ?? 0,
      chequeDate: map['pc_cheque_date'] ?? '',
      amount: (map['pc_amount'] ?? 0).toDouble(),
      bankId: map['pc_bank_id'],
      chequeNo: map['pc_cheque_no'] ?? '',
      note: map['pc_note'] ?? '',
      empCode: map['pc_emp_code'] ?? 0,
      accountNo: map['pc_account_no'] ?? '',
      chequePhoto: map['pc_cheque_photo'] ?? '',
      conditionId: map['pc_condition_id'] ?? 0,
      status: map['pc_status'] ?? 0,
      createdAt: map['created_at'] ?? '',
      updatedAt: map['updated_at'] ?? '',
      syncStatus: map['pc_sync_status'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'pc_code': code,
      'pc_cm_code': cmCode,
      'pc_type': type,
      'pc_cheque_date': chequeDate,
      'pc_amount': amount,
      'pc_bank_id': bankId,
      'pc_cheque_no': chequeNo,
      'pc_note': note,
      'pc_emp_code': empCode,
      'pc_account_no': accountNo,
      'pc_cheque_photo': chequePhoto,
      'pc_condition_id': conditionId,
      'pc_status': status,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'pc_sync_status': syncStatus,
    };
  }

  @override
  String toString() {
    return 'Payment{code: $code, cmCode: $cmCode, type: $type, '
        'chequeDate: $chequeDate, amount: $amount, bankId: $bankId, '
        'chequeNo: $chequeNo, note: $note, empCode: $empCode, '
        'accountNo: $accountNo, chequePhoto: $chequePhoto, '
        'conditionId: $conditionId, status: $status, '
        'createdAt: $createdAt, updatedAt: $updatedAt, syncStatus: $syncStatus}';
  }
}
