import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class Config {
  int config_id;
  String? config_key;
  String? config_value;
  String? config_type;

  Config({
    required this.config_id,
    this.config_key,
    this.config_value,
    this.config_type,
  });

  factory Config.fromJson(Map<String, dynamic> map) {
    return Config(
      config_id: map['${TableValues.configID}'],
      config_key: map['${TableValues.configKey}'],
      config_value: map['${TableValues.configValue}'],
      config_type: map['${TableValues.configType}'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '${TableValues.configID}': config_id,
      '${TableValues.configKey}': config_key,
      '${TableValues.configValue}': config_value,
      '${TableValues.configType}': config_type,
    };
  }

  @override
  String toString() {
    return 'Config{config_id: $config_id, config_key: $config_key, config_value: $config_value, config_type: $config_type}';
  }
}
