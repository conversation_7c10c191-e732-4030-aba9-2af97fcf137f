
class Product {
  final String? productCode;
  final int? pmPtId;
  final int? productBrand;
  final String? productName;
  final int? uom;
  final double? unitSize;
  final int? innerCaseSize;
  final int? outerCaseSize;
  final double? productMrp;
  final double? productGst;
  final String? productHsnSacCode;
  final String? productImage;
  final int? productStatus;
  final String? barcode;
  final int? marketType;
  final double? productPtss;
  final double? productPtdistributor;
  final double? productPtdealer;
  final double? productPtretailer;



  Product({
    this.productCode,
    this.pmPtId,
    this.productBrand,
    this.productName,
    this.uom,
    this.unitSize,
    this.innerCaseSize,
    this.outerCaseSize,
    this.productMrp,
    this.productGst,
    this.productHsnSacCode,
    this.productImage,
    this.productStatus,
    this.barcode,
    this.marketType,
    this.productPtss,
    this.productPtdistributor,
    this.productPtdealer,
    this.productPtretailer,
  });

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      productCode: map['product_code'],
      pmPtId: map['pm_pt_id'],
      productBrand: map['product_brand'],
      productName: map['product_name'],
      uom: map['uom'],
      unitSize: map['unit_size']?.toDouble(), 
      innerCaseSize: map['inner_case_size'],
      outerCaseSize: map['outer_case_size'],
      productMrp: map['product_mrp']?.toDouble(), 
      productGst: map['product_gst']?.toDouble(), 
      productHsnSacCode: map['product_hsn_sac_code'],
      productImage: map['product_image'] ?? '',
      productStatus: map['product_status'],
      barcode: map['barcode'],
      marketType: map['market_type'],
      productPtss: map['product_ptss']?.toDouble(),
      productPtdistributor: map['product_ptdistributor']?.toDouble(),
      productPtdealer: map['product_ptdealer']?.toDouble(),
      productPtretailer: map['product_ptretailer']?.toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'product_code': productCode,
      'pm_pt_id': pmPtId,
      'product_brand': productBrand,
      'product_name': productName,
      'uom': uom,
      'unit_size': unitSize,
      'inner_case_size': innerCaseSize,
      'outer_case_size': outerCaseSize,
      'product_mrp': productMrp,
      'product_gst': productGst,
      'product_hsn_sac_code': productHsnSacCode,
      'product_image': productImage,
      'product_status': productStatus,
      'barcode': barcode,
      'market_type': marketType,
      'product_ptss': productPtss,
      'product_ptdistributor': productPtdistributor,
      'product_ptdealer': productPtdealer,
      'product_ptretailer': productPtretailer,
    };
  }

  @override
  String toString() {
    return 'Product{productCode: $productCode, '
        'pmPtId: $pmPtId, '
        'productBrand: $productBrand, '
        'productName: $productName, '
        'uom: $uom, '
        'unitSize: $unitSize, '
        'innerCaseSize: $innerCaseSize, '
        'outerCaseSize: $outerCaseSize, '
        'productMrp: $productMrp, '
        'productGst: $productGst, '
        'productHsnSacCode: $productHsnSacCode, '
        'productImage: $productImage, '
        'productStatus: $productStatus, '
        'barcode: $barcode, '
        'marketType: $marketType, '
        'productPtss: $productPtss, '
        'productPtdistributor: $productPtdistributor, '
        'productPtdealer: $productPtdealer, '
        'productPtretailer: $productPtretailer}';
  }
}
