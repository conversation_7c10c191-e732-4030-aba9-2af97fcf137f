import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class Bank {
  int bank_id;
  String? bank_name;

  Bank({
    required this.bank_id,
    this.bank_name,
  });

  factory Bank.fromMap(Map<String, dynamic> map) {
    return Bank(
      bank_id: map['${TableValues.bankID}'] ?? 0,
      bank_name: map['${TableValues.bankName}'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.bankID}': bank_id,
      '${TableValues.bankName}': bank_name,
    };
  }

  @override
  String toString() {
    return 'Bank{bank_id: $bank_id, bank_name: $bank_name}';
  }
}
