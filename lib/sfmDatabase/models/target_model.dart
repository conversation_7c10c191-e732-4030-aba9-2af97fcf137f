
class Target {
  final String? empCode;
  final String? empName;
  final String? designation;
  final String? startDate;
  final String? endDate;
  final String? type;
  final String? primaryTarget;
  final String? primaryAchievement;
  final String? primaryPercent;
  final String? secondaryTarget;
  final String? secondaryAchievement;
  final String? secondaryPercent;

  Target({
    this.empCode,
    this.empName,
    this.designation,
    this.startDate,
    this.endDate,
    this.type,
    this.primaryTarget,
    this.primaryAchievement,
    this.primaryPercent,
    this.secondaryTarget,
    this.secondaryAchievement,
    this.secondaryPercent,
  });

  factory Target.fromJson(Map<String, dynamic> map) {
    return Target(
      empCode: map['employee_code'] ?? '',
      empName: map['employee_name'] ?? '',
      designation: map['designation'] ?? '',
      startDate: map['start_date'] ?? '',
      endDate: map['end_date'] ?? '',
      type: map['type'] ?? '',
      primaryTarget: map['primary_target'] ?? '',
      primaryAchievement: map['primary_achievement'] ?? '',
      primaryPercent: map['primary_percent'] ?? '',
      secondaryTarget: map['secondary_target'] ?? '',
      secondaryAchievement: map['secondary_achievement'] ?? '',
      secondaryPercent: map['secondary_percent'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'employee_code': empCode,
      'employee_name': empName,
      'designation': designation,
      'start_date': startDate,
      'end_date': endDate,
      'type': type,
      'primary_target': primaryTarget,
      'primary_achievement': primaryAchievement,
      'primary_percent': primaryPercent,
      'secondary_target': secondaryTarget,
      'secondary_achievement': secondaryAchievement,
      'secondary_percent': secondaryPercent,
    };
  }

  @override
  String toString() {
    return 'Target{empCode: $empCode, empName: $empName, designation: $designation, startDate: $startDate, '
        'endDate: $endDate, type: $type, primaryTarget: $primaryTarget, '
        'primaryAchievement: $primaryAchievement, primaryPercent: $primaryPercent, '
        'secondaryTarget: $secondaryTarget, secondaryAchievement: $secondaryAchievement, ' 
        'secondaryPercent: $secondaryPercent}';
  }
}
