import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class UOM {
  int uom_id;
  String? uom_name;

  UOM({
    required this.uom_id,
    this.uom_name,
  });

  factory UOM.fromMap(Map<String, dynamic> map) {
    return UOM(
      uom_id: map['${TableValues.uomID}'] ?? 1,
      uom_name: map['${TableValues.uomName}'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.uomID}': uom_id,
      '${TableValues.uomName}': uom_name,
    };
  }

  @override
  String toString() {
    return 'Uom { ${TableValues.uomID}: $uom_id, ${TableValues.uomName}: $uom_name }';
  }
}
