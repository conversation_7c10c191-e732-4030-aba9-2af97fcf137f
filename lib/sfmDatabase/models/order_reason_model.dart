import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class OrderReason {
  int reason_id;
  String? reason_name;

  OrderReason({
    required this.reason_id,
    this.reason_name,
  });

  factory OrderReason.fromMap(Map<String, dynamic> map) {
    return OrderReason(
      reason_id: map['${TableValues.reasonID}'] ?? 0,
      reason_name: map['${TableValues.reasonName}'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.reasonID}': reason_id,
      '${TableValues.reasonName}': reason_name,
    };
  }
}
