import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class Customer {
  String? cmCode;
  String? cmName;
  int? cmMobile;
  int? cmMobile2;
  String? cmEmail;
  String? cmAddress;
  String? cmPincode;
  String? cmGST;
  String? cmPan;
  double? cmLat;
  double? cmLong;
  int? cmMarketType;
  int? cmStatus;
  int? cmTownID;
  double? cmOutstandingAmount;
  String? cmContactPerson;
  String? cmArea;
  String? cmRelationCode;
  int? cmType;
  String? cmCreatedAt;
  String? cmAnniversary;
  String? cmDOB;
  String? cmImageRelation;
  String? cmBeatRelationCode;
  int? cmCategoryRelationID;
  int? cmGradeRelationID;
  int? syncStatus;

  Customer({
    this.cmCode,
    this.cmName,
    this.cmMobile,
    this.cmMobile2,
    this.cmEmail,
    this.cmAddress,
    this.cmPincode,
    this.cmGST,
    this.cmPan,
    this.cmLat,
    this.cmLong,
    this.cmMarketType,
    this.cmStatus,
    this.cmTownID,
    this.cmOutstandingAmount,
    this.cmContactPerson,
    this.cmArea,
    this.cmRelationCode,
    this.cmType,
    this.cmCreatedAt,
    this.cmAnniversary,
    this.cmDOB,
    this.cmImageRelation,
    this.cmBeatRelationCode,
    this.cmCategoryRelationID,
    this.cmGradeRelationID,
    this.syncStatus,
  });

 

  factory Customer.fromMap(Map<String, dynamic> map) {
    return Customer(
      cmCode: map['${TableValues.customerCode}'] ?? "",
      cmName: map['${TableValues.customerName}'] ?? "",
      cmMobile: map['${TableValues.customerMobile}'] ?? 0,
      cmMobile2: map['${TableValues.customerMobile2}'] ?? 0,
      cmEmail: map['${TableValues.customerEmail}'] ?? "",
      cmAddress: map['${TableValues.customerAddress}'] ?? "",
      cmPincode: map['${TableValues.customerPincode}'] ?? "",
      cmGST: map['${TableValues.customerGST}'] ?? "",
      cmPan: map['${TableValues.customerPAN}'] ?? "",
      cmLat: map['${TableValues.customerLatitude}'] ?? 0.0,
      cmLong: map['${TableValues.customerLongitude}'] ?? 0.0,
      cmMarketType: map['${TableValues.customerMarketType}'] ?? 0,
      cmStatus: map['${TableValues.customerStatus}'] ?? 0,
      cmTownID: map['${TableValues.customerTownID}'] ?? 0,


      cmOutstandingAmount:
          (map['${TableValues.customerOutstandingAmount}'] ?? 0).toDouble(),
      cmContactPerson: map['${TableValues.customerContactPerson}'] ?? "",
      cmArea: map['${TableValues.customerArea}'] ?? "",
      cmRelationCode: map['${TableValues.customerRelationCode}'] ?? "",
      cmType: map['${TableValues.customerType}'] ?? 0,
      cmCreatedAt: map['${TableValues.customerCreatedAt}'] ?? "",
      cmAnniversary: map['${TableValues.customerAnniversary}'] ?? "",
      cmDOB: map['${TableValues.customerDOB}'] ?? "",
      cmImageRelation: map['${TableValues.customerImageRelation}'] ?? "",
      cmBeatRelationCode: map['${TableValues.customerBeatRelationCode}'] ?? "",
      cmCategoryRelationID:
          map['${TableValues.customerCategoryRelationID}'] ?? 0,
      cmGradeRelationID: map['${TableValues.customerGradeRelationID}'] ?? 0,
      syncStatus: map['${TableValues.customerSyncStatus}'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.customerCode}': cmCode,
      '${TableValues.customerName}': cmName,
      '${TableValues.customerMobile}': cmMobile,
      '${TableValues.customerMobile2}': cmMobile2,
      '${TableValues.customerEmail}': cmEmail,
      '${TableValues.customerAddress}': cmAddress,
      '${TableValues.customerPincode}': cmPincode,
      '${TableValues.customerGST}': cmGST,
      '${TableValues.customerPAN}': cmPan,
      '${TableValues.customerLatitude}': cmLat,
      '${TableValues.customerLongitude}': cmLong,
      '${TableValues.customerMarketType}': cmMarketType,
      '${TableValues.customerStatus}': cmStatus,
      '${TableValues.customerTownID}': cmTownID,
      '${TableValues.customerOutstandingAmount}': cmOutstandingAmount,
      '${TableValues.customerContactPerson}': cmContactPerson,
      '${TableValues.customerArea}': cmArea,
      '${TableValues.customerRelationCode}': cmRelationCode,
      '${TableValues.customerType}': cmType,
      '${TableValues.customerCreatedAt}': cmCreatedAt,
      '${TableValues.customerAnniversary}': cmAnniversary,
      '${TableValues.customerDOB}': cmDOB,
      '${TableValues.customerImageRelation}': cmImageRelation,
      '${TableValues.customerBeatRelationCode}': cmBeatRelationCode,
      '${TableValues.customerCategoryRelationID}': cmCategoryRelationID,
      '${TableValues.customerGradeRelationID}': cmGradeRelationID,
      '${TableValues.customerSyncStatus}': syncStatus,
    };
  }

  @override
  String toString() {
    return 'Customer(cmCode: $cmCode, cmName: $cmName, cmMobile: $cmMobile, cmMobile2: $cmMobile2, cmEmail: $cmEmail, cmAddress: $cmAddress, cmPincode: $cmPincode, cmGST: $cmGST, cmPan: $cmPan, cmLat: $cmLat, cmLong: $cmLong, cmMarketType: $cmMarketType, cmStatus: $cmStatus, cmTownID: $cmTownID, cmOutstandingAmount: $cmOutstandingAmount, cmContactPerson: $cmContactPerson, cmArea: $cmArea, cmRelationCode: $cmRelationCode, cmType: $cmType, cmCreatedAt: $cmCreatedAt, cmAnniversary: $cmAnniversary,  cmDOB: $cmDOB, cmImageRelation: $cmImageRelation, cmBeatRelationCode: $cmBeatRelationCode, cmCategoryRelationID: $cmCategoryRelationID, cmGradeRelationID: $cmGradeRelationID)';
  }
}

class CustomerModel {
  final String code;
  final String name;

  CustomerModel({required this.code, required this.name});
}

class MarketType {
  int mtmId;
  String mtmType;

  MarketType({
    required this.mtmId,
    required this.mtmType,
  });

  Map<String, dynamic> toMap() {
    return {
      'mtm_id': mtmId,
      'mtm_type': mtmType,
    };
  }

  factory MarketType.fromJson(Map<String, dynamic> map) {
    return MarketType(
      mtmId: map['mtm_id'],
      mtmType: map['mtm_type'],
    );
  }
}

class BeatRelation {
  int cmCode;
  int beatCode;
  BeatRoute beat;

  BeatRelation({
    required this.cmCode,
    required this.beatCode,
    required this.beat,
  });

  Map<String, dynamic> toMap() {
    return {
      'cm_code': cmCode,
      'beat_code': beatCode,
      'beat': beat.toMap(),
    };
  }

  factory BeatRelation.fromJson(Map<String, dynamic> map) {
    return BeatRelation(
      cmCode: map['cm_code'],
      beatCode: map['beat_code'],
      beat: BeatRoute.fromJson(map['beat']),
    );
  }
}

class BeatRoute {
  String beatCode;
  int townId;
  String beatName;

  BeatRoute({
    required this.beatCode,
    required this.townId,
    required this.beatName,
  });

  Map<String, dynamic> toMap() {
    return {
      'beat_code': beatCode,
      'town_id': townId,
      'beat_name': beatName,
    };
  }

  factory BeatRoute.fromJson(Map<String, dynamic> map) {
    return BeatRoute(
      beatCode: map['beat_code'],
      townId: map['town_id'],
      beatName: map['beat_name'],
    );
  }
}
