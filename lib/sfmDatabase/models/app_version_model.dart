import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class AppVersion {
  String? sync_version;

  AppVersion({
    this.sync_version,
  });

  factory AppVersion.fromMap(Map<String, dynamic> map) {
    return AppVersion(
      sync_version: map['${TableValues.syncVersion}'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.syncVersion}': sync_version,
    };
  }
}
