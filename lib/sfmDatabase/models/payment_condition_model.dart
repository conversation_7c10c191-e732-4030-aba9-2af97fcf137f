import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class PaymentCondition {
  int pc_id;
  String? pc_name;

  PaymentCondition({
    required this.pc_id,
    this.pc_name,
  });

  factory PaymentCondition.fromMap(Map<String, dynamic> map) {
    return PaymentCondition(
      pc_id: map['${TableValues.pcID}'] ?? 0,
      pc_name: map['${TableValues.pcName}'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.pcID}': pc_id,
      '${TableValues.pcName}': pc_name,
    };
  }
}
