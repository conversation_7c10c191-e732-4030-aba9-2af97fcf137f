import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class Town {
  int town_id;
  String? town_name;
  int? district_id;

  Town({
    required this.town_id,
    this.town_name,
    this.district_id,
  });

  factory Town.fromJson(Map<String, dynamic> map) {
    return Town(
      town_id: map['${TableValues.townID}'],
      town_name: map['${TableValues.townName}'],
      district_id: map['${TableValues.districtID}'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.townID}': town_id,
      '${TableValues.townName}': town_name,
      '${TableValues.districtID}': district_id,
    };
  }

  @override
  String toString() {
    return 'Town{town_id: $town_id, town_name: $town_name, district_id: $district_id}';
  }
}
