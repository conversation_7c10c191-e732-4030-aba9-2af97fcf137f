class ClaimComplain {
  final String? ccCode;
  final int? ccTypeID;
  final String? ccNote;
  final String? ccImage;
  final String? ccCMCode;
  final String? ccEmpCode;
  final int? ccType;
  final int? ccStatus;
  final String? ccRemarks;
  final String? ccCreatedAt;
  final String? ccUpdatedAt;
  final int syncStatus;

  ClaimComplain({
    this.ccCode,
    this.ccTypeID,
    this.ccNote,
    this.ccImage,
    this.ccCMCode,
    this.ccEmpCode,
    this.ccType,
    this.ccStatus,
    this.ccRemarks,
    this.ccCreatedAt,
    this.ccUpdatedAt,
    required this.syncStatus,
  });

  factory ClaimComplain.fromMap(Map<String, dynamic> map) {
    return ClaimComplain(
      ccCode: map['ccm_code'] ?? '',
      ccTypeID: map['ccm_type_id'] ?? 0,
      ccNote: map['ccm_note'] ?? '',
      ccImage: map['ccm_image'] ?? '',
      ccCMCode: map['cm_code'] ?? '',
      ccEmpCode: map['emp_code'] ?? '',
      ccType: map['ccm_cc_type'] ?? 0,
      ccStatus: map['ccm_status'] ?? 0,
      ccRemarks: map['ccm_remarks'] ?? '',
      ccCreatedAt: map['created_at'] ?? '',
      ccUpdatedAt: map['updated_at'] ?? '',
      syncStatus: map['ccm_sync_status'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'ccm_code': ccCode ?? '',
      'ccm_type_id': ccTypeID ?? 0,
      'ccm_note': ccNote ?? '',
      'ccm_image': ccImage ?? '',
      'cm_code': ccCMCode ?? 0,
      'emp_code': ccEmpCode ?? 0,
      'ccm_cc_type': ccType ?? 0,
      'ccm_status': ccStatus ?? 0,
      'ccm_remarks': ccRemarks ?? '',
      'created_at': ccCreatedAt ?? '',
      'updated_at': ccUpdatedAt ?? '',
      'ccm_sync_status': syncStatus,
    };
  }

  @override
  String toString() {
    return 'ClaimComplain{ccCode: $ccCode, '
        'ccTypeID: $ccTypeID, '
        'ccNote: $ccNote, '
        'ccImage: $ccImage, '
        'ccCMCode: $ccCMCode, '
        'ccEmpCode: $ccEmpCode, '
        'ccType: $ccType, '
        'ccStatus: $ccStatus, '
        'ccRemarks: $ccRemarks, '
        'ccCreatedAt: $ccCreatedAt, '
        'ccUpdatedAt: $ccUpdatedAt, '
        'ccm_sync_status: $syncStatus}';
  }
}
