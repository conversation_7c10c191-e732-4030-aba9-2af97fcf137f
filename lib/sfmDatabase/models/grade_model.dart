import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class Grade {
  int gm_id;
  String? gm_name;

  Grade({
    required this.gm_id,
    this.gm_name,
  });

  factory Grade.fromJson(Map<String, dynamic> map) {
    return Grade(
      gm_id: map['${TableValues.gmID}'],
      gm_name: map['${TableValues.gmName}'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '${TableValues.gmID}': gm_id,
      '${TableValues.gmName}': gm_name,
    };
  }

  @override
  String toString() {
    return 'Grade{gm_id: $gm_id, gm_name: $gm_name}';
  }
}
