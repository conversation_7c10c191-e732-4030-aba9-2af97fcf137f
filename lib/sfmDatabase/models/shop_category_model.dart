import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class ShopCategory {
  int category_id;
  String? category_name;

  ShopCategory({
    required this.category_id,
    this.category_name,
  });

  factory ShopCategory.fromMap(Map<String, dynamic> map) {
    return ShopCategory(
      category_id: map['${TableValues.categoryID}'] ?? 0,
      category_name: map['${TableValues.categoryName}'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.categoryID}': category_id,
      '${TableValues.categoryName}': category_name,
    };
  }
}
