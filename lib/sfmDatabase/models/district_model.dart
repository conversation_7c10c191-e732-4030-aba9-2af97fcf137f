import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class District {
  int dm_id;
  String? dm_name;
  int? state_id;

  District({
    required this.dm_id,
    this.dm_name,
    this.state_id,
  });

  factory District.fromJson(Map<String, dynamic> map) {
    return District(
      dm_id: map['${TableValues.dmID}'],
      dm_name: map['${TableValues.dmName}'],
      state_id: map['${TableValues.stateID}'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.dmID}': dm_id,
      '${TableValues.dmName}': dm_name,
      '${TableValues.stateID}': state_id,
    };
  }

  @override
  String toString() {
    return 'District{dm_id: $dm_id, dm_name: $dm_name, state_id: $state_id}';
  }
}
