import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class ProductType {
  int type_id;
  String? type_name;
  int? brand_id;

  ProductType({
    required this.type_id,
    this.type_name,
    this.brand_id,
  });

  factory ProductType.fromMap(Map<String, dynamic> map) {
    return ProductType(
      type_id: map['${TableValues.productTypeID}'] ?? 0,
      type_name: map['${TableValues.productTypeName}'] ?? '',
      brand_id: map['${TableValues.brandID}'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.productTypeID}': type_id,
      '${TableValues.productTypeName}': type_name,
      '${TableValues.brandID}': brand_id,
    };
  }

  @override
  String toString() {
    return 'ProductType{type_id: $type_id, type_name: $type_name, brand_id: $brand_id}';
  }
}
