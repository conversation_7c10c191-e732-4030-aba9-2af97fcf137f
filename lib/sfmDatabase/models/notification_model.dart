
class NotificationData {
  int enId;
  int enType;
  String? enImage;
  String enTitle;
  String enMessage;
  String empCode;
  String createdAt;
  String updatedAt;

  NotificationData({
    required this.enId,
    required this.enType,
    this.enImage,
    required this.enTitle,
    required this.enMessage,
    required this.empCode,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NotificationData.fromJson(Map<String, dynamic> json) {
    return NotificationData(
      enId: json['en_id'],
      enType: json['en_type'],
      enImage: json['en_image'],
      enTitle: json['en_title'],
      enMessage: json['en_message'],
      empCode: json['emp_code'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'en_id': enId,
      'en_type': enType,
      'en_image': enImage,
      'en_title': enTitle,
      'en_message': enMessage,
      'emp_code': empCode,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  @override
  String toString() {
    return 'NotificationData{enId: $enId, enType: $enType, enImage: $enImage, enTitle: $enTitle, enMessage: $enMessage, empCode: $empCode, createdAt: $createdAt, updatedAt: $updatedAt}';
  }
}
