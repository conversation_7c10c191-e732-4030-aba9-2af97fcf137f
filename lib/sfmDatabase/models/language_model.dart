import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class Language {
  String lang_id;
  String? lang_locale;
  String? lang_key;
  String? lang_value;

  Language({
    required this.lang_id,
    this.lang_locale,
    this.lang_key,
    this.lang_value,
  });

  factory Language.fromMap(Map<String, dynamic> map) {
    return Language(
      lang_id: map['${TableValues.langCode}'] ?? 0,
      lang_locale: map['${TableValues.langName}'] ?? '',
      lang_key: map['${TableValues.langTranslateKey}'] ?? '',
      lang_value: map['${TableValues.langTranslateValue}'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.langCode}': lang_id,
      '${TableValues.langName}': lang_locale,
      '${TableValues.langTranslateKey}': lang_key,
      '${TableValues.langTranslateValue}': lang_value,
    };
  }
}
