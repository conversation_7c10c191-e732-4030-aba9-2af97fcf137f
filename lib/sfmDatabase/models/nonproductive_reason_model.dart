class NonProductiveReason {
  final int nprID;
  final String? nprReason;

  NonProductiveReason({
    required this.nprID,
    this.nprReason,
  });

  factory NonProductiveReason.fromMap(Map<String, dynamic> map) {
    return NonProductiveReason(
      nprID: map['npr_id'] ?? -1,
      nprReason: map['npr_reason'] ?? '',
    );
  }

  @override
  String toString() {
    return 'NonProductiveReason{id: $nprID, PaymentTypeName: $nprReason}';
  }
}