import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class Beat {
  String? beat_code;
  String? beat_name;
  int? beat_status;
  String? bdm_date;
  int? bdm_numberwise;
  int? bdm_type;
  String? bdm_day;
  int? bdm_period;
  int? beat_sync_status;

  Beat({
    this.beat_code,
    this.beat_name,
    this.beat_status,
    this.bdm_date,
    this.bdm_numberwise,
    this.bdm_type,
    this.bdm_day,
    this.bdm_period,
    this.beat_sync_status,
  });

  factory Beat.fromMap(Map<String, dynamic> map) {
    return Beat(
      beat_code: map['${TableValues.beatCode}'] ?? '',
      beat_name: map['${TableValues.beatName}'] ?? '',
      beat_status: map['${TableValues.beatStatus}'] ?? 0,
      bdm_date: map['${TableValues.beatBDMDate}'] ?? '',
      bdm_numberwise: map['${TableValues.beatBDMNumberwise}'] ?? 0,
      bdm_type: map['${TableValues.beatBDMType}'] ?? 0,
      bdm_day: map['${TableValues.beatBDMDay}'] ?? '',
      bdm_period: map['${TableValues.beatBDMPeriod}'] ?? 0,
      beat_sync_status: map['${TableValues.beatSyncStatus}'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.beatCode}': beat_code,
      '${TableValues.beatName}': beat_name,
      '${TableValues.beatStatus}': beat_status,
      '${TableValues.beatBDMDate}': bdm_date,
      '${TableValues.beatBDMNumberwise}': bdm_numberwise,
      '${TableValues.beatBDMType}': bdm_type,
      '${TableValues.beatBDMDay}': bdm_day,
      '${TableValues.beatBDMPeriod}': bdm_period,
      '${TableValues.beatSyncStatus}': beat_sync_status,
    };
  }

  @override
  String toString() {
    return 'Beat{beat_code: $beat_code, beat_name: $beat_name, bdm_date: $bdm_date, bdm_numberwise: $bdm_numberwise, bdm_type: $bdm_type, bdm_day: $bdm_day, bdm_period: $bdm_period, beat_sync_status: $beat_sync_status}';
  }
}
