import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class ClaimComplainType {
  int cct_id;
  String? cct_type_name;
  int cct_type_status;

  ClaimComplainType({
    required this.cct_id,
    this.cct_type_name,
    required this.cct_type_status,
  });

  factory ClaimComplainType.fromMap(Map<String, dynamic> map) {
    return ClaimComplainType(
      cct_id: map['${TableValues.claimComplainID}'] ?? 0,
      cct_type_name: map['${TableValues.claimComplainTypeName}'] ?? '',
      cct_type_status: map['${TableValues.claimComplainType}'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.claimComplainID}': cct_id,
      '${TableValues.claimComplainTypeName}': cct_type_name,
      '${TableValues.claimComplainType}': cct_type_status,
    };
  }

  @override
  String toString() {
    return 'ClaimComplainType{cct_id: $cct_id, cct_type_name: $cct_type_name, cct_type_status: $cct_type_status}';
  }
}
