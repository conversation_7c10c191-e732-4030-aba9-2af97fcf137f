import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class ProductBrand {
  int brand_id;
  String? brand_name;

  ProductBrand({
    required this.brand_id,
    this.brand_name,
  });

  factory ProductBrand.fromMap(Map<String, dynamic> map) {
    return ProductBrand(
      brand_id: map['${TableValues.brandID}'] ?? 0,
      brand_name: map['${TableValues.brandName}'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.brandID}': brand_id,
      '${TableValues.brandName}': brand_name,
    };
  }

  @override
  String toString() {
    return 'ProductBrand{${TableValues.brandID}: $brand_id, ${TableValues.brandName}: $brand_name}';
  }
}
