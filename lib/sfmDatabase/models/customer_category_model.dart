import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class CustomerCategory {
  int ccmID;
  String? ccmName;

  CustomerCategory({
    required this.ccmID,
    this.ccmName,
  });

  factory CustomerCategory.fromJson(Map<String, dynamic> map) {
    return CustomerCategory(
      ccmID: map['${TableValues.ccmID}'],
      ccmName: map['${TableValues.ccmName}'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.ccmID}': ccmID,
      '${TableValues.ccmName}': ccmName,
    };
  }

  @override
  String toString() {
    return 'ClaimComplainType{ccmID: $ccmID, ccmName: $ccmName}';
  }
}
