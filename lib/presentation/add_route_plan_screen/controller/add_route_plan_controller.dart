// ignore_for_file: unnecessary_null_comparison, unused_local_variable,

import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';

import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/presentation/add_route_plan_screen/models/add_route_plan_model.dart';
import 'package:sfm_new/presentation/route_plan_list_screen/controller/route_plan_list_controller.dart';

import 'dart:async';
import 'package:sfm_new/sfmDatabase/models/config_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

class Pair {
  final String text;
  final IconData icon;
  const Pair(this.text, this.icon);

  @override
  String toString() {
    return text;
  }
}

class AddRoutePlanController extends GetxController {
  SelectionPopupModel? selectedDropDownValue;
  SelectionPopupModel? selectedDropDownValue1;

  RxList<Customer> customerList = <Customer>[].obs;
  RxList<Customer> selectedCustomerList = <Customer>[].obs;

  RxString selectedCustomerCode = ''.obs;
  RxString selectedBeatCode = ''.obs;

  RxString employeeCode = ''.obs;
  RxString employeeToken = "".obs;

  var isLoading = false.obs;

  RxInt selectedAddCustomerType = 0.obs;
  RxList<Config> configList = <Config>[].obs;
  RxList<RoutePlan> routePlanBeatList = <RoutePlan>[].obs;

  final List<Map<String, dynamic>> routePlanTypeList = [
    {'id': '1', 'name': 'Fixed'},
    {'id': '2', 'name': 'Daywise'},
    {'id': '3', 'name': 'Datewise'},
  ];

  final List<String> dayWiseList = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
  ];

  RxString selectedRoutePlanType = ''.obs;
  RxString selectedDay = ''.obs;

  TextEditingController dateWiseController = TextEditingController();

  RxString dateWiseToShow = ''.obs;
  RxString dateWiseToSave = ''.obs;

  // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer

  @override
  void onInit() async {
    super.onInit();

    await fetchEmployeeData();
    await fetchedToken();

    await getRoutePlanBeatListAPI();
  }

  @override
  void onClose() {
    super.onClose();
    // claimComplainController.dispose();
  }

  Future<String?> fetchEmployeeData() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? employeeDataJson = prefs.getString('employee');
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];
      employeeCode.value = empCode!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  Future<void> getRoutePlanBeatListAPI() async {
    isLoading.value = true;
    try {
      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getRoutePlanBeatListEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> beatRoute =
                List<Map<String, dynamic>>.from(response.data['beat_list']);
            print(beatRoute);

            // Create a new list containing only the desired fields
            routePlanBeatList.value = beatRoute.map((beat) {
              String beatCode = '';
              String beatName = '';

              // Process values
              if (beat['beat_code'] != null) {
                try {
                  beatCode = convertValue(beat['beat_code'], String);
                } catch (e) {
                  print("Error converting beat_code: $e");
                }
              }

              if (beat['beat_name'] != null) {
                try {
                  beatName = convertValue(beat['beat_name'], String);
                } catch (e) {
                  print("Error converting beat_name: $e");
                }
              }

              return RoutePlan(
                beat_code: beatCode,
                beat_name: beatName,
              );
            }).toList();

            print("beatList: ${routePlanBeatList}");
          }
        } else {
          print('Response data is null');
        }
      } catch (e) {
        print('Error parsing response data: $e');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {
        }
      }
      await ApiLogger.logError(
        apiName: ApiClient.getRoutePlanListEndpoint,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: ApiClient.getRoutePlanListEndpoint,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> addRoutePlanAPI() async {
    Map<String, dynamic> data = {};

    data = {
      "beat_code": "${selectedBeatCode.value}",
      "bdm_type": "${selectedRoutePlanType.value}", // 1-fix
      "bdm_date": "${dateWiseToSave.value}",
      "bdm_day": "${selectedDay.value}",
    };

    try {
      print("data: ${data}");

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };
      print("headers: ${headers}");

      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.postAddRoutePlanEndpoint),
        data: data,
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.data);

      if (response.data != null && response.statusCode == 200) {
        showToastMessage("Route plan requested successfully");
        final routePlanList = Get.find<RoutePlanListController>();
        routePlanList.fetchedToken();
        routePlanList.getRoutePlanListAPI();
        EasyLoading.dismiss();
        Get.back();
      } else {
        print('Response data is null');
        EasyLoading.dismiss();
      }
    } on DioException catch (e) {
      String errorMessage;

      if (e.response != null) {
        errorMessage =
            'Error ${e.response!.statusCode}: ${e.response!.statusMessage}';
        print(errorMessage);
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        }
      } else {
        errorMessage = 'Network error: ${e.message}';
        print(errorMessage);
      }

      await ApiLogger.logError(
        apiName: ApiClient.postAddBeatEndpoint,
        apiRequestData: data,
        apiResponse: errorMessage,
      );
      EasyLoading.dismiss();
    } on TimeoutException catch (e) {
      print('Request timed out: $e');
      EasyLoading.dismiss();

      await ApiLogger.logError(
        apiName: ApiClient.postAddBeatEndpoint,
        apiRequestData: data,
        apiResponse: "TimeoutException: $e",
      );
    } finally {
      EasyLoading.dismiss();
    }
  }

  String? getBeatCodeFromName(String selectedType) {
    try {
      final districtType = routePlanBeatList.firstWhere(
        (districtType) => districtType.beat_name == selectedType,
      );
      return districtType.beat_code;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getDistrictIDFromName: $e');
      }
      return null;
    }
  }

  String? getRoutePlanTypeIDFromName(String selectedType) {
    try {
      final type = routePlanTypeList.firstWhere(
        (type) => type['name'].toString() == selectedType,
      );
      return type['id'].toString();
    } catch (e) {
      if (kDebugMode) {
        print('Error in getRoutePlanTypeIDFromName: $e');
      }
      return null;
    }
  }

  String formatDateTimeToSave(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(dateTime);
  }

  String formatDateTimeToShow(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('dd MMM yyyy');
    return formatter.format(dateTime);
  }
}

dynamic convertValue(dynamic value, [Type targetType = String]) {
  try {
    if (targetType == int) {
      String stringValue = value is String ? value : value.toString();
      return int.tryParse(stringValue) ?? 0;
    } else if (targetType == double) {
      String stringValue = value is String ? value : value.toString();
      return double.tryParse(stringValue) ?? 0.0;
    } else {
      return value;
    }
  } catch (e) {
    print("Error converting value to $targetType: $e");
    return targetType == int
        ? 0
        : targetType == double
            ? 0.0
            : "";
  }
}
