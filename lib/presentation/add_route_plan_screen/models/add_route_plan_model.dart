import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

class RoutePlan {
  String? beat_code;
  String? beat_name;

  RoutePlan({
    this.beat_code,
    this.beat_name,
  });

  factory RoutePlan.fromMap(Map<String, dynamic> map) {
    return RoutePlan(
      beat_code: map['${TableValues.beatCode}'] ?? '',
      beat_name: map['${TableValues.beatName}'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      '${TableValues.beatCode}': beat_code,
      '${TableValues.beatName}': beat_name,
    };
  }

  @override
  String toString() {
    return 'Beat{beat_code: $beat_code, beat_name: $beat_name,}';
  }
}
