import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/presentation/complain_screen/controller/complain_controller.dart';
import 'package:sfm_new/presentation/view_complain_screen/models/view_complain_model.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/claim_complain_model.dart';
import 'package:sfm_new/sfmDatabase/models/claim_complain_type_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';

import 'dart:async';

class ViewComplainController extends GetxController {
  Rx<ViewComplainModel> viewComplainModelObj = ViewComplainModel().obs;

  String ccCode = "";
  RxList<ClaimComplain> complainList = <ClaimComplain>[].obs;

  RxList<ClaimComplainType> complainTypeList = <ClaimComplainType>[].obs;
  RxList<Customer> customerList = <Customer>[].obs;

  @override
  void onInit() async {
    super.onInit();

    EasyLoading.show(status: 'Loading...');
    print(
        "instance.selectedCCCode ${ComplainController.instance.selectedCCCode.value}");
    await fetchClaimComplain();
    await fetchClaimComplainType();
    await fetchCustomers();
  }

  Future<void> fetchClaimComplain() async {
    print("fetchClaimComplain called");
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps = await db.query(
        "${TableValues.tableClaimComplain}",
        where: "${TableValues.ccCode} = ?",
        whereArgs: ["${ComplainController.instance.selectedCCCode.value}"]);
    complainList.value = List.generate(maps.length, (index) {
      return ClaimComplain.fromMap(maps[index]);
    });
    print("complainList : ${complainList}");
  }

  Future<void> fetchClaimComplainType() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableClaimComplainType}");
    print(maps);
    complainTypeList.value = List.generate(maps.length, (index) {
      return ClaimComplainType.fromMap(maps[index]);
    });
    print("complainTypeList : ${complainTypeList}");
  }

  Future<void> fetchCustomers() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCustomer}");
    print(maps);
    customerList.value = List.generate(maps.length, (index) {
      return Customer.fromMap(maps[index]);
    });
    await EasyLoading.dismiss();
  }

  String? getCCTypeNameFromID(int selectedID) {
    try {
      print(selectedID);
      final bankData = complainTypeList.firstWhere(
        (bank) => bank.cct_id == selectedID,
        orElse: () => throw "No data found for this ID1",
      );
      return bankData.cct_type_name!;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCCTypeNameFromID: $e');
      }
      return null;
    }
  }

  String? getCustomerNameFromTypeID(String selectedType) {
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmCode == selectedType,
        orElse: () => throw "No data found for this ID2",
      );
      return customerType.cmName;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerNameFromTypeID: $e');
      }
      return null;
    }
  }

  String? getCustomerTypeFromCMCode(String selectedType) {
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmCode == selectedType,
        orElse: () => throw "No data found for this ID3",
      );
      String custTypeString = "";
      if (customerType.cmType == 1) {
        custTypeString = "Company";
      } else if (customerType.cmType == 2) {
        custTypeString = "SS";
      } else if (customerType.cmType == 3) {
        custTypeString = "Distributor";
      } else if (customerType.cmType == 4) {
        custTypeString = "Dealer";
      } else if (customerType.cmType == 5) {
        custTypeString = "Retailer";
      }
      return custTypeString;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerNameFromTypeID: $e');
      }
      return null;
    }
  }

// 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer

  String getClientType(int clientType) {
    String clientTypeText;

    if (clientType == 1) {
      clientTypeText = "Company";
    } else if (clientType == 2) {
      clientTypeText = "SS";
    } else if (clientType == 3) {
      clientTypeText = "Distributor";
    } else if (clientType == 4) {
      clientTypeText = "Dealer";
    } else {
      clientTypeText = "Retailer";
    }
    return clientTypeText;
  }

  String formatDate(String dateString) {
    DateTime dateTime = DateTime.parse(dateString);
    DateFormat formatter = DateFormat('dd MMM yyyy');
    String formattedDate = formatter.format(dateTime);
    return formattedDate;
  }
}
