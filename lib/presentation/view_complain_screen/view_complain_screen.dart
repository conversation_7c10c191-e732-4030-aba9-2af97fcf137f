import 'package:sfm_new/core/utils/webview_image_load.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';

import 'controller/view_complain_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

// ignore_for_file: must_be_immutable
class ViewComplainScreen extends GetWidget<ViewComplainController> {
  const ViewComplainScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(),
        body: Container(
          margin: EdgeInsets.fromLTRB(14.h, 15.v, 18.h, 5.v),
          decoration: AppDecoration.outlineGray.copyWith(
            borderRadius: BorderRadiusStyle.roundedBorder10,
          ),
          child: Obx(
            () {
              if (controller.complainList.length > 0) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(height: 8.v),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: EdgeInsets.only(left: 16.h),
                        child: Text(
                          controller.getCustomerNameFromTypeID(
                                  controller.complainList[0].ccCMCode!) ??
                              "",
                          style: CustomTextStyles.titleMediumBluegray900Bold_1,
                        ),
                      ),
                    ),
                    SizedBox(height: 8.v),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                        title: "Client Name",
                        textValue: controller.getCustomerNameFromTypeID(
                                controller.complainList[0].ccCMCode!) ??
                            "",
                      ),
                    ),
                    SizedBox(height: 8.v),

                    // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                        title: "Client Type",
                        textValue: controller.getCustomerTypeFromCMCode(
                                controller.complainList[0].ccCMCode!) ??
                            "",
                      ),
                    ),
                    SizedBox(height: 8.v),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                        title:
                            '${controller.complainList[0].ccTypeID == 1 ? "Claim Type" : "Complain Type"}',
                        textValue: controller.getCCTypeNameFromID(
                                controller.complainList[0].ccType!) ??
                            "",
                      ),
                    ),
                    SizedBox(height: 8.v),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                        title: "Date",
                        textValue: controller.formatDate(
                            "${controller.complainList[0].ccCreatedAt}"),
                      ),
                    ),
                    SizedBox(height: 8.v),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                        title: "Status",
                        textValue: controller.complainList[0].ccStatus == 0
                            ? "Pending"
                            : controller.complainList[0].ccStatus == 1
                                ? "Approved"
                                : "Rejected",
                      ),
                    ),
                    SizedBox(height: 8.v),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                        title: "lbl_note".tr,
                        textValue: "${controller.complainList[0].ccNote}",
                      ),
                    ),
                    SizedBox(height: 16.v),
                    Visibility(
                      visible: controller.complainList[0].ccImage != ""
                          ? true
                          : false,
                      child: CustomElevatedButton(
                        height: 50.v,
                        text:
                            '${controller.complainList[0].ccTypeID == 1 ? "View claim photo" : "View complain photo"}',
                        buttonStyle: CustomButtonStyles.fillPrimary,
                        buttonTextStyle: CustomTextStyles
                            .labelLargeOpenSansOnErrorContainerExtraBold,
                        onPressed: () {
                          print("claim complain button tapped");
                          String? myString = controller.complainList[0].ccImage;
                          print("myString: ${myString}");
                          int imgLength = myString?.length ?? 0;
                          print("imgLength: ${imgLength}");

                          if (imgLength < 75) {
                            openImageDialog(
                                "Claim/Complain Image",
                                "${ApiClient.imgClaimComplainBaseURL}${controller.complainList[0].ccImage}",
                                1);
                          } else {
                            openImageDialog("Expense Image",
                                "${controller.complainList[0].ccImage}", 2);
                          }
                        },
                      ),
                    ),
                  ],
                );
              } else {
                return Center(
                  child: CircularProgressIndicator(),
                );
              }
            },
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "Claim/Complain",
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildClaimType({
    required String title,
    required String textValue,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 1.v),
          child: Text(
            title,
            style: CustomTextStyles.titleSmallPoppinsGray60002.copyWith(
              color: appTheme.gray60002,
            ),
          ),
        ),
        SizedBox(width: 20),
        Expanded(
          child: Text(
            textValue,
            textAlign: TextAlign.end,
            style: CustomTextStyles.bodyMediumPoppinsBluegray900.copyWith(
              color: appTheme.blueGray900,
            ),
            maxLines: 10,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
