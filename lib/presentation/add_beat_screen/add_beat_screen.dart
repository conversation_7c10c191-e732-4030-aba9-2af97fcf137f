// ignore_for_file: 

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/presentation/add_beat_screen/controller/add_beat_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';
import 'package:animated_custom_dropdown/custom_dropdown.dart';

// ignore_for_file: must_be_immutable
class AddBeatScreen extends GetWidget<AddBeatController> {
  AddBeatScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: _buildAppBar(),
        body: SingleChildScrollView(
          child: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(
              horizontal: 16.h,
              vertical: 15.v,
            ),
            child: Obx(() {
              EasyLoading.dismiss();
              return SingleChildScrollView(
                child: Column(
                  children: [
                    CustomAllTextField(
                      controller: controller.beatNameController,
                      hintText: "Beat Name",
                      height: 55,
                      showError: controller.showError.value,
                      isValid: controller.isBeatNameValid.value,
                    ),
                    SizedBox(height: 10.v),
                    CustomDropdown(
                      decoration: CustomDropdownDecoration(
                        expandedFillColor: Colors.white,
                        expandedBorder: Border.all(color: Colors.grey[300]!),
                        expandedBorderRadius: BorderRadius.circular(12),
                        closedBorder: Border.all(color: Colors.grey[300]!),
                        closedBorderRadius: BorderRadius.circular(12),
                        listItemStyle: CustomTextStyles.titleSmallBluegray900,
                        headerStyle: CustomTextStyles.titleSmallBluegray900,
                        closedErrorBorder: Border.all(color: Colors.red),
                        closedErrorBorderRadius: BorderRadius.circular(12),
                      ),
                      hintText: 'Select State',
                      // initialItem:
                      //     controller.isFromProductShopInfo.value == true
                      //         ? "${controller.selectedStateName.value}"
                      //         : "Select State",
                      items: controller.stateList
                          .map((cm) => cm.state_name!)
                          .toList(),
                      onChanged: (value) {
                        print('Selected State: $value');
                        final selectedStateID =
                            controller.getStateIDFromName('${value}');
                        controller.selectedStateID.value = selectedStateID ?? 0;
                        controller.selectedDistrictID.value = 0;
                        controller.selectedTownID.value = 0;
                      },
                    ),
                    SizedBox(height: 10.v),
                    CustomDropdown.search(
                      decoration: CustomDropdownDecoration(
                        searchFieldDecoration: SearchFieldDecoration(
                            textStyle: CustomTextStyles.titleSmallBluegray900),
                        noResultFoundStyle:
                            CustomTextStyles.titleSmallBluegray900,
                        listItemStyle: CustomTextStyles.titleSmallBluegray900,
                        headerStyle: CustomTextStyles.titleSmallBluegray900,
                        expandedFillColor: Colors.white,
                        expandedBorder: Border.all(color: Colors.grey[300]!),
                        expandedBorderRadius: BorderRadius.circular(12),
                        closedBorder: Border.all(color: Colors.grey[300]!),
                        closedBorderRadius: BorderRadius.circular(12),
                      ),
                      hintText: 'Select District',
                      searchHintText: 'Select District',
                      items: controller.districtList
                          .where((st) =>
                              st.state_id == controller.selectedStateID.value)
                          .map((cm) => cm.dm_name!)
                          .toList(),
                      excludeSelected: false,
                      // initialItem:
                      //     controller.isFromProductShopInfo.value == true
                      //         ? "${controller.selectedDistrictName.value}"
                      //         : "Select District",
                      onChanged: (selectedType) {
                        print('Selected District: $selectedType');
                        final selectedDistrictID =
                            controller.getDistrictIDFromName('${selectedType}');
                        controller.selectedDistrictID.value =
                            selectedDistrictID ?? 0;
                      },
                    ),
                    SizedBox(height: 10.v),
                    CustomDropdown.search(
                      decoration: CustomDropdownDecoration(
                        searchFieldDecoration: SearchFieldDecoration(
                            textStyle: CustomTextStyles.titleSmallBluegray900),
                        noResultFoundStyle:
                            CustomTextStyles.titleSmallBluegray900,
                        listItemStyle: CustomTextStyles.titleSmallBluegray900,
                        headerStyle: CustomTextStyles.titleSmallBluegray900,
                        expandedFillColor: Colors.white,
                        expandedBorder: Border.all(color: Colors.grey[300]!),
                        expandedBorderRadius: BorderRadius.circular(12),
                        closedBorder: Border.all(color: Colors.grey[300]!),
                        closedBorderRadius: BorderRadius.circular(12),
                      ),
                      hintText: 'Select Town',
                      searchHintText: 'Select Town',
                      items: controller.townList
                          .where((dt) =>
                              dt.district_id ==
                              controller.selectedDistrictID.value)
                          .map((cm) => cm.town_name!)
                          .toList(),
                      excludeSelected: false,
                      // initialItem:
                      //     controller.isFromProductShopInfo.value == true
                      //         ? "${controller.selectedTownName.value}"
                      //         : "Select Town",
                      onChanged: (selectedType) {
                        print('Selected Town : $selectedType');
                        final selectedTownID =
                            controller.getTownIDFromName('${selectedType}');
                        controller.selectedTownID.value = selectedTownID ?? 0;
                      },
                    ),
                    SizedBox(height: 10.v),
                  ],
                ),
              );
            }),
          ),
        ),
        bottomNavigationBar: _buildSubmit(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "Add Beat",
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildSubmit() {
    return Obx(
      () => CustomElevatedButton(
        text: controller.isFromProductShopInfo.value ? "Update" : "Submit",
        margin: EdgeInsets.only(
          left: 25.h,
          right: 25.h,
          bottom: 24.v,
        ),
        onPressed: () {
          FocusManager.instance.primaryFocus?.unfocus;
          addBeat();
        },
      ),
    );
  }

  void addBeat() async {
    controller.showError.value = true;
    controller.validateAllFields();

    if (controller.beatNameController.value.text.isEmpty) {
      showToastMessage("Please enter beat name");
    } else if (controller.selectedStateID.value == 0) {
      showToastMessage("Please select state");
    } else if (controller.selectedDistrictID.value == 0) {
      showToastMessage("Please select district");
    } else if (controller.selectedTownID.value == 0) {
      showToastMessage("Please select town");
    } else {
      print("shop name: ${controller.beatNameController.value.text}");
      print("state id: ${controller.selectedStateID.value}");
      print("district id: ${controller.selectedDistrictID.value}");
      print("town id: ${controller.selectedTownID.value}");

      final List<ConnectivityResult> connectivityResult =
          await (Connectivity().checkConnectivity());

      if (connectivityResult.contains(ConnectivityResult.none)) {
        showToastMessage("Please check your internet connection");
      } else {
        controller.addBeatAPI();
      }
    }
  }
}

class CustomAllTextField extends StatelessWidget {
  final AddBeatController beatController = Get.put(AddBeatController());

  final Rx<TextEditingController> controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final TextInputType? keyboardType;
  final int? maxLength;
  final Function(String)? onChanged;
  final bool isValid;
  final bool showError;

  CustomAllTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.keyboardType,
    this.maxLength,
    this.onChanged,
    this.isValid = true,
    this.showError = false,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius,
          border: Border.all(
            color: showError && !isValid ? Colors.red : Colors.grey.shade300,
          ),
        ),
        child: TextFormField(
          style: CustomTextStyles.titleSmallBluegray900,
          controller: controller.value,
          keyboardType: keyboardType,
          maxLength: maxLength,
          inputFormatters: [
            if (keyboardType == TextInputType.text)
              FilteringTextInputFormatter.singleLineFormatter,
            if (keyboardType == TextInputType.phone)
              FilteringTextInputFormatter.digitsOnly,
          ],
          onChanged: (value) {
            beatController.validateField(
                beatController.beatNameController.value.text,
                beatController.isBeatNameValid);
            if (onChanged != null) onChanged!(value);
          },
          decoration: InputDecoration(
            counterText: "",
            hintText: hintText,
            hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
            contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
            errorText:
                showError && !isValid ? 'This field cannot be empty' : null,
            border: InputBorder.none,
          ),
        ),
      ),
    );
  }
}

class CustomAllIDTextField extends StatelessWidget {
  final AddBeatController beatController = Get.put(AddBeatController());

  final Rx<TextEditingController> controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final TextInputType? keyboardType;
  final int? maxLength;
  final Function(String)? onChanged;
  final bool isValid;
  final bool showError;

  CustomAllIDTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.keyboardType,
    this.maxLength,
    this.onChanged,
    this.isValid = true,
    this.showError = false,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius,
          border: Border.all(
            color: showError && !isValid ? Colors.red : Colors.grey.shade300,
          ),
        ),
        child: TextFormField(
          style: CustomTextStyles.titleSmallBluegray900,
          controller: controller.value,
          keyboardType: keyboardType,
          maxLength: maxLength,
          inputFormatters: [
            if (keyboardType == TextInputType.text)
              FilteringTextInputFormatter.singleLineFormatter,
            if (keyboardType == TextInputType.phone)
              FilteringTextInputFormatter.digitsOnly,
          ],
          onChanged: (value) {
            beatController.validateAllFields();
            if (onChanged != null) onChanged!(value);
          },
          decoration: InputDecoration(
            counterText: "",
            hintText: hintText,
            hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
            contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
            errorText:
                showError && !isValid ? 'This field cannot be empty' : null,
            border: InputBorder.none,
          ),
        ),
      ),
    );
  }
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
        text: newValue.text.toUpperCase(), selection: newValue.selection);
  }
}

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final TextInputType? keyboardType;
  final int? maxLength;

  CustomTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.keyboardType,
    this.maxLength,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextFormField(
        style: CustomTextStyles.titleSmallBluegray900,
        controller: controller,
        keyboardType: keyboardType,
        maxLength: maxLength,
        decoration: InputDecoration(
          counterText: "",
          hintText: hintText,
          hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
          contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
          border: InputBorder.none,
        ),
      ),
    );
  }
}
