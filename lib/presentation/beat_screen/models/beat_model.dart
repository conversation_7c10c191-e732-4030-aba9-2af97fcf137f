class RetailerModel {
  final int id;
  final String name;
  final String address;
  final String city;
  final String phoneNumber;

  RetailerModel({
    required this.id,
    required this.name,
    required this.address,
    required this.city,
    required this.phoneNumber,
  });
}

class DistributorModel {
  final int id;
  final int name;
  final int address;
  final int city;
  final String phoneNumber;

  DistributorModel({
    required this.id,
    required this.name,
    required this.address,
    required this.city,
    required this.phoneNumber,
  });
}

class CFModel {
  final int id;
  final int name;
  final int address;
  final int city;
  final String phoneNumber;

  CFModel({
    required this.id,
    required this.name,
    required this.address,
    required this.city,
    required this.phoneNumber,
  });
}
