import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:sfm_new/widgets/custom_icon_button.dart';

import 'controller/beat_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:dynamic_tabbar/dynamic_tabbar.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';

// ignore_for_file: must_be_immutable
class BeatScreen extends GetWidget<BeatController> {
  bool isScrollable = false;
  bool showNextIcon = true;
  bool showBackIcon = true;

  Widget? leading;

  Widget? trailing;
  List<TabData> tabs = [];

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: SizedBox(
          width: double.maxFinite,
          child: Column(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomSearchBar(),
                    SizedBox(height: 10.v),
                    _buildRetailerList(),
                    SizedBox(height: 10.v),
                  ],
                ),
              ),
            ],
          ),
        ),
        floatingActionButton: Obx(() {
          return controller.addRetailerStatus.value == "1"
              ? _buildFloatingActionButton()
              : SizedBox.shrink();
        }),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: Obx(
        () => AppbarSubtitleFour(
          text: "Shop List (${controller.filteredRetailerList.length})",
        ),
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildFloatingActionButton() {
    return CustomIconButton(
      height: 70.adaptSize,
      width: 70.adaptSize,
      padding: EdgeInsets.all(19.h),
      decoration: IconButtonStyleHelper.outlineOnErrorContainer,
      alignment: Alignment.bottomRight,
      child: CustomImageView(
        imagePath: ImageConstant.imgGroup29,
      ),
      onTap: () {
        print("Plus button pressed");
        SharedPrefManager.instance
            .setBool(ConstantValues.isFromProductShopInfo, false);
        Get.toNamed(AppRoutes.addRetailerScreen);
      },
    );
  }

  Widget _buildRetailerList() {
    return Expanded(
      child: Obx(() {
        if (controller.isLoading.value) {
          return Center(
            child: CircularProgressIndicator(),
          );
        } else if (controller.filteredRetailerList.isEmpty) {
          return Center(
            child: Text(
              'No Shops found',
              style: TextStyle(fontSize: 14),
            ),
          );
        } else {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.h),
            child: ListView.separated(
              itemCount: controller.filteredRetailerList.length,
              physics: BouncingScrollPhysics(),
              shrinkWrap: true,
              separatorBuilder: (
                context,
                index,
              ) {
                return SizedBox(
                  height: 10.v,
                );
              },
              itemBuilder: (context, index) => CustomCustomerListItem(
                customer: controller.filteredRetailerList[index],
              ),
            ),
          );
        }
      }),
    );
  }
}

class CustomCustomerListItem extends StatelessWidget {
  final Customer customer;
  final BeatController controller = Get.put(BeatController());

  CustomCustomerListItem({
    required this.customer,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {

        if (customer.syncStatus == 1) {
          SharedPrefManager.instance
              .setString(ConstantValues.selectedCustCode, customer.cmCode!);

          SharedPrefManager.instance.setString(
              ConstantValues.selectedCustRelationCode,
              customer.cmRelationCode!);

          SharedPrefManager.instance
              .setString(ConstantValues.selectedCustName, customer.cmName!);

          SharedPrefManager.instance.setInt(ConstantValues.selectedCustType, 5);

          SharedPrefManager.instance.setBool(ConstantValues.isFromBeat, true);

          Get.toNamed(AppRoutes.productScreen);
        } else {
          print("Shop not synced");
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return CustomDialogShop();
            },
          );
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 9.h,
          vertical: 15.v,
        ),
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 4.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [],
              ),
            ),
            SizedBox(height: 4.v),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                // Container(
                //   height: 10.adaptSize,
                //   width: 10.adaptSize,
                //   margin: EdgeInsets.only(
                //     // top: 7.v,
                //     // bottom: 8.v,
                //   ),
                //   decoration: BoxDecoration(
                //     color: appTheme.deepOrange300,
                //     borderRadius: BorderRadius.circular(
                //       4.h,
                //     ),
                //   ),
                //   // decoration: notification.notification_read_status != 1
                //   //     ? BoxDecoration(
                //   //         color: appTheme.deepOrange300,
                //   //         borderRadius: BorderRadius.circular(
                //   //           4.h,
                //   //         ),
                //   //       )
                //   //     : null,
                // ),
                SizedBox(width: 8.v),
                Expanded(
                  child: Text(
                    "${customer.cmName} (${customer.cmArea})",
                    style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8.v),
                Visibility(
                  visible: customer.syncStatus == 0 ? true : false,
                  child: CustomImageView(
                    svgPath: ImageConstant.imgNotSynced,
                    color: Colors.red,
                    height: 15.adaptSize,
                    width: 15.adaptSize,
                    margin: EdgeInsets.only(bottom: 2.v),
                  ),
                ),
                SizedBox(width: 8),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class CustomSearchBar extends StatelessWidget {
  final BeatController controller = Get.put(BeatController());

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 12),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.5),
              spreadRadius: 2,
              blurRadius: 7,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Icon(
                Icons.search,
                color: Colors.grey,
              ),
            ),
            Expanded(
              child: TextField(
                controller: controller.searchController,
                style: CustomTextStyles.bodyMediumBluegray700,
                decoration: InputDecoration(
                  hintText: 'Search',
                  hintStyle: TextStyle(color: Colors.grey),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 10.0),
                ),
                onChanged: (value) {
                  print('Search value: $value');
                  controller.updateSearchText(value);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomDialogShop extends StatelessWidget {
  const CustomDialogShop({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    BeatController controller = Get.put(BeatController());

    return Dialog(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Retailer not synced',
              style: CustomTextStyles.titleSmallBluegray900Bold,
            ),
            SizedBox(height: 8.0),
            Text(
              'Please sync retailer to add order',
              style: CustomTextStyles.titleSmallBluegray900,
            ),
            SizedBox(height: 16.0),
            SizedBox(height: 10.0),
            Container(
              width: 250,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: 110.0,
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ButtonStyle(
                        side: WidgetStateProperty.all(
                          BorderSide(
                              color: theme.colorScheme.primary,
                              width: 1.0), 
                        ),
                        shape: WidgetStateProperty.all(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                                10.0),
                          ),
                        ),
                        foregroundColor: WidgetStateProperty.all(
                            theme.colorScheme.primary), 
                      ),
                      child: Text(
                        'Cancel',
                        style: CustomTextStyles.titleSmallBluegray900,
                      ),
                    ),
                  ),
                  SizedBox(width: 16.0),
                  SizedBox(
                    width: 110.0,
                    height: 40.0,
                    child: ElevatedButton(
                      onPressed: () {
                        print("sync pressed");
                        controller.checkInternetConnectivity();
                        Get.back();
                      },
                      child: Text(
                        'Sync',
                        style: CustomTextStyles.titleSmallBluegray900logout,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
