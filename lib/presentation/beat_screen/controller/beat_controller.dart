import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/super_stockist_screen/controller/super_stockist_controller.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/config_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:dio/dio.dart' as dio_package;

class BeatController extends GetxController
    with GetSingleTickerProviderStateMixin {
  TextEditingController searchController = TextEditingController();

  final SuperStockistController ssController =
      Get.find<SuperStockistController>();

  RxList<Customer> customerList = <Customer>[].obs;
  RxList<Customer> retailerList = <Customer>[].obs;

  RxList<Customer> filteredRetailerList = <Customer>[].obs;

  RxInt selectedCustID = 0.obs;
  RxString selectedCustName = ''.obs;

  RxString selectedCustCode = ''.obs;
  var isLoading = true.obs;
  final searchText = ''.obs;

  final Connectivity _connectivity = Connectivity();

  bool isCustomerAPISuccess = false;
  RxString employeeToken = "".obs;

  RxList<Config> configList = <Config>[].obs;
  RxString addRetailerStatus = ''.obs;

  @override
  void onInit() async {
    super.onInit();

    selectedCustCode.value = await SharedPrefManager.instance
            .getString(ConstantValues.selectedCustCode) ??
        "";

    await checkInternetConnectivity();
    await fetchedToken();
    await fetchRetailer();

    await fetchConfig();
  }

  @override
  void onClose() {
    super.onClose();
    searchController.dispose();
  }

  Future<void> fetchConfig() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableConfig}");
    print(maps);
    configList.value = List.generate(maps.length, (index) {
      return Config.fromJson(maps[index]);
    });
    print("configList: ${configList}");

    if (configList.length != 0) {
      final amountReqImg = configList.firstWhere(
        (config) => config.config_key == 'add_retailer',
      );

      print("add_retailer: ${amountReqImg.config_value}");
      addRetailerStatus.value = amountReqImg.config_value ?? "";
    }
  }

  Future<bool> checkInternetConnectivity() async {
    var connectivityResult = await _connectivity.checkConnectivity();
    if (connectivityResult != ConnectivityResult.none) {
      syncCustomersWithServer();
    }
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> syncCustomersWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedCustomers = await db.query(
        '${TableValues.tableCustomer}',
        where: '${TableValues.customerSyncStatus} = ?',
        whereArgs: [0]);

    if (unsyncedCustomers.isNotEmpty) {
      print('Syncing customers with the server...');
      print("unsyncedCustomers: ${unsyncedCustomers}");

      for (final customer in unsyncedCustomers) {
        try {
          print("Customer: ${customer}");
          await addCustomerAPI(customer);

          if (isCustomerAPISuccess == true) {
            await db.update('${TableValues.tableCustomer}',
                {'${TableValues.customerSyncStatus}': 1},
                where: '${TableValues.customerCode} = ?',
                whereArgs: [customer['${TableValues.customerCode}']]);
            print(
                'Customer with ID ${customer['${TableValues.customerCode}']} synced successfully');
          } else {
            print(
                'Error syncing Customer with ID ${customer['${TableValues.customerCode}']}');
          }
        } catch (error) {
          print(
              'Error syncing Customer with ID ${customer['${TableValues.customerCode}']}: $error');
        }
      }

      fetchRetailer();
    } else {
      print('No customers to sync.');
    }
  }

  Future<void> addCustomerAPI(Map<String, dynamic> customer) async {
    try {
      Map<String, dynamic> data = {};

      String strImage = "${customer['cm_image_relation']}";
      List<String> strImagesList = strImage.split(",");
      print("strImagesList: ${strImagesList}");

      data = {
        "cm_code": "${customer['cm_code']}",
        "cm_name": "${customer['cm_name']}",
        "cm_mobile": customer['cm_mobile'],
        "cm_mobile2": customer['cm_mobile2'],
        "cm_email": "${customer['cm_email']}",
        "cm_address": "${customer['cm_address']}",
        "cm_pincode": customer['cm_pincode'],
        "cm_gst": "${customer['cm_gst']}",
        "cm_pan": "${customer['cm_pan']}",
        "latitude": "${customer['cm_lat']}",
        "longitude": "${customer['cm_long']}",
        "cm_town_id": customer['cm_town_id'],
        "cm_outstanding_amount": customer['cm_outstanding_amount'],
        "cm_contact_person": "${customer['cm_contact_person']}",
        "cm_area": "${customer['cm_area']}",
        "cm_relation_code": "${customer['cm_relation_code']}",
        "cm_type": customer['cm_type'],
        "beat_code": "${customer['cm_beat_relation']}",
        "birth_date": "${customer['cm_dob']}",
        "anni_date": "${customer['cm_anniversary']}",
        "category_id": customer['cm_category_relation'],
        "grade_id": customer['cm_grade_relation'],
        "shop_images": strImagesList,
      };

      print("data: ${data}");

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };

      // dio_package.Dio dioClient = dio_package.Dio();
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.postAddCustomerEndpoint),
        data: data,
        options: dio_package.Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.data);
      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            isCustomerAPISuccess = true;
            EasyLoading.dismiss();
          }
        } else {
          isCustomerAPISuccess = false;
          print('Response data is null');
        }
      } catch (e) {
        isCustomerAPISuccess = false;
        print('Error parsing response data: $e');
      }
    } on dio_package.DioException catch (e) {
      isCustomerAPISuccess = false;
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        print(e.response!.data["message"] ?? "No data found");
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
    } finally {
      EasyLoading.dismiss();
    }
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer

  Future<void> fetchRetailer() async {
    isLoading.value = true;
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCustomer}");
    customerList.value = List.generate(maps.length, (index) {
      return Customer.fromMap(maps[index]);
    });
    print("customerList: ${customerList}");
    print("customerList Count: ${customerList.length}");
    print("selectedCustCode.value: ${selectedCustCode.value}");

    filteredRetailerList.value = customerList.where((customer) {
      return customer.cmType == 5 &&
          customer.cmBeatRelationCode == selectedCustCode.value;
    }).toList();

    print("filteredRetailerList: ${filteredRetailerList}");
    print("filteredRetailerList Count: ${filteredRetailerList.length}");

    print("customerList.length: ${customerList.length}");

    retailerList.value = customerList
        .where((customer) =>
            customer.cmType == 5 &&
            customer.cmBeatRelationCode == selectedCustCode.value)
        .toList();
    print("retailerList: ${retailerList}");
    print("retailerList.length: ${retailerList.length}");

    EasyLoading.dismiss();

    filteredRetailerList
        .sort((a, b) => b.cmCreatedAt!.compareTo(a.cmCreatedAt!));

    isLoading.value = false;
  }

  Future<void> fetchFilteredRetailers() async {
    filteredRetailerList.clear();

    filteredRetailerList.value = retailerList
        .where((retailer) => retailer.cmName!
            .toLowerCase()
            .contains(searchText.value.toLowerCase()))
        .toList();

    print("filteredRetailerList: ${filteredRetailerList}");

    filteredRetailerList
        .sort((a, b) => b.cmCreatedAt!.compareTo(a.cmCreatedAt!));
  }

  void updateSearchText(String newText) {
    searchText.value = newText;
    fetchFilteredRetailers(); 
  }
}
