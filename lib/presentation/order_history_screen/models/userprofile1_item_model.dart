import '../../../core/app_export.dart';

/// This class is used in the [userprofile1_item_widget] screen.
class Userprofile1ItemModel {
  Userprofile1ItemModel({
    this.dynamicText,
    this.dynamicText1,
    this.dynamicText2,
    this.dynamicText3,
    this.dynamicText4,
    this.dynamicText5,
    this.dynamicText6,
    this.dynamicText7,
    this.dynamicImage,
    this.id,
  }) {
    dynamicText = dynamicText ?? Rx("N");
    dynamicText1 = dynamicText1 ?? Rx("Navkar sales corporation");
    dynamicText2 = dynamicText2 ?? Rx("7 Jan 2021 ");
    dynamicText3 = dynamicText3 ?? Rx("Productive");
    dynamicText4 = dynamicText4 ?? Rx("Total Qty ");
    dynamicText5 = dynamicText5 ?? Rx("10");
    dynamicText6 = dynamicText6 ?? Rx("Grand Total");
    dynamicText7 = dynamicText7 ?? Rx("20,000");
    dynamicImage = dynamicImage ?? Rx(ImageConstant.imgRightArrow1);
    id = id ?? Rx("");
  }

  Rx<String>? dynamicText;

  Rx<String>? dynamicText1;

  Rx<String>? dynamicText2;

  Rx<String>? dynamicText3;

  Rx<String>? dynamicText4;

  Rx<String>? dynamicText5;

  Rx<String>? dynamicText6;

  Rx<String>? dynamicText7;

  Rx<String>? dynamicImage;

  Rx<String>? id;
}
