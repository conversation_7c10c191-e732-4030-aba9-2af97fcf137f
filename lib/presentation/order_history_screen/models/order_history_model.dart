import '../../../core/app_export.dart';
import 'userprofile1_item_model.dart';


class OrderHistoryModel {
  Rx<List<Userprofile1ItemModel>> userprofile1ItemList = Rx(
    [
      Userprofile1ItemModel(
          dynamicText: "N".obs,
          dynamicText1: "Navkar sales corporation".obs,
          dynamicText2: "7 Jan 2021 ".obs,
          dynamicText3: "Productive".obs,
          dynamicText4: "Total Qty ".obs,
          dynamicText5: "10".obs,
          dynamicText6: "Grand Total".obs,
          dynamicText7: "20,000".obs,
          dynamicImage: ImageConstant.imgRightArrow1.obs),
      Userprofile1ItemModel(
        dynamicText: "N".obs,
        dynamicText1: "Navkar sales corporation".obs,
        dynamicText2: "7 Jan 2021 ".obs,
        dynamicText3: "Non-Productive".obs,
      ),
      Userprofile1ItemModel(
        dynamicText: "N".obs,
        dynamicText1: "Navkar sales corporation".obs,
        dynamicText2: "7 Jan 2021 ".obs,
        dynamicText3: "Non-Productive".obs,
      ),
      Userprofile1ItemModel(
        dynamicText: "N".obs,
        dynamicText1: "Navkar sales corporation".obs,
        dynamicText2: "7 Jan 2021 ".obs,
        dynamicText3: "Non-Productive".obs,
      ),
      Userprofile1ItemModel(
        dynamicText: "N".obs,
        dynamicText1: "Navkar sales corporation".obs,
        dynamicText2: "7 Jan 2021 ".obs,
        dynamicText3: "Non-Productive".obs,
      ),
      Userprofile1ItemModel(
        dynamicText: "N".obs,
        dynamicText1: "Navkar sales corporation".obs,
        dynamicText2: "7 Jan 2021 ".obs,
        dynamicText3: "Non-Productive".obs,
      ),
      Userprofile1ItemModel(
        dynamicText: "N".obs,
        dynamicText1: "Navkar sales corporation".obs,
        dynamicText2: "7 Jan 2021 ".obs,
        dynamicText3: "Non-Productive".obs,
      ),
      Userprofile1ItemModel(
        dynamicText: "N".obs,
        dynamicText1: "Navkar sales corporation".obs,
        dynamicText2: "7 Jan 2021 ".obs,
        dynamicText3: "Non-Productive".obs,
      ),
      Userprofile1ItemModel(
        dynamicText: "N".obs,
        dynamicText1: "Navkar sales corporation".obs,
        dynamicText2: "7 Jan 2021 ".obs,
        dynamicText3: "Non-Productive".obs,
      ),
      Userprofile1ItemModel(
        dynamicText: "N".obs,
        dynamicText1: "Navkar sales corporation".obs,
        dynamicText2: "7 Jan 2021 ".obs,
        dynamicText3: "Non-Productive".obs,
      ),
      Userprofile1ItemModel(
        dynamicText: "N".obs,
        dynamicText1: "Navkar sales corporation".obs,
        dynamicText2: "7 Jan 2021 ".obs,
        dynamicText3: "Non-Productive".obs,
      ),
      Userprofile1ItemModel(
        dynamicText: "N".obs,
        dynamicText1: "Navkar sales corporation".obs,
        dynamicText2: "7 Jan 2021 ".obs,
        dynamicText3: "Non-Productive".obs,
      ),
      Userprofile1ItemModel(
        dynamicText: "N".obs,
        dynamicText1: "Navkar sales corporation".obs,
        dynamicText2: "7 Jan 2021 ".obs,
        dynamicText3: "Non-Productive".obs,
      ),
      Userprofile1ItemModel(
        dynamicText: "N".obs,
        dynamicText1: "Navkar sales corporation".obs,
        dynamicText2: "7 Jan 2021 ".obs,
        dynamicText3: "Non-Productive".obs,
      ),
    ],
  );
}
