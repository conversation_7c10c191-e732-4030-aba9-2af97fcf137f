import '../controller/order_history_controller.dart';
import '../models/userprofile1_item_model.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';

// ignore: must_be_immutable
class Userprofile1ItemWidget extends StatelessWidget {
  Userprofile1ItemWidget(
    this.userprofile1ItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  Userprofile1ItemModel userprofile1ItemModelObj;

  var controller = Get.find<OrderHistoryController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 7.h,
        vertical: 3.v,
      ),
      decoration: AppDecoration.outlineGray.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder10,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 35.adaptSize,
            margin: EdgeInsets.only(
              top: 1.v,
              bottom: 53.v,
            ),
            padding: EdgeInsets.symmetric(
              horizontal: 12.h,
              vertical: 6.v,
            ),
            decoration: AppDecoration.fillAmberA.copyWith(
              borderRadius: BorderRadiusStyle.roundedBorder17,
            ),
            child: Obx(
              () => Text(
                userprofile1ItemModelObj.dynamicText!.value,
                style: CustomTextStyles.titleSmallOnErrorContainer15,
              ),
            ),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 7.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Obx(
                        () => Text(
                          userprofile1ItemModelObj.dynamicText1!.value,
                          style: CustomTextStyles.titleMediumBluegray900Bold_1,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(bottom: 3.v),
                        child: Obx(
                          () => Text(
                            userprofile1ItemModelObj.dynamicText2!.value,
                            style: CustomTextStyles.bodySmallOpenSansGray500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 2.v),
                  Obx(
                    () => Text(
                      userprofile1ItemModelObj.dynamicText3!.value,
                      style: CustomTextStyles.titleSmallBluegray400,
                    ),
                  ),
                  SizedBox(height: 4.v),
                  SizedBox(
                    width: 274.h,
                    child: Row(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Obx(
                              () => Text(
                                userprofile1ItemModelObj.dynamicText4!.value,
                                style: CustomTextStyles
                                    .labelLargeOpenSansBluegray40013,
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(left: 13.h),
                              child: Obx(
                                () => Text(
                                  userprofile1ItemModelObj.dynamicText5!.value,
                                  style: CustomTextStyles
                                      .titleSmallBluegray900Bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 23.h),
                          child: SizedBox(
                            height: 39.v,
                            child: VerticalDivider(
                              width: 1.h,
                              thickness: 1.v,
                              color: appTheme.black900.withValues(alpha: 0.2),
                              indent: 3.h,
                              endIndent: 6.h,
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 27.h),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Obx(
                                () => Text(
                                  userprofile1ItemModelObj.dynamicText6!.value,
                                  style: CustomTextStyles
                                      .labelLargeOpenSansBluegray40013,
                                ),
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomImageView(
                                    imagePath: ImageConstant.imgRupeeIndian1210,
                                    height: 12.adaptSize,
                                    width: 12.adaptSize,
                                    margin: EdgeInsets.only(
                                      top: 3.v,
                                      bottom: 5.v,
                                    ),
                                  ),
                                  Obx(
                                    () => Text(
                                      userprofile1ItemModelObj
                                          .dynamicText7!.value,
                                      style: CustomTextStyles
                                          .titleSmallBluegray900Bold,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Spacer(),
                        Obx(
                          () => CustomImageView(
                            imagePath:
                                userprofile1ItemModelObj.dynamicImage!.value,
                            height: 15.adaptSize,
                            width: 15.adaptSize,
                            margin: EdgeInsets.only(
                              top: 13.v,
                              bottom: 11.v,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
