// ignore_for_file: invalid_use_of_protected_member

import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/order_history_screen/models/order_history_model.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/call_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

enum OrderCustType { all, ss, distributor, dealer, retailer }

enum OrderDays { all, today, yesterday, last7days, last30days, date }

class OrderHistoryController extends GetxController {
  Rx<OrderHistoryModel> orderHistoryModelObj = OrderHistoryModel().obs;

  RxList<CallData> callList = <CallData>[].obs;
  RxList<CallData> filteredCallList = <CallData>[].obs;
  RxList<Customer> customerList = <Customer>[].obs;

  RxSet<OrderCustType> currentOrderCustType =
      <OrderCustType>{OrderCustType.all}.obs;

  RxSet<OrderDays> currentOrderDays = <OrderDays>{OrderDays.today}.obs;

  var isLoading = true.obs;

  RxDouble grandTotalAmount = 0.0.obs;

  late Rx<DateTime> fromDate;
  late Rx<DateTime> toDate;

  RxString formattedFromDate = ''.obs;
  RxString formattedToDate = ''.obs;

  RxString filterFromDate = ''.obs;
  RxString filterToDate = ''.obs;
  RxString employeeToken = "".obs;
  final Dio dio = Dio();

  final searchText = ''.obs;

  RxString pdfUrl = ''.obs;
  RxString partyName = ''.obs;

  var isReady = false.obs;
  var errorMessage = ''.obs;
  String? filePath;

  @override
  void onInit() async {
    super.onInit();

    EasyLoading.dismiss();

    fromDate = DateTime.now().obs;
    toDate = (DateTime.now().add(Duration(days: 1))).obs;

    formattedFromDate.value = formatDateTime(fromDate.toString());
    formattedToDate.value = formatDateTime(toDate.toString());

    await fetchedToken();
    await fetchCustomers();
    await fetchCalls();
  }

  Future<void> loadPdf(String pdfUrl) async {
    try {
      isReady.value = false;
      final response = await http.get(Uri.parse(pdfUrl));
      if (response.statusCode == 200) {
        final directory = await getApplicationDocumentsDirectory();
        final now = DateTime.now();
        final formattedDate = DateFormat('ddMMyyyy').format(now);
        String partyNameTrimmed = trimTrailingSlash(partyName.value);
        print("partyNameTrimmed: ${partyNameTrimmed}");
        final file =
            File('${directory.path}/${partyNameTrimmed}_$formattedDate.pdf');
        print("file: ${file}");
        await file.writeAsBytes(response.bodyBytes);
        filePath = file.path;
        isReady.value = true;
      } else {
        throw Exception('Failed to load PDF');
      }
    } catch (e) {
      errorMessage.value = e.toString();
    }
  }

  String trimTrailingSlash(String input) {
    return input.replaceAll('/', '');
  }

  void sharePdf() async {
    if (filePath != null) {
      final files = <XFile>[];
      files.add(XFile(filePath!));
      await Share.shareXFiles(
        files,
        text: "Text",
        subject: "Subject",
      );
    } else {
      Get.snackbar('Error', 'PDF file not available to share');
    }
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  Future<bool> checkInternetConnectivity() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      showToastMessage("No Internet Connection");
    }
    return true;
  }

  String formatDateTime(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('dd MMM');
    return formatter.format(dateTime);
  }

  String formatFilterDateTime(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(dateTime);
  }

  Future<void> fetchCalls() async {
    grandTotalAmount.value = 0.0;
    isLoading(true);
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCall}");
    callList.value = List.generate(maps.length, (index) {
      return CallData.fromMap(maps[index]);
    });
    filteredCallList.value = List.generate(maps.length, (index) {
      return CallData.fromMap(maps[index]);
    });

    print("callList.length: ${callList.length}");
    print("callList: ${callList}");

    // Filter filteredCallList to show only today's orders
    filteredCallList.value = filteredCallList.value.where((call) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final callDate = DateTime.parse(call.callCreatedAt!);
      final callDateOnly =
          DateTime(callDate.year, callDate.month, callDate.day);
      return callDateOnly.isAtSameMomentAs(today);
    }).toList();

    // Sort filteredCallList in descending order based on the call date
    filteredCallList.value
        .sort((a, b) => b.callCreatedAt!.compareTo(a.callCreatedAt!));

    isLoading(false);

    fetchAmounts();
  }

  Future<void> fetchAmounts() async {
    grandTotalAmount.value = 0.0;
    for (var item in filteredCallList) {
      grandTotalAmount.value += item.callGrandTotal ?? 0.0;
    }
    print("grandTotalAmount: ${grandTotalAmount}");
  }

  Future<void> fetchFilteredCalls() async {
    filteredCallList.value = callList.value
        .where((call) =>
            '${getCustomerNameFromPartyCode(call.callClientCode ?? "")}'
                .toLowerCase()
                .contains(searchText.value.toLowerCase()))
        .toList();

    print("filteredCallList: ${filteredCallList}");

    filteredCallList.value
        .sort((a, b) => b.callCreatedAt!.compareTo(a.callCreatedAt!));

    fetchAmounts();
  }

  void updateSearchText(String newText) {
    searchText.value = newText;
    fetchFilteredCalls();
  }

  Future<void> fetchCustomers() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCustomer}");
    print(maps);
    customerList.value = List.generate(maps.length, (index) {
      return Customer.fromMap(maps[index]);
    });
    print(customerList);
  }

  String? getCustomerNameFromPartyCode(String partyCode) {
    print("partyCode: ${partyCode}");
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmCode == partyCode,
      );
      print("customerType.cmName: ${customerType.cmName}");
      return customerType.cmName;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerCodeFromTypeName: $e');
      }
      return null;
    }
  }

  int? getCustomerTypeFromPartyCode(String partyCode, int custType) {
    print("partyCode: ${partyCode}");
    print("custType: ${custType}");
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmCode == partyCode && ccType.cmType == custType,
      );
      print("customerType.cmType: ${customerType.cmType}");
      return customerType.cmType;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerCodeFromTypeName: $e');
      }
      return null;
    }
  }

  String formatDate(String dateString) {
    if (dateString.isNotEmpty) {
      DateTime dateTime = DateTime.parse(dateString);
      String formattedDate = DateFormat('dd MMM yyyy').format(dateTime);
      return formattedDate;
    } else {
      return "";
    }
  }

  void filterOrders(
      String fromDateStr,
      String toDateStr,
      Set<OrderCustType> currentOrderCustTypes,
      Set<OrderDays> currentOrderDays) {
    filteredCallList.clear();
    grandTotalAmount.value = 0.0;

    DateTime fromDate = DateTime.tryParse(fromDateStr) ?? DateTime.now();
    print("fromDate: ${fromDate}");
    DateTime toDate = (DateTime.tryParse(toDateStr) ?? DateTime.now());
    print("toDate: ${toDate}");
    bool isWithinDateRange = false;
    bool matchesOrderDays = false;

    List<CallData> filteredCalls = callList.where((call) {
      print("call inside: ${call}");
      if (!fromDateStr.isEmpty || !toDateStr.isEmpty) {
        DateTime callDate = DateTime.parse(call.callCreatedAt ?? "");
        print("callDate: ${callDate}");

        DateTime callDateOnly =
            DateTime(callDate.year, callDate.month, callDate.day);
        DateTime fromDateOnly =
            DateTime(fromDate.year, fromDate.month, fromDate.day);
        DateTime toDateOnly = DateTime(toDate.year, toDate.month, toDate.day);

        isWithinDateRange = callDateOnly.isAtSameMomentAs(fromDateOnly) ||
            callDateOnly.isAtSameMomentAs(toDateOnly) ||
            (callDateOnly.isAfter(fromDateOnly) &&
                callDateOnly.isBefore(toDateOnly));
      }

      bool matchesCallType = currentOrderCustTypes
              .contains(OrderCustType.all) ||
          (currentOrderCustTypes.contains(OrderCustType.ss) &&
              getCustomerTypeFromPartyCode(call.callClientCode ?? "", 2) ==
                  2) ||
          (currentOrderCustTypes.contains(OrderCustType.distributor) &&
              getCustomerTypeFromPartyCode(call.callClientCode ?? "", 3) ==
                  3) ||
          (currentOrderCustTypes.contains(OrderCustType.dealer) &&
              getCustomerTypeFromPartyCode(call.callClientCode ?? "", 4) ==
                  4) ||
          (currentOrderCustTypes.contains(OrderCustType.retailer) &&
              getCustomerTypeFromPartyCode(call.callClientCode ?? "", 5) == 5);

      if (!currentOrderDays.isEmpty) {
        DateTime fromDate = DateTime.tryParse(fromDateStr) ?? DateTime.now();
        DateTime toDate = DateTime.tryParse(toDateStr) ?? DateTime.now();
        print("fromDate before: ${fromDate}");

        if (currentOrderDays.contains(OrderDays.all)) {
          fromDate = fromDate.subtract(Duration(days: 60));
          fromDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
        } else if (currentOrderDays.contains(OrderDays.today)) {
          fromDate = DateTime.now();
          fromDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
        } else if (currentOrderDays.contains(OrderDays.yesterday)) {
          fromDate = fromDate.subtract(Duration(days: 1));
          fromDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
        } else if (currentOrderDays.contains(OrderDays.last7days)) {
          fromDate = fromDate.subtract(Duration(days: 7));
          fromDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
        } else if (currentOrderDays.contains(OrderDays.last30days)) {
          fromDate = fromDate.subtract(Duration(days: 30));
          fromDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
        }

        print("fromDate after: ${fromDate}");

        DateTime callFromDate = DateTime.parse(call.callCreatedAt ?? "");
        print('currentOrderDaysFromDate: ${callFromDate}');
        DateTime callToDate = DateTime.parse(call.callCreatedAt ?? "");
        print('currentOrderDaysToDate: ${callToDate}');

        matchesOrderDays =
            callFromDate.isBefore(toDate) && callToDate.isAfter(fromDate);
      }

      print("searchText: ${searchText}");
      bool matchesSearchText = searchText.isEmpty ||
          call.callCode.toLowerCase().contains(searchText.toLowerCase());
      print("matchesSearchText: ${matchesSearchText}");

      print("isWithinDateRange: $isWithinDateRange");
      print("matchesCallType: $matchesCallType");
      print("matchesOrderDays: $matchesOrderDays");

      if (isWithinDateRange || (matchesCallType && matchesOrderDays)) {
        filteredCallList.add(call);
      }

      filteredCallList.value
          .sort((a, b) => b.callCreatedAt!.compareTo(a.callCreatedAt!));

      return matchesCallType;
    }).toList();

    for (var item in filteredCallList) {
      grandTotalAmount.value += item.callGrandTotal ?? 0.0;
    }
    print("grandTotalAmount: ${grandTotalAmount}");
    print("callList: ${callList}");
    print("filteredCalls ${filteredCalls}");
    print("filteredCalls.length ${filteredCalls.length}");
  }

  Future<void> getInvoice(String callCode) async {
    print(ApiClient.getUrl(ApiClient.getInvoiceEndpoint + callCode));

    Map<String, dynamic> headers = {
      'Authorization': 'Bearer ${employeeToken.value}',
    };

    try {
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.getInvoiceEndpoint + callCode),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      try {
        if (response.statusCode == 200) {
          print("response.data: ${response.data}");
          pdfUrl.value = response.data['redirect_url'] ?? "";
          print("pdfUrl: ${pdfUrl.value}");

          filePath = response.data['redirect_url'] ?? "";
          print("filePath: ${filePath}");
          Get.toNamed(AppRoutes.orderViewInvoiceScreen, arguments: filePath);
        } else {
          print('Response data is null');
        }
      } catch (e) {
        print('Error parsing response data: $e');
      }
    } catch (e) {
      print(e);
    } finally {}
  }
}
