// ignore_for_file: duplicate_import, deprecated_member_use, unused_local_variable

import 'package:flutter/material.dart';
import 'package:sfm_new/presentation/order_history_screen/controller/order_history_controller.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'controller/order_history_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';

// ignore_for_file: must_be_immutable
class OrderViewInvoiceScreen extends GetWidget<OrderHistoryController> {
  @override
  Widget build(BuildContext context) {
    String url = Get.arguments;
    controller.loadPdf(controller.pdfUrl.value);

    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(),
        body: Stack(
          children: [
            Obx(
              () {
                if (controller.isReady.value) {
                  return PDFView(
                    filePath: controller.filePath,
                    enableSwipe: true,
                    swipeHorizontal: true,
                    autoSpacing: false,
                    pageFling: false,
                  );
                } else if (controller.errorMessage.value.isNotEmpty) {
                  return Center(
                      child: Text('Error: ${controller.errorMessage.value}'));
                } else {
                  return Center(child: CircularProgressIndicator());
                }
              },
            ),
            Positioned(
              bottom: 16,
              right: 16,
              child: FloatingActionButton(
                onPressed: () {
                  controller.sharePdf();
                },
                child: Icon(
                  Icons.share,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "View Invoice",
      ),
      styleType: Style.bgShadow,
    );
  }
}
