// ignore_for_file: unnecessary_null_comparison, must_be_immutable, invalid_use_of_protected_member

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/sfmDatabase/models/expense_model.dart';
import 'package:sfm_new/sfmDatabase/models/expense_type_model.dart';
import 'package:intl/intl.dart';
import '../controller/home_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';

enum SyncDays { today, yesterday, last7days, last15days, last30days, all }

class HomeDrawerWidget extends StatelessWidget {
  var controller = Get.find<HomeController>();

  @override
  Widget build(BuildContext context) {
    return Drawer(
      width: MediaQuery.of(context).size.width * 0.75,
      child: Column(
        children: [
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: <Widget>[
                Padding(
                  padding: EdgeInsets.only(
                    left: 0.h,
                    right: 0.h,
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      CustomImageView(
                        imagePath: ImageConstant.imgMaskGroup,
                        height: 150.v,
                        alignment: Alignment.center,
                      ),
                      Align(
                        alignment: Alignment.center,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SizedBox(
                              child: Stack(
                                alignment: Alignment.center,
                                children: [],
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(left: 0.h),
                              child: Row(
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "${controller.employeeName.value}",
                                        style: CustomTextStyles
                                            .titleMediumOnErrorContainerBold,
                                      ),
                                      SizedBox(height: 5.v),
                                      Text(
                                        "${controller.employeeCode.value}",
                                        style: CustomTextStyles
                                            .titleSmallOnErrorContainer,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16.v),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () async {
                    Navigator.of(context).pop();

                    final List<ConnectivityResult> connectivityResult =
                        await (Connectivity().checkConnectivity());

                    if (connectivityResult.contains(ConnectivityResult.none)) {
                      showToastMessage("Please check your internet connection");
                    } else {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return CustomDialog(
                            options: [
                              'Today',
                              'Yesterday',
                              'Last 7 Days',
                              'Last 15 Days',
                              'Last 30 Days',
                              'All'
                            ],
                            onOptionSelected: (selectedOption) {
                              print('Selected Option: $selectedOption');
                              if (selectedOption == 'Today') {
                                controller.syncDays = 1;
                                setSyncDays(1);
                              } else if (selectedOption == 'Yesterday') {
                                controller.syncDays = 2;
                                setSyncDays(2);
                              } else if (selectedOption == 'Last 7 Days') {
                                controller.syncDays = 7;
                                setSyncDays(7);
                              } else if (selectedOption == 'Last 15 Days') {
                                controller.syncDays = 15;
                                setSyncDays(15);
                              } else if (selectedOption == 'Last 30 Days') {
                                controller.syncDays = 30;
                                setSyncDays(30);
                              } else if (selectedOption == 'All') {
                                controller.syncDays = 60;
                                setSyncDays(60);
                              }

                              Navigator.pop(context);
                              Get.toNamed(
                                AppRoutes.syncDataScreen,
                              );
                            },
                          );
                        },
                      );
                    }
                  },
                  child: Container(
                    width: double.infinity,
                    child: Padding(
                      padding: EdgeInsets.only(
                        left: 16.h,
                        right: 8.h,
                      ),
                      child: Row(
                        children: [
                          CustomImageView(
                            svgPath: ImageConstant.imgQrcode,
                            height: 24.adaptSize,
                            width: 24.adaptSize,
                          ),
                          Padding(
                            padding: EdgeInsets.only(
                              left: 16.h,
                              top: 3.v,
                            ),
                            child: Text(
                              "lbl_re_sync_data".tr,
                              // style: CustomTextStyles.titleSmallBlack900,
                              style: CustomTextStyles
                                  .titleMediumOnPrimaryContainer,
                            ),
                          ),
                          Spacer(),
                          CustomImageView(
                            svgPath: ImageConstant.imgArrowRight,
                            height: 16.adaptSize,
                            width: 16.adaptSize,
                            margin: EdgeInsets.only(right: 8),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 20.v),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    Navigator.of(context).pop();
                    print("Help & Support button pressed");
                    Get.toNamed(AppRoutes.helpSupportScreen);
                  },
                  child: Padding(
                    padding: EdgeInsets.only(
                      left: 16.h,
                      right: 8.h,
                    ),
                    child: Row(
                      children: [
                        CustomImageView(
                          svgPath: ImageConstant.imgGroup503,
                          height: 24.adaptSize,
                          width: 24.adaptSize,
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                            left: 16.h,
                            top: 3.v,
                          ),
                          child: Text(
                            "lbl_help_support".tr,
                            style:
                                CustomTextStyles.titleMediumOnPrimaryContainer,
                          ),
                        ),
                        Spacer(),
                        CustomImageView(
                          svgPath: ImageConstant.imgArrowRight,
                          height: 16.adaptSize,
                          width: 16.adaptSize,
                          margin: EdgeInsets.only(right: 8),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 16.v),
              ],
            ),
          ),
          Spacer(),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () async {
              Navigator.of(context).pop();
              print("logout button pressed");
              final List<ConnectivityResult> connectivityResult =
                  await (Connectivity().checkConnectivity());

              if (connectivityResult.contains(ConnectivityResult.none)) {
                showToastMessage("Please check your internet connection");
              } else if (controller.accuracy.value == 0.0 ||
                  controller.accuracy.value >
                      controller.requiredAccuracy.value) {
                await controller.getLocation();
                showToastMessage(
                    "Wait while getting your location. If error persist then set location to high accuracy");
              } else {
                await controller.fetchExpenseType();

                controller.selectedOption = RxString('Skip');
                controller.showTextField.value = false;
                controller.textFieldValue.value = '';

                showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return CustomDialogLogout(
                      options: controller.expenseTypeList.value,
                      onOptionSelected: (selectedOption) {
                        print('Selected Option: $selectedOption');
                      },
                    );
                  },
                );
              }
            },
            child: Padding(
              padding: EdgeInsets.only(
                left: 16.h,
                right: 8.h,
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.logout,
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      left: 16.h,
                      top: 3.v,
                    ),
                    child: Text(
                      "lbl_logout".tr,
                      style: CustomTextStyles.titleMediumOnPrimaryContainer,
                    ),
                  ),
                  Spacer(),
                  CustomImageView(
                    svgPath: ImageConstant.imgArrowRight,
                    height: 16.adaptSize,
                    width: 16.adaptSize,
                    margin: EdgeInsets.only(right: 8),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 20.v),
        ],
      ),
    );
  }

  Future setSyncDays(int days) async {
    print("setSyncDays days: ${days}");
    SharedPrefManager.instance.setInt(ConstantValues.syncDays, days);
    int? daysSelected =
        await SharedPrefManager.instance.getInt(ConstantValues.syncDays);
    print("daysSelected: ${daysSelected}");
  }
}

class CustomDialog extends StatelessWidget {
  final List<String> options;
  final Function(String) onOptionSelected;

  const CustomDialog(
      {Key? key, required this.options, required this.onOptionSelected})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        padding: EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Upload Data For:',
              style: CustomTextStyles.titleLargeBlack900,
            ),
            SizedBox(height: 16.0),
            Column(
              children: options.map((option) {
                return GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () => onOptionSelected(option),
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.6,
                    margin: EdgeInsets.symmetric(vertical: 5.0),
                    padding: EdgeInsets.all(10.0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.0),
                      border: Border.all(
                        color:
                            theme.colorScheme.primary.withValues(alpha: 0.25),
                        width: 1.0,
                      ),
                    ),
                    child: Text(
                      option,
                      textAlign: TextAlign.center,
                      style: CustomTextStyles.titleSmallBluegray900,
                    ),
                  ),
                );
              }).toList(),
            ),
            SizedBox(height: 10.0),
          ],
        ),
      ),
    );
  }
}

class CustomDialogLogout extends StatelessWidget {
  final List<ExpenseType> options;
  final Function(String) onOptionSelected;

  const CustomDialogLogout({
    Key? key,
    required this.options,
    required this.onOptionSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    HomeController controller = Get.put(HomeController());

    return Dialog(
      backgroundColor: appTheme.whiteA700,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Logout',
              style: CustomTextStyles.titleSmallBluegray900,
            ),
            SizedBox(height: 8.0),
            Text(
              'Are you sure you want to logout?',
              style: CustomTextStyles.titleSmallBluegray900,
            ),
            SizedBox(height: 16.0),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 0.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.0),
                border: Border.all(
                  color: Colors.grey,
                  width: 1.0,
                ),
              ),
              child: Obx(
                () => CustomDropdown(
                  decoration: CustomDropdownDecoration(
                    expandedFillColor: Colors.white,
                    listItemStyle: CustomTextStyles.titleSmallBluegray900,
                    headerStyle: CustomTextStyles.titleSmallBluegray900,
                  ),
                  hintText: 'Select Expense Type',
                  // initialItem: controller.expenseTypeList.isNotEmpty
                  //     ? controller.expenseTypeList.first
                  //         .expenseName // Set the first item as default
                  //     : null,
                  excludeSelected: false,
                  items: controller.expenseTypeList
                      .map((expenseType) => expenseType.expenseName ?? "")
                      .toList(),
                  onChanged: (value) {
                    final selectedExpenseID =
                        controller.getExpenseCodeFromTypeName('${value}');

                    controller.selectedExpenseTypeName.value = '${value}';
                    print("selectedCustomerID: ${selectedExpenseID}");
                    controller.selectedExpenseType.value =
                        selectedExpenseID ?? 0;

                    print(
                        "controller.selectedOption.value1: ${controller.selectedOption.value}");
                    print("value1: ${value}");

                    controller.selectedOption.value = '${value}';
                    if (value == 'Skip') {
                      controller.showTextField.value = false;
                    } else {
                      controller.showTextField.value = true;
                      controller.textFieldValue.value = '';
                    }
                  },
                ),
              ),
            ),
            // if (selectedOption == 'HomeTown' || selectedOption == 'Out of Town')
            SizedBox(height: 16.0),
            Obx(
              () => controller.showTextField.value
                  ? SizedBox(
                      height: 65,
                      child: TextFormField(
                        keyboardType: TextInputType.number,
                        style: CustomTextStyles.titleSmallBluegray900,
                        cursorHeight: 14,
                        onChanged: (value) {
                          controller.textFieldValue.value = value;
                        },
                        decoration: InputDecoration(
                          labelStyle: CustomTextStyles.titleSmallBluegray900,
                          hintStyle: CustomTextStyles.titleSmallBluegray900,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 15.0,
                            vertical: 17.0,
                          ),
                          labelText: 'Enter value',
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: Colors.grey,
                              width: 1.0,
                            ),
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: Colors.grey,
                              width: 1.0,
                            ),
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                        ),
                      ),
                    )
                  : SizedBox.shrink(),
            ),
            SizedBox(height: 10.0),
            Container(
              width: 250,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: 110.0,
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ButtonStyle(
                        side: WidgetStateProperty.all(
                          BorderSide(
                              color: theme.colorScheme.primary, width: 1.0),
                        ),
                        shape: WidgetStateProperty.all(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                        ),
                        foregroundColor:
                            WidgetStateProperty.all(theme.colorScheme.primary),
                      ),
                      child: Text(
                        'Cancel',
                        style: CustomTextStyles.titleSmallBluegray900,
                      ),
                    ),
                  ),
                  SizedBox(width: 16.0),
                  SizedBox(
                    width: 110.0,
                    height: 40.0,
                    child: ElevatedButton(
                      onPressed: controller.isLoading.value
                          ? null // Disable button when loading
                          : () async {
                              controller.isLoading.value = true;
                              final List<ConnectivityResult>
                                  connectivityResult =
                                  await (Connectivity().checkConnectivity());

                              if (connectivityResult
                                  .contains(ConnectivityResult.none)) {
                                showToastMessage(
                                    "Please check your internet connection");
                                controller.isLoading.value = false;
                              } else if (controller.accuracy.value == 0.0 ||
                                  controller.accuracy.value >
                                      controller.requiredAccuracy.value) {
                                await controller.getLocation();
                                showToastMessage(
                                    "Wait while getting your location \n If error persist then set location to high accuracy");
                                controller.isLoading.value = false;
                              } else {
                                Get.back();
                                if (controller.selectedExpenseType.value != 0) {
                                  print(
                                      "controller.selectedOption.value2: ${controller.selectedOption.value}");
                                  if (controller.selectedOption.value != null) {
                                    onOptionSelected(
                                        controller.selectedOption.value);
                                  }

                                  if (controller.selectedOption.value !=
                                      "Skip") {
                                    String formattedDateTime =
                                        DateFormat("yyyy-MM-dd")
                                            .format(DateTime.now());
                                    print(formattedDateTime);

                                    String uuid = controller.generateUUID();

                                    var addExpense = Expense(
                                      expenseType:
                                          controller.selectedExpenseType.value,
                                      expenseCode: uuid,
                                      expenseDetail: controller
                                          .selectedExpenseTypeName.value,
                                      amount: double.parse(
                                          controller.textFieldValue.value),
                                      approvedAmount: 0,
                                      billPicture: "",
                                      fileExtension: "",
                                      longitude: controller.long.value,
                                      latitude: controller.lat.value,
                                      employeeCode:
                                          controller.employeeCode.value,
                                      expenseStatus: 0,
                                      date: formattedDateTime,
                                      kilometers: "0",
                                      syncStatus: 0,
                                    );

                                    print("addExpense: ${addExpense}");
                                    controller
                                        .addExpenseAPI(addExpense.toMap());
                                    controller.isLoading.value = false;
                                  } else {
                                    SharedPrefManager.instance.setBool(
                                        ConstantValues.isDailyLogOut, true);
                                    await controller.traccar.stopService();
                                    Get.toNamed(AppRoutes.syncDataScreen);
                                    controller.isLoading.value = false;
                                  }
                                } else {
                                  showToastMessage(
                                      "Please select expense type to continue");
                                  controller.isLoading.value = false;
                                }
                              }
                            },
                      child: Text(
                        'Logout',
                        style: CustomTextStyles.titleSmallBluegray900logout,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
