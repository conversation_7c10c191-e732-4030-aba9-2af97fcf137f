import '../controller/home_controller.dart';
import '../models/order_item_model.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';

// ignore: must_be_immutable
class HomeItemWidget extends StatelessWidget {
  HomeItemWidget(
    this.orderItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  OrderItemModel orderItemModelObj;

  var controller = Get.find<HomeController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.h),
      decoration: AppDecoration.outlineBlack.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder6,
      ),
      width: 176.h,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 9.v),
          Row(
            children: [
              Obx(
                () => CustomImageView(
                  imagePath: orderItemModelObj.orderImage1!.value,
                  height: 42.v,
                  width: 45.h,
                ),
              ),
              CustomImageView(
                imagePath: ImageConstant.imgRightArrow7,
                height: 15.adaptSize,
                width: 15.adaptSize,
                margin: EdgeInsets.only(
                  left: 90.h,
                  top: 0.v,
                  bottom: 13.v,
                ),
              ),
            ],
          ),
          SizedBox(height: 1.v),
          Padding(
            padding: EdgeInsets.only(left: 4.h),
            child: Obx(
              () => Text(
                orderItemModelObj.orderText!.value,
                style: CustomTextStyles.bodyMediumOpenSansBlack900,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 4.h),
            child: Obx(
              () => Text(
                orderItemModelObj.addOrderText!.value,
                style: CustomTextStyles.bodySmallOpenSansBluegray200,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
