import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/presentation/home_screen/controller/home_controller.dart';

class HomeOptionsWidget extends StatelessWidget {
  final HomeController controller = Get.put(HomeController());

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final optionWidth = (screenWidth - (5 * 10)) /
        6;
    print("optionWidth: ${optionWidth}");

    return Container(
      width: screenWidth * 0.95,
      margin: EdgeInsets.only(right: 1.h),
      padding: EdgeInsets.symmetric(
        horizontal: 4.h,
        vertical: 8.v,
      ),
      decoration: AppDecoration.outlineBlack.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder6,
      ),
      child: Padding(
        padding: const EdgeInsets.only(left: 0, right: 0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Obx(
              () => Wrap(
                spacing: 15,
                runSpacing: 8,
                alignment: WrapAlignment.start,
                children: [
                  if (controller.showPayment.value == 1)
                    buildOption(
                      imagePath: ImageConstant.imgPayment,
                      label: 'lbl_payment'.tr,
                      onTap: onTapPayment,
                      optionWidth: optionWidth,
                    ),
                  if (controller.showExpense.value == 1)
                    buildOption(
                      imagePath: ImageConstant.imgExpense,
                      label: 'lbl_expense'.tr,
                      onTap: onTapExpense,
                      optionWidth: optionWidth,
                    ),
                  if (controller.showLeave.value == 1)
                    buildOption(
                      imagePath: ImageConstant.imgLeave,
                      label: 'lbl_leave'.tr,
                      onTap: onTapLeave,
                      optionWidth: optionWidth,
                    ),
                  if (controller.showComplain.value == 1)
                    buildOption(
                      imagePath: ImageConstant.imgComplain,
                      label: 'lbl_complain'.tr,
                      onTap: onTapComplain,
                      optionWidth: optionWidth,
                    ),
                  if (controller.showAddBeat.value == 1)
                    buildOption(
                      imagePath: ImageConstant.imgTask,
                      label: 'Route Plan',
                      optionWidth: optionWidth,
                      onTap: () {
                        EasyLoading.show(status: 'Loading...');
                        onTapAddRoutePlan();
                      },
                    ),
                  if (controller.showReport.value == 1)
                    buildOption(
                      imagePath: ImageConstant.imgReport,
                      label: 'lbl_report'.tr,
                      onTap: onTapReport,
                      optionWidth: optionWidth,
                    ),
                  if (controller.showTeamReport.value == 1)
                    buildOption(
                      imagePath: ImageConstant.imgReport,
                      label: 'Team Report',
                      onTap: onTapTeamReport,
                      optionWidth: optionWidth,
                    ),
                    
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  onTapPayment() {
    Get.toNamed(
      AppRoutes.paymentScreen,
    );
  }

  onTapExpense() {
    Get.toNamed(
      AppRoutes.expenseScreen,
    );
  }

  onTapLeave() {
    Get.toNamed(
      AppRoutes.leaveScreen,
    );
  }

  onTapComplain() {
    Get.toNamed(
      AppRoutes.complainScreen,
    );
  }

  onTapReport() {
    Get.toNamed(
      AppRoutes.downloadReportScreen,
    );
  }

  onTapTeamReport() {
    EasyLoading.show(status: 'Loading...');
    Get.toNamed(
      AppRoutes.teamReportScreen,
    );
  }

  onTapAddRoutePlan() {
    Get.toNamed(
      AppRoutes.routePlanListScreen,
    );
  }

// Get.toNamed(AppRoutes.orderViewInvoiceScreen
  onTapTask() {
    Get.toNamed(AppRoutes.expenseFilterScreen);
  }

  onTapMeeting() {
    // Get.toNamed(
    //   AppRoutes.expenseScreen,
    // );
  }

  onTapService() {
    // Get.toNamed(
    //   AppRoutes.leaveScreen,
    // );
  }
}

Widget buildOption({
  required String imagePath,
  required String label,
  required VoidCallback onTap,
  required double optionWidth, 
}) {
  return GestureDetector(
    behavior: HitTestBehavior.translucent,
    onTap: onTap,
    child: SizedBox(
      width: optionWidth, 
      child: Column(
        children: [
          Align(
            alignment: Alignment.center,
            child: CustomImageView(
              imagePath: imagePath,
              height: 40.adaptSize,
              width: 40.adaptSize,
            ),
          ),
          Text(
            label,
            style: CustomTextStyles.bodySmallOpenSans.copyWith(
              color: appTheme.black900,
            ),
            textAlign: TextAlign.center, 
          ),
        ],
      ),
    ),
  );
}
