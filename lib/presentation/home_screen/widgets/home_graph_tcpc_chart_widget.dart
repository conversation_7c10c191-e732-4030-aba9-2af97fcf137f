// ignore_for_file: invalid_use_of_protected_member

import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/presentation/home_screen/controller/home_controller.dart';
import 'package:flutter_echarts/flutter_echarts.dart';
import 'package:sfm_new/presentation/home_screen/widgets/graph_theme_script.dart';

class HomeGraphTCPCChartWidget extends StatelessWidget {
  final HomeController controller = Get.put(HomeController());

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Visibility(
        visible: controller.showTCPCChart.value == 1 ? true : false,
        child: Container(
          margin: EdgeInsets.only(right: 1.h),
          padding: EdgeInsets.symmetric(vertical: 5.v),
          decoration: AppDecoration.outlineBlack900.copyWith(
            borderRadius: BorderRadiusStyle.roundedBorder6,
          ),
          child: Visibility(
            visible: controller.showTCPCChart.value == 1 ? true : false,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 6.0),
                Padding(
                  padding: EdgeInsets.only(
                    left: 21.h,
                    right: 11.h,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "TC - PC Chart",
                            style: CustomTextStyles.titleSmallBlack900Bold,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  height: 165,
                  child: Echarts(
                    extensions: [darkThemeScript],
                    captureAllGestures: true,
                    theme: 'dark',
                    option: '''
                  {
                  legend: {
                    data: ['TC', 'PC'],
                        textStyle: {
                          color: '#000' // Change this if needed for better visibility on dark theme
                        }
                      },
                    grid: {
                      left: '3%',
                      right: '8%',
                      top: '30%',
                      bottom: '2%',
                      containLabel: true
                    },
                    xAxis: {
                      type: 'category',
                      data: ${controller.extractDay(controller.extractDates())}
                    },
                    yAxis: {
                      type: 'value'
                    },
                    tooltip: {
                        trigger: 'axis',
                        position: function (pt, params, dom, rect, size) {
                          // Get the total number of data points
                          var dataLength = ${controller.extractDay(controller.extractDates())}.length;
                          
                          // Determine the index of the current point
                          var index = params[0].dataIndex;
                          
                          // Define positioning logic
                          if (index === 0) {
                            // First bar: position tooltip on the right
                            return [pt[0] + 10, '10%'];
                          } else if (index === dataLength - 1 || index === dataLength - 2) {
                            // Last bar: position tooltip on the left
                            return [pt[0] - size.contentSize[0] - 10, '10%'];
                          } else {
                            // Default positioning
                            return [pt[0], '10%'];
                          }
                        }
                      },
                    series: [
                            {
                              "name": "TC",
                              "type": "bar",
                              "data": ${controller.tcValues},
                              "label": {
                                        "show": true,
                                        "position": "top",
                                        "formatter": function (params) {
                                            return params.value > 0 ? params.value : '';
                                          }
                                      },
                            },
                            {
                              "name": "PC",
                              "type": "bar",
                              "data": ${controller.pcValues},
                              "label": {
                                        "show": true,
                                        "position": "top",
                                        "formatter": function (params) {
                                          return params.value > 0 ? params.value : '';
                                        }
                                      },
                            }
                          ]

                  }
                ''',
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
