import '../controller/home_controller.dart';
import '../models/imgtwentyone_item_model.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';

// ignore: must_be_immutable
class ImgtwentyoneItemWidget extends StatelessWidget {
  ImgtwentyoneItemWidget(
    this.imgtwentyoneItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  ImgtwentyoneItemModel imgtwentyoneItemModelObj;

  var controller = Get.find<HomeController>();

  @override
  Widget build(BuildContext context) {
    return CustomImageView(
      imagePath: ImageConstant.imgImg21,
      height: 140.v,
      width: 358.h,
      radius: BorderRadius.circular(
        5.h,
      ),
    );
  }
}
