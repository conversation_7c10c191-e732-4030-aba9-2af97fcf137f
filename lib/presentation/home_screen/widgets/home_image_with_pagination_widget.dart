import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:sfm_new/presentation/home_screen/controller/home_controller.dart';
import 'package:carousel_slider/carousel_slider.dart';

class HomeImageWithPaginationWidget extends StatelessWidget {
  final HomeController controller =
      Get.put(HomeController()); 

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Obx(
          () => Container(
            width: MediaQuery.of(context).size.width,
            child: CarouselSlider.builder(
              options: CarouselOptions(
                height: 140.v,
                initialPage: 0,
                autoPlay: true,
                viewportFraction: 1.0,
                enableInfiniteScroll: false,
                scrollDirection: Axis.horizontal,
                onPageChanged: (
                  index,
                  reason,
                ) {
                  controller.sliderIndex.value = index;
                },
              ),
              itemCount: controller.bannerList.length,
              itemBuilder: (context, index, realIndex) {
                String imageUrl =
                    "${ApiClient.imgBannerBaseURL}${controller.bannerList[index]}";
                print("imageBannerUrl: ${imageUrl}");
                return SizedBox(
                  width: double.maxFinite,
                  child: CachedNetworkImage(
                    imageUrl: imageUrl,
                    fit: BoxFit.fill,
                    progressIndicatorBuilder:
                        (context, url, downloadProgress) => Container(
                      margin: EdgeInsets.only(top: 100.h, bottom: 100.h),
                      child: CircularProgressIndicator(
                        value: downloadProgress.progress,
                      ),
                    ),


                    errorWidget: (context, error, stackTrace) {
                      return Center(
                        child: Text('Error loading image'),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ),
        SizedBox(height: 8),
        Obx(
          () => Container(
            height: 7.v,
            alignment: Alignment.center,
            child: AnimatedSmoothIndicator(
              activeIndex: controller.sliderIndex.value,
              count: controller.bannerList.length,
              axisDirection: Axis.horizontal,
              effect: ScrollingDotsEffect(
                spacing: 7,
                activeDotColor: appTheme.lightBlue900,
                dotColor: appTheme.gray400,
                dotHeight: 7.v,
                dotWidth: 7.h,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
