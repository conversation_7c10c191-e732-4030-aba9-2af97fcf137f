import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/presentation/home_screen/controller/home_controller.dart';
import 'package:sfm_new/presentation/home_screen/models/order_item_model.dart';
import 'package:sfm_new/presentation/home_screen/widgets/home_item_widget.dart';

class HomeOrderHistoryWidget extends StatelessWidget {
  final HomeController controller =
      Get.put(HomeController()); 

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 100.v,
      child: Obx(
        () => ListView.separated(
          padding: EdgeInsets.only(
            left: 2.h,
            right: 1.h,
          ),
          scrollDirection: Axis.horizontal,
          separatorBuilder: (
            context,
            index,
          ) {
            return SizedBox(
              width: 4.h,
            );
          },
          itemCount: controller.homeModelObj.value.orderItemList.value.length,
          itemBuilder: (context, index) {
            OrderItemModel model =
                controller.homeModelObj.value.orderItemList.value[index];
            return GestureDetector(
              onTap: () {
                print("Order item pressed");
                EasyLoading.show(status: 'Loading...');
                if (index == 0) {
                  Get.toNamed(AppRoutes.superStockistScreen);
                } else {
                  Get.toNamed(AppRoutes.orderHistoryScreen);
                }
              },
              child: HomeItemWidget(
                model,
              ),
            );
          },
        ),
      ),
    );
  }
}
