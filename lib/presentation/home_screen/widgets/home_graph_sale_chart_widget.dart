import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/presentation/home_screen/controller/home_controller.dart';
import 'package:flutter_echarts/flutter_echarts.dart';
import 'package:sfm_new/presentation/home_screen/widgets/graph_theme_script.dart';

class HomeGraphSaleChartWidget extends StatelessWidget {
  final HomeController controller = Get.put(HomeController());


  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 1.h),
      padding: EdgeInsets.symmetric(vertical: 5.v),
      decoration: AppDecoration.outlineBlack900.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder6,
      ),
      child: Obx(
        () => Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: 6.v),
            Padding(
              padding: EdgeInsets.only(
                left: 21.h,
                right: 11.h,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "lbl_total_sale_amount".tr,
                        style: CustomTextStyles.titleSmallBlack900Bold,
                      ),
                      Row(
                        children: [
                          Text(
                            "₹${controller.totalSalesAmount.value.toStringAsFixed(2)}",
                            style: CustomTextStyles.titleLargeLight,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(height: 6.v),
            
            Container(
              height: 165,
              child: Echarts(
                extensions: [darkThemeScript],
                captureAllGestures: true,
                theme: 'dark',
                option: '''
                  {
                    grid: {
                      left: '3%',
                      right: '8%',
                      bottom: '2%',
                      top: '10%',
                      containLabel: true
                    },
                    xAxis: {
                      type: 'category',
                      data: ${controller.extractDay(controller.extractDates())}
                    },
                    yAxis: {
                      type: 'value'
                    },
                    tooltip: {
                        trigger: 'axis',
                        position: function (pt, params, dom, rect, size) {
                          // Get the total number of data points
                          var dataLength = ${controller.extractDay(controller.extractDates())}.length;
                          
                          // Determine the index of the current point
                          var index = params[0].dataIndex;
                          
                          // Define positioning logic
                          if (index === 0) {
                            // First bar: position tooltip on the right
                            return [pt[0] + 10, '10%'];
                          } else if (index === dataLength - 1 || index === dataLength - 2) {
                            // Last bar: position tooltip on the left
                            return [pt[0] - size.contentSize[0] - 10, '10%'];
                          } else {
                            // Default positioning
                            return [pt[0], '10%'];
                          }
                        }
                      },
                    series: [
                      {
                        name: 'Sales',
                        type: 'bar',
                        stack: 'total',
                        data: ${controller.extractTotalSales()}
                      },
                    ]
                  }
                ''',
              ),
            ),
          ],
        ),
      ),
    );
  }
}
