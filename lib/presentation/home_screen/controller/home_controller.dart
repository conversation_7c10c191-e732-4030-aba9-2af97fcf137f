// ignore_for_file: unnecessary_null_comparison, unused_local_variable, invalid_use_of_protected_member, body_might_complete_normally_nullable

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:geolocator/geolocator.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:sfm_new/sfmDatabase/models/expense_type_model.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/home_screen/models/home_model.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/call_model.dart';
import 'package:sfm_new/sfmDatabase/models/config_model.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:restart_app/restart_app.dart';
import 'package:sfm_new/sfmDatabase/models/target_model.dart';
import 'package:traccar_flutter/entity/traccar_configs.dart';
import 'package:traccar_flutter/traccar_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:uuid/uuid.dart';
import 'package:package_info_plus/package_info_plus.dart';

class StackedBarData {
  final String label;
  final double value;

  const StackedBarData(this.label, this.value);
}

class HomeController extends GetxController {
  Rx<HomeModel> homeModelObj = HomeModel().obs;

  Rx<int> sliderIndex = 0.obs;

  SelectionPopupModel? selectedDropDownValue;

  var isSearching = false.obs;

  final Dio dio = Dio();
  RxDouble lat = 0.0.obs;
  RxDouble long = 0.0.obs;
  RxDouble accuracy = 0.0.obs;
  RxDouble requiredAccuracy = 0.0.obs;

  RxString token = ''.obs;
  RxString employeeCode = ''.obs;
  RxString employeeName = ''.obs;

  final RxBool isLoading = false.obs;

  RxString locationMessage = ''.obs;

  var selectedOption = RxString('Skip');
  var showTextField = RxBool(false);
  var textFieldValue = RxString('');

  final Connectivity _connectivity = Connectivity();

  bool isOrderCallAPISuccess = false;
  RxString fetchedToken = ''.obs;

  int syncDays = 0;

  List<Map<String, dynamic>> graphFilterList = [
    {'key': '7', 'value': 'Last 7 days'},
    {'key': '15', 'value': 'Last 15 days'},
    {'key': '30', 'value': 'Last 30 days'},
  ];

  List<Map<String, dynamic>> graphData = [];

  final Color leftBarColor = Colors.yellow;
  final Color rightBarColor = Colors.red;
  final Color avgColor = Colors.blue;

  final double width = 7;

  late List<BarChartGroupData> rawBarGroups;
  late List<BarChartGroupData> showingBarGroups;

  final List<double> totalCalls = [100, 150, 200, 180];
  final List<double> positiveCalls = [40, 60, 80, 70];
  final List<double> negativeCalls = [60, 90, 120, 110];

  final RxList<BarChartGroupData> barChartData = RxList<BarChartGroupData>([]);

  RxList<String> dateList = <String>[].obs;

  RxList<CallData> callList = <CallData>[].obs;
  RxList<CallData> filteredCallList = <CallData>[].obs;

  Map<String, dynamic> totalSalesMap = {};
  Map<String, dynamic> totalTCMap = {};
  Map<String, dynamic> totalPCMap = {};

  RxList<Map<String, dynamic>> totalSalesGraph = <Map<String, dynamic>>[].obs;
  RxList<Map<String, dynamic>> totalTCPCGraph = <Map<String, dynamic>>[].obs;

  List<double> totalSalesList = [];

  RxDouble totalSalesAmount = 0.0.obs;

  RxDouble totalTCCount = 0.0.obs;
  RxDouble totalPCCount = 0.0.obs;

  RxDouble todaySalesAmount = 0.0.obs;
  RxList<Config> configList = <Config>[].obs;

  RxList<String> bannerList = <String>[].obs;

  RxBool isBannerDataValid = false.obs;

  RxString internetConnectivity = "".obs;

  RxBool _downloading = false.obs;
  RxString _filePath = ''.obs;

  bool get downloading => _downloading.value;
  String get filePath => _filePath.value;
  RxString selectedExpenseTypeName = "".obs;

  RxBool storagePermissionStatus = false.obs;

  RxList<ExpenseType> expenseTypeList = <ExpenseType>[].obs;

  RxBool isExpenseTypeSelected = false.obs;

  RxInt selectedExpenseType = 0.obs;

  RxInt showPayment = 0.obs;
  RxInt showExpense = 0.obs;
  RxInt showLeave = 0.obs;
  RxInt showComplain = 0.obs;
  RxInt showReport = 0.obs;
  RxInt showTeamReport = 0.obs;
  RxInt showAddBeat = 0.obs;

  RxInt showTCPCChart = 0.obs;

  RxString imgAppLogo = ''.obs;

  var localizedStrings = <String, String>{}.obs;

  RxString addNewBeatURL = "".obs;
  var isWebViewLoading = true.obs;
  var hasError = false.obs;

  RxDouble progress = 0.0.obs;
  setProgress(int newProgress) {
    progress.value = newProgress / 100;
  }

  get totalSales => null;

  RxList<String> months = <String>[].obs;
  RxList<int> salesTC = <int>[].obs;
  RxList<int> salesPC = <int>[].obs;

  var installedVersion = ''.obs;
  var latestVersion = ''.obs;

  var isLogoutLoading = false.obs;

  late InAppWebViewController webViewController;
  RxString url = ''.obs;
  RxDouble urlProgress = 0.0.obs;

  RxList<Target> targetList = <Target>[].obs;

  List<int> tcValues = [];
  List<int> pcValues = [];

  final traccar = TraccarFlutter();

  // Observable to store service logs (optional)
  final RxString serviceLogs = ''.obs;

  RxString companyName = ''.obs;

  RxInt traccarMode = 0.obs;
  RxString tracServerURL = "".obs;
  RxInt tracInterval = 0.obs;
  RxInt tracDistance = 0.obs;

  RxString apkURL = "".obs;

  late Config traccarJsonData;

  @override
  void onInit() async {
    super.onInit();

    companyName.value = "lbl_app_name".tr;

    EasyLoading.dismiss();

    getLocation();

    fetchToken();
    // loadLocalizedStrings();

    int isAppRestart =
        await SharedPrefManager.instance.getInt(ConstantValues.isAppRestart) ??
            0;
    print("isAppRestart: ${isAppRestart}");

    if (isAppRestart != 1) {
      restartApp();
    }

    await fetchEmployeeData();
    await fetchTokenData();
    EasyLoading.dismiss();

    await checkInternetConnectivity();

    await fetchData();
    dateList.value = getLast7Days();

    await fetchCalls();
    print("dateList: ${dateList}");

    await fetchConfig();
    await requestStoragePermission();

    await checkAppVersion();

    await fetchStoredData();

    await fetchTarget();

    addNewBeatURL.value =
        ApiClient.baseURL + ApiClient.getAddManualBeatEndpoint;

    Map<String, String> headers = {
      'Authorization': 'Bearer ${fetchedToken.value}',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    };
  }

  Future<void> startTraccarService({
    required String deviceId,
    required String serverUrl,
    required int interval,
    required int distance,
    AccuracyLevel accuracy = AccuracyLevel.medium,
    bool offlineBuffering = true,
    bool wakelock = true,
  }) async {
    try {
      // Initialize the service
      await traccar.initTraccar();

      // Set configurations
      final configs = TraccarConfigs(
        deviceId: deviceId,
        serverUrl: serverUrl,
        interval: interval, //(in milliseconds - 10000 = 10 seconds)
        accuracy: accuracy,
        distance: distance,
        angle: 0,
        offlineBuffering: offlineBuffering,
        wakelock: wakelock,
      );

      await traccar.setConfigs(configs);

      print("configs.deviceId: ${configs.deviceId}");
      print("configs.serverUrl: ${configs.serverUrl}");

      // Start the tracking service
      await traccar.startService();

      // // Fetch and store service logs (optional)
      // final logs = await traccar.showStatusLogs();
      // serviceLogs.value = logs ?? "";
      // print('Service Logs: $logs');
    } catch (e) {
      // Handle errors
      print('Error initializing Traccar service: $e');
    }
  }

  Future<void> fetchTarget() async {
    targetList.value = await SharedPrefManager.instance.getTargets();
    if (targetList.isNotEmpty) {
      print('Retrieved targets:');
      targetList.forEach((target) {
        print(target.toJson());
      });
    } else {
      print('No targets stored in SharedPreferences.');
    }

    print("retrievedTargets: ${targetList}");
    EasyLoading.dismiss();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void setController(InAppWebViewController controller) {
    webViewController = controller;
  }

  void onLoadStart(String newUrl) {
    url.value = newUrl;
  }

  void onLoadStop(String newUrl) {
    url.value = newUrl;
  }

  void onProgressChanged(int newProgress) {
    progress.value = newProgress / 100;
  }

  Future<void> goBack() async {
    if (await webViewController.canGoBack()) {
      webViewController.goBack();
    }
  }

  Future<void> goForward() async {
    if (await webViewController.canGoForward()) {
      webViewController.goForward();
    }
  }

  void reload() {
    webViewController.reload();
  }

  // Handle page loading state
  void setLoading(bool value) {
    isWebViewLoading.value = value;
  }

  // Handle errors
  void setError(bool value) {
    hasError.value = value;
  }

  // Function to get the installed app version
  Future<void> getInstalledVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    installedVersion.value = packageInfo.version;
    print("installedVersion: ${installedVersion.value}");
  }

  // Check if the app version is outdated
  Future<void> checkAppVersion() async {
    await getInstalledVersion();

    print("installedVersion: ${installedVersion.value}");
    print("latestVersion: ${latestVersion.value}");
     if (_isOutdatedVersion(installedVersion.value, latestVersion.value)) {
    SharedPrefManager.instance.setInt(ConstantValues.isUsingOldVersion, 1);
    showUpdateDialog();
    } else {
    SharedPrefManager.instance.setInt(ConstantValues.isUsingOldVersion, 0);
    }
  }

  // Compare installed version with the latest version
  bool _isOutdatedVersion(String installed, String latest) {
    // List<String> installedParts = installed.split('.');
    // print(installedParts);
    // List<String> latestParts = latest.split('.');
    // print(latestParts);

    // for (int i = 0; i < latestParts.length; i++) {
    //   if (int.parse(installedParts[i]) < int.parse(latestParts[i])) {
    //     return true;
    //   }
    // }
    // return false;
    try {
      List<String> installedParts =
          installed.split('.').map((e) => e.trim()).toList();
      List<String> latestParts =
          latest.split('.').map((e) => e.trim()).toList();

      print("Installed Parts: $installedParts");
      print("Latest Parts: $latestParts");

      for (int i = 0; i < latestParts.length; i++) {
        int installedPart = (i < installedParts.length)
            ? int.tryParse(installedParts[i]) ?? 0
            : 0;
        int latestPart = int.tryParse(latestParts[i]) ?? 0;

        if (installedPart < latestPart) {
          return true;
        } else if (installedPart > latestPart) {
          return false;
        }
      }
    } catch (e) {
      print("Error in _isOutdatedVersion: $e");
    }

    return false;
  }

  void showUpdateDialog() async {
    var googleDriveLink = apkURL.value;

    Future<void> openDownloadLink() async {
      // Check internet connection
      var connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        Get.snackbar(
          'No Internet',
          'Please check your internet connection and try again.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.redAccent,
          colorText: Colors.white,
        );
        return;
      }

      // Launch the Google Drive link
      if (await canLaunchUrl(Uri.parse(googleDriveLink))) {
        await launchUrl(Uri.parse(googleDriveLink),
            mode: LaunchMode.externalApplication);
      } else {
        Get.snackbar(
          'Error',
          'Could not open the link.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.redAccent,
          colorText: Colors.white,
        );
      }
    }

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Title
              Text(
                'Update Available',
                style: CustomTextStyles.titleSmallBluegray900,
              ),
              SizedBox(height: 15.0),
              Text(
                'A new version of the app is available. Please update to the latest version.',
                style: CustomTextStyles.titleSmallBluegray900,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 15.0),
              SizedBox(
                width: 250,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 110.0,
                      height: 40.0,
                      child: ElevatedButton(
                        onPressed: () async {
                          await openDownloadLink();
                        },
                        child: Text(
                          'Download',
                          style: CustomTextStyles.titleSmallBluegray900logout,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> restartApp() async {
    SharedPrefManager.instance.setInt(ConstantValues.isAppRestart, 1);
    await Restart.restartApp();
  }

  Future<void> fetchStoredData() async {
    try {
      String? tcPcData =
          await SharedPrefManager.instance.getString(ConstantValues.TCPC);
      print("tcPcData: $tcPcData");

      if (tcPcData != null && tcPcData.isNotEmpty) {
        try {
          Map<String, dynamic> tcPcMap =
              jsonDecode(tcPcData) as Map<String, dynamic>;

          print("tcPcMap: ${tcPcMap}");

          months.value = [
            tcPcMap['previous_month']['month'],
            tcPcMap['current_month']['month']
          ];
          salesTC.value = [
            tcPcMap['previous_month']['tc'],
            tcPcMap['current_month']['tc']
          ];
          salesPC.value = [
            tcPcMap['previous_month']['pc'],
            tcPcMap['current_month']['pc']
          ];
        } catch (e) {
          print("Error parsing tcPcData: $e");
          // Handle parsing error
          setDefaultValues();
        }
      } else {
        setDefaultValues();
      }

      print("months: ${months}");
      print("salesTC: ${salesTC}");
      print("salesPC: ${salesPC}");
    } catch (e) {
      print("Error fetching data from Shared Preferences: $e");
      // Handle error while fetching from Shared Preferences
      setDefaultValues();
    }

    update();
  }

  void setDefaultValues() {
    months.value = ["", ""];
    salesTC.value = [0, 0];
    salesPC.value = [0, 0];
  }

  String get monthsJson => jsonEncode(months.value);
  String get salesTCJson => jsonEncode(salesTC.value);
  String get salesPCJson => jsonEncode(salesPC.value);

  List<String> extractMonthValue(RxList<String> month) {
    print("month: ${month}");
    List<String> monthList = month.map((date) {
      // Split the date string by '-' and take the last part
      List<String> parts = date.split('-');
      print("parts: ${parts}");
      print("parts.last: ${parts.last}");
      return parts.last;
    }).toList();
    print("monthList: ${monthList}");
    return monthList;
  }

  List<int> extractSalesTC() {
    return salesTC.value;
  }

  Future<void> requestStoragePermission() async {
    final DeviceInfoPlugin info = DeviceInfoPlugin();
    final AndroidDeviceInfo androidInfo = await info.androidInfo;
    print('releaseVersion : ${androidInfo.version.release}');
    final String androidVersion = "${androidInfo.version.release}";
    print("androidVersion: ${androidVersion}");
    bool havePermission = false;

    int comparison = compareAndroidVersions(androidVersion, "11");
    print("comparison: ${comparison}");

    if (comparison > 0) {
      final request = await [
        Permission.manageExternalStorage,
      ].request();

      havePermission =
          request.values.every((status) => status == PermissionStatus.granted);
      print("havePermission: ${havePermission}");
      storagePermissionStatus.value = havePermission;
    } else {
      final status = await Permission.storage.request();
      print("else status: ${status}");
      havePermission = status.isGranted;
      print("havePermission: ${havePermission}");
      storagePermissionStatus.value = havePermission;
      print("storagePermissionStatus: ${storagePermissionStatus}");
    }

    if (!havePermission) {
      Get.dialog(
        AlertDialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.transparent,
          title: Text(
            'Permission Denied',
            style: CustomTextStyles.titleMediumPrimary.copyWith(
              color: theme.colorScheme.primary,
            ),
          ),
          content: Text('Please allow permission to create log file'),
          actions: [
            TextButton(
              onPressed: () async {
                await openAppSettings();
                Get.back();
              },
              child: Text(
                'Open Settings',
                style: CustomTextStyles.bodyMediumPoppinsBluegray900.copyWith(
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  int compareAndroidVersions(String version1, String version2) {
    // Split version strings into their components
    List<String> components1 = version1.split('.');
    List<String> components2 = version2.split('.');

    // Parse major version numbers
    int majorVersion1 = int.parse(components1[0]);
    int majorVersion2 = int.parse(components2[0]);

    // Assume minor and maintenance release versions are zero for single-number version
    int minorVersion1 = components1.length > 1 ? int.parse(components1[1]) : 0;
    int minorVersion2 = components2.length > 1 ? int.parse(components2[1]) : 0;
    int maintenanceVersion1 =
        components1.length > 2 ? int.parse(components1[2]) : 0;
    int maintenanceVersion2 =
        components2.length > 2 ? int.parse(components2[2]) : 0;

    // Compare major version numbers
    if (majorVersion1 < majorVersion2) {
      return -1;
    } else if (majorVersion1 > majorVersion2) {
      return 1;
    }

    // If major version numbers are equal, compare minor version numbers
    if (minorVersion1 < minorVersion2) {
      return -1;
    } else if (minorVersion1 > minorVersion2) {
      return 1;
    }

    // If minor version numbers are equal, compare maintenance release version numbers
    if (maintenanceVersion1 < maintenanceVersion2) {
      return -1;
    } else if (maintenanceVersion1 > maintenanceVersion2) {
      return 1;
    }

    return 0;
  }

  void updateTraccarData(Map<String, dynamic> jsonData) {
    try {
      if (jsonData.containsKey('traccar_mode')) {
        traccarMode.value = jsonData['traccar_mode'] ?? 0;
      }
      if (jsonData.containsKey('server_url')) {
        tracServerURL.value = jsonData['server_url'] ?? "";
      }
      if (jsonData.containsKey('interval')) {
        tracInterval.value = jsonData['interval'] ?? 0;
      }
      if (jsonData.containsKey('distance')) {
        tracDistance.value = jsonData['distance'] ?? 0;
      }

      print("Traccar Mode: ${traccarMode.value}");
      print("Server URL: ${tracServerURL.value}");
      print("Interval: ${tracInterval.value}");
      print("Distance: ${tracDistance.value}");

      String formattedString = "${companyName.value}_${employeeCode.value}"
          .replaceAll(' ', '_')
          .toUpperCase();

      startTraccarService(
          deviceId: formattedString,
          serverUrl: "${tracServerURL.value}",
          interval: tracInterval.value,
          distance: tracDistance.value);
    } catch (e) {
      print("Error updating Traccar data: $e");
    }
  }

  Future<void> fetchConfig() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableConfig}");
    configList.value = List.generate(maps.length, (index) {
      return Config.fromJson(maps[index]);
    });
    print("configList: ${configList}");

    if (configList.isNotEmpty) {
      final amountReqImg = getConfigValue('banner');
      final traccarConfig = getConfigValue('traccar');
      print("traccarConfig: ${traccarConfig}");

      if (traccarConfig!.config_value != null) {
        final jsonData = jsonDecode(traccarConfig.config_value!);
        print("jsonData: ${jsonData}");
        updateTraccarData(jsonData);
      }

      showPayment.value = safelyFetchShowOptionValues("show_payment");
      showExpense.value = safelyFetchShowOptionValues("show_expense");
      showLeave.value = safelyFetchShowOptionValues("show_leave");
      showComplain.value = safelyFetchShowOptionValues("show_complain");
      showTeamReport.value = safelyFetchShowOptionValues("show_team_report");
      showReport.value = safelyFetchShowOptionValues("show_report");
      showAddBeat.value = safelyFetchShowOptionValues("add_route_plan");
      showTCPCChart.value = safelyFetchShowOptionValues("tc_pc_chart");

      final imgLogo = getConfigValue('logo');
      if (imgLogo != null) {
        imgAppLogo.value = "${imgLogo.config_value}";
        print("imgAppLogo.value: ${imgAppLogo.value}");

        SharedPrefManager.instance
            .setString(ConstantValues.loginAppLogo, imgAppLogo.value);
      }

      SharedPrefManager.instance
          .setInt(ConstantValues.showPayment, showPayment.value);

      final accuracyData = getConfigValue('accepted_accuracy');
      final appVersion = getConfigValue('latest_app_version');
      final newAPK = getConfigValue("new_apk");
      print("newAPK: ${newAPK}");

      if (newAPK != null) {
        apkURL.value = "${newAPK.config_value}";
      }

      if (appVersion != null) {
        latestVersion.value = "${appVersion.config_value}";
      }

      if (accuracyData != null) {
        double doubleNumber =
            double.tryParse("${accuracyData.config_value}") ?? 500;
        print("accuracyData: ${accuracyData.config_value}");

        SharedPrefManager.instance
            .setDouble(ConstantValues.requiredAccuracy, doubleNumber);
        requiredAccuracy.value = doubleNumber;
        print("requiredAccuracy.value: ${requiredAccuracy.value}");
      }

      print("showPayment: ${showPayment.value}");
      print("showExpense: ${showExpense.value}");
      print("showLeave: ${showLeave.value}");
      print("showComplain: ${showComplain.value}");
      print("showReport: ${showReport.value}");
      print("showTeamReport: ${showTeamReport.value}");
      print("showAddBeat: ${showAddBeat.value}");
      print("showTCPCChart: ${showTCPCChart.value}");

      if (amountReqImg != null) {
        print('banner fetched');
        print("banner: ${amountReqImg.config_value}");

        String imageUrlWithoutQuotes =
            amountReqImg.config_value!.replaceAll('"', '');
        bannerList.value = imageUrlWithoutQuotes.split(',');
        print('bannerArray: ${bannerList}');

        if (bannerList.isEmpty) {
          isBannerDataValid.value = false;
        }
        checkBannerListValidity(bannerList.value);
      }
    }
  }

  /// Helper function to fetch a configuration by key safely.
  Config? getConfigValue(String key) {
    try {
      return configList.firstWhere(
        (config) => config.config_key == key,
        orElse: () => Config(config_id: 0),
      );
    } catch (e) {
      print("Error fetching config for key '$key': $e");
      return null;
    }
  }

  int safelyFetchShowOptionValues(String configKey) {
    try {
      final optionType = configList.firstWhere(
        (config) => config.config_key == configKey,
      );
      return int.tryParse("${optionType.config_value}") ?? 0;
    } catch (e) {
      print("Error fetching $configKey: $e");
      return 0;
    }
  }

  String safelyFetchCompanyValue(String configKey) {
    try {
      final optionType = configList.firstWhere(
        (config) => config.config_key == configKey,
      );
      return "${optionType.config_value}";
    } catch (e) {
      print("Error fetching $configKey: $e");
      return "";
    }
  }

  int? getExpenseCodeFromTypeName(String selectedType) {
    try {
      final expenseType = expenseTypeList.firstWhere(
        (ccType) => ccType.expenseName == selectedType,
      );
      return expenseType.id;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getExpenseCodeFromTypeName: $e');
      }
      return null;
    }
  }

  String generateUUID() {
    var uuid = Uuid();
    return uuid.v4();
  }

  Future<void> addExpenseAPI(Map<String, dynamic> expense) async {
    EasyLoading.show(status: 'Loading...');
    try {
      Map<String, dynamic> data = {};

      data = {
        "em_expense_type": "${expense['em_expense_type']}",
        "em_code": "${expense['em_code']}",
        "em_expense_detail": "${expense['em_expense_detail']}",
        "em_amount": "${expense['em_amount']}",
        "em_approved_amount": "${expense['em_approved_amount']}",
        "em_bill_picture": "${expense['em_bill_picture']}",
        "file_extension": "${expense['file_extension']}",
        "latitude": "${expense['em_lat']}",
        "longitude": "${expense['em_long']}",
        "emp_code": "${expense['emp_code']}",
        "em_expense_status": "${expense['em_expense_status']}",
        "em_date": "${expense['em_date']}",
        "em_km": "${expense['em_km']}",
      };

      print("data: ${data}");

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${fetchedToken.value}',
      };

      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.postAddExpenseEndpoint),
        data: expense,
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.data);

      if (response.data != null && response.statusCode == 200) {
        EasyLoading.dismiss();

        SharedPrefManager.instance.setBool(ConstantValues.isDailyLogOut, true);

        await traccar.stopService();

        Get.toNamed(AppRoutes.syncDataScreen);
      } else {
        print('Response data is null');
        EasyLoading.dismiss();
      }
    } on DioException catch (e) {
      String errorMessage;

      if (e.response != null) {
        errorMessage =
            'Error ${e.response!.statusCode}: ${e.response!.statusMessage}';
        print(errorMessage);
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        }
      } else {
        errorMessage = 'Network error: ${e.message}';
        print(errorMessage);
      }

      await ApiLogger.logError(
        apiName: ApiClient.postAddExpenseEndpoint,
        apiRequestData: expense,
        apiResponse: errorMessage,
      );
      EasyLoading.dismiss();
    } finally {
      EasyLoading.dismiss();
    }
  }

  Future<void> checkBannerListValidity(List<String> bannerList) async {
    for (String imageUrl in bannerList) {
      String imageUrlWithoutQuotes = imageUrl.replaceAll('"', '');

      final Uri url =
          Uri.parse(ApiClient.imgBannerBaseURL + imageUrlWithoutQuotes);
      print("imageURL: ${url}");

      try {
        if (await canLaunchUrl(url)) {
          print('Launched URL: $imageUrl');
        } else {
          print('Could not launch URL: $imageUrl');
          isBannerDataValid.value = false;
        }
      } on PlatformException catch (e) {
        print('Error launching URL: $e');
      } catch (e) {
        print('Unexpected error: $e');
      }
    }
  }

  String formatDate(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    String formattedDate =
        "${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}";
    return formattedDate;
  }

  Future<void> fetchCalls() async {
    // grandTotalAmount.value = 0.0;
    isLoading(true);
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCall}");
    callList.value = List.generate(maps.length, (index) {
      return CallData.fromMap(maps[index]);
    });

    print("callList.length: ${callList.length}");
    print("callList: ${callList}");
    isLoading(false);

    for (CallData call in callList) {
      String callDate = formatDate(call.callCreatedAt!);

      if (dateList.contains(callDate)) {
        totalSalesMap.update(
          callDate,
          (value) => value + call.callGrandTotal,
          ifAbsent: () => call.callGrandTotal,
        );

        totalTCMap.update(
          callDate,
          (value) => value + 1,
          ifAbsent: () => 1,
        );

        if (call.callOrderType == 1) {
          totalPCMap.update(
            callDate,
            (value) => value + 1,
            ifAbsent: () => 1,
          );
        }
      }
    }

    print("totalSalesMap: ${totalSalesMap}");
    print("totalTCMap: ${totalTCMap}");
    print("totalPCMap: ${totalPCMap}");

    List<Map<String, dynamic>> data = [];

    totalSalesAmount.value = 0.0;
    totalTCCount.value = 0.0;
    totalPCCount.value = 0.0;

    print("dateList: ${dateList}");

    for (String date in dateList) {
      print(date);
      double totalSales = totalSalesMap[date] ?? 0;
      int totalTC = totalTCMap[date] ?? 0;
      int totalPC = totalPCMap[date] ?? 0;

      Map<String, dynamic> dateData = {
        'date': date,
        'totalSales': totalSales,
      };

      Map<String, dynamic> tcpcData = {
        'date': date,
        'tc': totalTC,
        'pc': totalPC,
      };

      totalSalesAmount.value += totalSales;
      totalSalesGraph.add(dateData);

      totalTCCount.value += totalTC;
      totalPCCount.value += totalPC;
      totalTCPCGraph.add(tcpcData);
    }

    print("totalSalesGraph: ${totalSalesGraph}");
    print("totalTCPCGraph: ${totalTCPCGraph}");

    for (var entry in totalTCPCGraph) {
      tcValues.add(entry["tc"]);
      pcValues.add(entry["pc"]);
    }

    print("TC Values: $tcValues");
    print("PC Values: $pcValues");

    // return data;
  }

  // Method to extract total sales
  List<double> extractTotalSales() {
    print("totalSalesGraph: ${totalSalesGraph}");
    return totalSalesGraph
        .map<double>((item) => item['totalSales'] as double)
        .toList();
  }

  // Method to extract dates
  List<String> extractDates() {
    return totalSalesGraph
        .map<String>((item) => item['date'] as String)
        .toList();
  }

  List<String> extractDay(List<String> dates) {
    List<String> dayList = dates.map((date) {
      List<String> parts = date.split('-');
      return parts.last;
    }).toList();
    print("dayList: ${dayList}");
    return dayList;
  }

  Future<void> fetchData() async {
    // ... your logic to fetch data
    // For example:
    final data = [
      StackedBarData("A", 20),
      StackedBarData("B", 30),
      StackedBarData("C", 15),
    ];

    // Convert data to BarChartGroupData
    final groups = convertDataToGroups(data);
    barChartData.value = groups;
    print("barChartData: ${barChartData}");
  }

  void onClose() {
    super.onClose();
  }

  List<String> getLast7Days() {
    List<String> last7Days = [];
    DateTime currentDate = DateTime.now();

    // Loop through the last 7 days
    for (int i = 0; i < 7; i++) {
      // Subtract i days from the current date
      DateTime date = currentDate.subtract(Duration(days: 6 - i));
      // Format the date to yyyy-MM-dd format
      String formattedDate =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      last7Days.add(formattedDate);
    }

    return last7Days;
  }

  List<BarChartGroupData> convertDataToGroups(List<StackedBarData> data) {
    List<BarChartGroupData> groups = [];
    for (var item in data) {
      groups.add(
        BarChartGroupData(
          x: data.indexOf(item),
          barRods: [
            BarChartRodData(
              toY: item.value,
              color: Colors.blue,
            ),
          ],
        ),
      );
    }
    return groups;
  }

  Map<String, dynamic> getDataForDate(DateTime date) {
    final random = Random();
    return {
      'date': DateFormat('yyyy-MM-dd').format(date),
      'value': random.nextInt(100),
    };
  }

  // Function to get data for the last 7 days
  List<Map<String, dynamic>> getLast7DaysData() {
    final List<Map<String, dynamic>> graphData = [];
    final now = DateTime.now();
    final DateFormat formatter = DateFormat('yyyy-MM-dd');

    // Loop through the last 7 days
    for (int i = 0; i < 7; i++) {
      final date = now.subtract(Duration(days: i));
      final dataForDate = getDataForDate(date);
      graphData.add(dataForDate);
    }
    print("graphData: ${graphData}");
    return graphData;
  }

  Future<String?> fetchEmployeeData() async {
    // final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.employee) ??
            "";
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];
      employeeCode.value = empCode!;

      final String? empName = employeeData['emp_name'];
      employeeName.value = empName!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  onSelected(dynamic value) {
    for (var element in homeModelObj.value.dropdownItemList.value) {
      element.isSelected = false;
      if (element.id == value.id) {
        element.isSelected = true;
      }
    }
    homeModelObj.value.dropdownItemList.refresh();
  }

  Future<void> getLocation() async {
    Position position;
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();

    if (!serviceEnabled) {
      locationMessage.value = "Location services are disabled";
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        locationMessage.value = 'Location permissions are denied.';
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      locationMessage.value =
          'Location permissions are permanently denied, we cannot request permissions.';
      return;
    }

    if (permission == LocationPermission.unableToDetermine) {}

    position = await Geolocator.getCurrentPosition();
    locationMessage.value =
        'Latitude: ${position.latitude}, Longitude: ${position.longitude}';

    lat.value = position.latitude;
    long.value = position.longitude;
    accuracy.value = position.accuracy;
    print("accuracy.value: ${accuracy.value}");
  }

  //Method to fetch the token asynchronously
  void fetchToken() async {
    String? fetchedToken =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
    if (fetchedToken != null) {
      token.value = getTokenFromInput(fetchedToken);
    }
  }

  String getTokenFromInput(String input) {
    List<String> parts = input.split('|');
    if (parts.length > 1) {
      print("parts: ${parts}");
      return parts[1];
    } else {
      return input;
    }
  }

  Future<void> checkout() async {
    try {
      isLoading.value = true;
      print("accuracy checkout home: ${accuracy.value}");
      print("token.value: {token.value}");
      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${fetchedToken.value}',
      };
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.checkoutEndpoint),
        data: {
          'latitude': '${lat.value}',
          'longitude': '${long.value}',
        },
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.data);
      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            SharedPrefManager.instance
                .setBool(ConstantValues.isDailyLogOut, true);
            showToastMessage("Logged out sucessfully");
            await traccar.stopService();
            Get.toNamed(AppRoutes.syncDataScreen);
          }
        } else {
          print('Response data is null');
        }
      } catch (e) {
        print('Error parsing response data: $e');
      }
    } catch (e) {
      print(e);
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> checkInternetConnectivity() async {
    print("checkInternetConnectivity called");
    var connectivityResult = await _connectivity.checkConnectivity();
    if (connectivityResult != ConnectivityResult.none) {
      syncCallsWithServer();
    }
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> syncCallsWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedCalls = await db.query(
        '${TableValues.tableCall}',
        where: '${TableValues.callSyncStatus} = ?',
        whereArgs: [0]);

    if (unsyncedCalls.isNotEmpty) {
      print('Syncing calls with the server...');
      print("unsyncedcalls: ${unsyncedCalls}");

      for (final call in unsyncedCalls) {
        try {
          await syncOrdersWithServer(call);
        } catch (error) {
          print(
              'Error syncing call with ID ${call['${TableValues.callCode}']}: $error');
        }
      }
    } else {
      print('No calls to sync.');
    }
  }

  Future<void> syncOrdersWithServer(Map<String, dynamic> call) async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedOrders = await db.query(
        '${TableValues.tableOrder}',
        where: '${TableValues.orderProductSyncStatus} = ?',
        whereArgs: [0]);

    int? callOrderType = call['${TableValues.callOrderType}'];
    print("callOrderType: ${callOrderType}");

    if (callOrderType == 1) {
      if (unsyncedOrders.isNotEmpty) {
        print('Syncing orders with the server...');
        print("unsyncedorders: ${unsyncedOrders}");

        String? callCode = call['${TableValues.callCode}'];
        print("callCode: ${callCode}");

        List<Map<String, dynamic>> filteredOrders =
            unsyncedOrders.where((order) {
          return order[TableValues.orderCallCode] == callCode;
        }).toList();
        print("filteredOrders Home: ${filteredOrders}");

        if (filteredOrders.length > 0) {
          await placeOrder(call, filteredOrders);

          if (isOrderCallAPISuccess == true) {
            await db.update('${TableValues.tableCall}',
                {'${TableValues.callSyncStatus}': 1},
                where: '${TableValues.callCode} = ?',
                whereArgs: [call['${TableValues.callCode}']]);
            print(
                'Call with ID ${call['${TableValues.callCode}']} synced successfully');
          } else {
            print(
                'Error syncing call with ID ${call['${TableValues.callCode}']}');
          }
        } else {
          showToastMessage("No products found in productive order");
        }
      } else {
        showToastMessage("No unsynced products found in productive order");
      }
    } else {
      print("unsyncedorders: ${unsyncedOrders}");
      int? callType = call['${TableValues.callOrderType}'];
      print("callType: ${callType}");

      if (callType == 0) {
        List<Map<String, dynamic>> filteredOrders = [];
        await placeOrder(call, filteredOrders);

        if (isOrderCallAPISuccess == true) {
          await db.update(
              '${TableValues.tableCall}', {'${TableValues.callSyncStatus}': 1},
              where: '${TableValues.callCode} = ?',
              whereArgs: [call['${TableValues.callCode}']]);
          print(
              'Call with ID ${call['${TableValues.callCode}']} synced successfully');
        } else {
          print(
              'Error syncing call with ID ${call['${TableValues.callCode}']}');
        }
      }

      print('No orders to sync.');
    }
  }

  Future<void> fetchExpenseType() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableExpenseType}");

    ExpenseType skipExpenseType = ExpenseType(
      id: -1,
      expenseName: 'Skip',
    );

    expenseTypeList.value = List.generate(maps.length, (index) {
      print("expenseTypeList: ${expenseTypeList}");
      return ExpenseType.fromMap(maps[index]);
    });

    expenseTypeList.value.insert(0, skipExpenseType);

    print("expenseTypeList: ${expenseTypeList.value}");
  }

  Future<void> fetchTokenData() async {
    fetchedToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  Future<void> placeOrder(
      Map<String, dynamic> call, List<Map<String, dynamic>> orders) async {
    print("placeOrder order: ${orders}");

    List<Map<String, dynamic>> localProducts = [];

    for (var order in orders) {
      localProducts.add({
        "order_code": order['order_code'],
        "product_code": order['order_product_code'],
        "quantity": order['order_product_qty'],
        "pcs": order['order_piece'],
        "box": order['order_box'],
        "bunch": order['order_bunch'],
        "mrp": order['order_product_mrp'],
        "pts": order['order_product_pts'],
        "rate_basic": order['order_product_base_rate'],
        "total_basic_rate": order['order_product_total_before_tax'],
        "gst": order['order_product_tax'],
        "gst_amount": order['order_product_tax_amount'],
        "grand_total": order['order_product_total_price_with_tax'],
      });
    }

    print("localProducts final: ${localProducts}");
    Map<String, dynamic> data = {};
    try {
      Dio dio = Dio(
        BaseOptions(
          connectTimeout: Duration(seconds: 10),
          receiveTimeout: Duration(seconds: 10),
          sendTimeout: Duration(seconds: 10),
        ),
      );

      if ('${call['call_order_type']}' == '1') {
        print("is productive");
        data = {
          "call_code": call['${TableValues.callCode}'],
          "emp_code": call['${TableValues.callEmpCode}'],
          "client_code": call['${TableValues.callClientCode}'],
          "latitude": call['${TableValues.callLat}'],
          "longitude": call['${TableValues.callLong}'],
          "accuracy": call['${TableValues.callAccuracy}'],
          "total_quantity": call['${TableValues.callTotalQTY}'],
          "party_code": call['${TableValues.callPartyCode}'],
          "grand_total": call['${TableValues.callGrandTotal}'],
          "product_order_type": call['${TableValues.callOrderType}'],
          "start_time": call['${TableValues.callStartTime}'],
          "stop_time": call['${TableValues.callStopTime}'],
          "packaging_charge": call['${TableValues.callPackagingCharge}'],
          "transportation_charge":
              call['${TableValues.callTransportationCharge}'],
          "transportation_name": call['${TableValues.callTransporterName}'],
          "remarks": call['${TableValues.callRemark}'],
          "is_telephonic": call['${TableValues.callIsTelephonic}'],
          "products": localProducts,
        };
      } else {
        print("is not productive");
        data = {
          "call_code": call['${TableValues.callCode}'],
          "emp_code": call['${TableValues.callEmpCode}'],
          "client_code": call['${TableValues.callClientCode}'],
          "latitude": call['${TableValues.callLat}'],
          "longitude": call['${TableValues.callLong}'],
          "accuracy": call['${TableValues.callAccuracy}'],
          // "party_code": call['${TableValues.callPartyCode}'],
          "product_order_type": call['${TableValues.callOrderType}'],
          "start_time": call['${TableValues.callStartTime}'],
          "stop_time": call['${TableValues.callStopTime}'],
          "remarks": call['${TableValues.callRemark}'],
          "reason_id": call['${TableValues.callOrderReasonID}'],
          "is_telephonic": 0,
        };
      }

      print("data: ${data}");
      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${fetchedToken.value}',
      };

      // Send POST request
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.postAddOrderEndpoint),
        data: data,
        options: Options(
          headers: headers,
        ),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      if (response.statusCode == 200) {
        EasyLoading.dismiss();
        print('Data posted successfully');
        print('Response: ${response.data}');
        isOrderCallAPISuccess = true;
        await ApiLogger.logError(
          apiName: ApiClient.postAddOrderEndpoint,
          apiRequestData: response.data,
          apiResponse: 'Error: nil',
        );
      } else {
        print('Failed to post data');
        isOrderCallAPISuccess = false;
        EasyLoading.dismiss();
      }
    } on DioException catch (e) {
      isOrderCallAPISuccess = false;
      if (e.response != null) {
        print('Error: ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {
          print(e.response!.data["message"] ?? "No data found");
        }
      }

      await ApiLogger.logError(
        apiName: ApiClient.postAddOrderEndpoint,
        apiRequestData: data,
        apiResponse: 'Error: $e',
      );
    } finally {
      EasyLoading.dismiss();
    }
  }
}


//1721