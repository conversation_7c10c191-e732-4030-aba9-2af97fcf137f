// ignore_for_file: duplicate_import, deprecated_member_use, unused_local_variable, unused_import, unnecessary_null_comparison

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:sfm_new/presentation/home_screen/controller/home_controller.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:flutter/material.dart';

// ignore_for_file: must_be_immutable
class AddNewBeatScreen extends GetWidget<HomeController> {
  @override
  Widget build(BuildContext context) {
    HomeController homeController = Get.put(HomeController());


    print('addNewBeatURL: ${homeController.addNewBeatURL.value}');

    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(),
        body: Obx(() {
          // Show CircularProgressIndicator while loading
          if (controller.isLoading.value) {
            return Center(child: CircularProgressIndicator());
          }

          // Show error message if there's an error
          if (controller.hasError.value) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, color: Colors.red, size: 50),
                  SizedBox(height: 10),
                  Text('Failed to load webpage.',
                      style: TextStyle(fontSize: 18)),
                ],
              ),
            );
          }

          // Headers for the request
          Map<String, String> headers = {
            'Authorization': 'Bearer ${homeController.fetchedToken.value}',
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0",
          };

          return Column(
            children: [
              // Progress Indicator
              Obx(
                () => controller.progress.value < 1.0
                    ? LinearProgressIndicator(
                        value: controller.progress.value,
                      )
                    : SizedBox.shrink(),
              ),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    if (controller.webViewController != null) {
                      await controller.webViewController.reload();
                    }
                  },
                  child: Container(
                    child: InAppWebView(
                      initialUrlRequest: URLRequest(
                        url: WebUri(controller.addNewBeatURL.value),
                        headers: headers,
                      ),
                      initialOptions: InAppWebViewGroupOptions(
                        crossPlatform: InAppWebViewOptions(
                          cacheEnabled: false,
                          // debuggingEnabled: true,
                        ),
                      ),
                      onWebViewCreated:
                          (InAppWebViewController webViewController) {
                        controller.setController(webViewController);
                      },
                      onLoadStart: (InAppWebViewController _, Uri? url) {
                        if (url != null) {
                          controller.onLoadStart(url.toString());
                        }
                      },
                      onLoadStop: (InAppWebViewController _, Uri? url) async {
                        if (url != null) {
                          controller.onLoadStop(url.toString());
                        }
                      },
                      onProgressChanged:
                          (InAppWebViewController _, int progress) {
                        controller.onProgressChanged(progress);
                      },
                    ),
                  ),
                ),
              ),
            ],
          );
        }),
      ),
    );
  }

  // Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "Add New Beat",
      ),
      styleType: Style.bgShadow,
    );
  }
}
