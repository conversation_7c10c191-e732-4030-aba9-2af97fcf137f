// ignore_for_file: unnecessary_import

import 'imgtwentyone_item_model.dart';
import '../../../core/app_export.dart';
import 'order_item_model.dart';
import 'package:sfm_new/data/models/selectionPopupModel/selection_popup_model.dart';


class HomeModel {
  Rx<List<ImgtwentyoneItemModel>> imgtwentyoneItemList =
      Rx(List.generate(2, (index) => ImgtwentyoneItemModel()));

  Rx<List<OrderItemModel>> orderItemList = Rx([
    OrderItemModel(
        orderImage1: ImageConstant.imgOrder25.obs,
        orderText: "lbl_order".tr.obs,
        addOrderText: "lbl_add_your_order".tr.obs),
    OrderItemModel(
        orderImage1: ImageConstant.imgOrderHistory2.obs,
        orderText: "lbl_order_history".tr.obs,
        addOrderText: "lbl_view_order_history".tr.obs)
  ]);

  Rx<List<SelectionPopupModel>> dropdownItemList = Rx([
    SelectionPopupModel(
      id: 1,
      title: "Item One",
      isSelected: true,
    ),
    SelectionPopupModel(
      id: 2,
      title: "Item Two",
    ),
    SelectionPopupModel(
      id: 3,
      title: "Item Three",
    )
  ]);
}
