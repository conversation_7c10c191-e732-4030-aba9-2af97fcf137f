import '../../../core/app_export.dart';

/// This class is used in the [order_item_widget] screen.
class OrderItemModel {
  OrderItemModel({
    this.orderImage1,
    this.orderText,
    this.addOrderText,
    this.id,
  }) {
    orderImage1 = orderImage1 ?? Rx(ImageConstant.imgOrder25);
    orderText = orderText ?? Rx("Order");
    addOrderText = addOrderText ?? Rx("Add your order");
    id = id ?? Rx("");
  }

  Rx<String>? orderImage1;

  Rx<String>? orderText;

  Rx<String>? addOrderText;

  Rx<String>? id;
}
