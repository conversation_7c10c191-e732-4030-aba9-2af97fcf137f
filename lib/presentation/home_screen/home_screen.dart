// ignore_for_file: unused_import

import 'package:sfm_new/presentation/home_screen/widgets/home_graph_tcpc_chart_widget.dart';
import 'package:sfm_new/presentation/home_screen/widgets/home_target_chart_widget.dart';

import 'widgets/home_drawer_widget.dart';
import 'widgets/home_image_with_pagination_widget.dart';
import 'widgets/home_order_history_widget.dart';
import 'widgets/home_options_widget.dart';
import 'widgets/home_graph_sale_chart_widget.dart';
import 'controller/home_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';

// ignore_for_file: must_be_immutable
class HomeScreen extends GetWidget<HomeController> {
  const HomeScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Safe<PERSON>rea(
        child: Scaffold(
          appBar: CustomAppBar(),
          drawer: HomeDrawerWidget(),
          body: SizedBox(
            width: SizeUtils.width,
            height: MediaQuery.of(context).size.height,
            child: SingleChildScrollView(
              padding: EdgeInsets.only(top: 0.v),
              child: Obx(
                () => Container(
                  margin: EdgeInsets.only(bottom: 5.v),
                  padding: EdgeInsets.symmetric(horizontal: 7.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (controller.isBannerDataValid.value)
                        Column(
                          children: [
                            HomeImageWithPaginationWidget(),
                            SizedBox(height: 8.v),
                          ],
                        ),

                      SizedBox(height: 4.v),
                      // Order History
                      HomeOrderHistoryWidget(),
                      SizedBox(height: 10.v),

                      // Home Options
                      HomeOptionsWidget(),
                      SizedBox(height: 10.v),

                      // Sales Chart
                      HomeGraphSaleChartWidget(),
                      SizedBox(height: 8.v),

                      // TCPC Chart
                      HomeGraphTCPCChartWidget(),
                      SizedBox(height: 8.v),

                      // Target Chart
                      if (controller.targetList.isNotEmpty)
                        Column(
                          children: [
                            HomeTargetChartWidget(),
                            SizedBox(height: 8.v),
                          ],
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  @override
  Size get preferredSize => Size.fromHeight(50.0);
  final HomeController myController = Get.put(HomeController());

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      title: Obx(() {
        return myController.isSearching.value
            ? Container(
                width: MediaQuery.of(context).size.width * 0.9,
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Search...',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(vertical: 15.0),
                    prefixIcon: IconButton(
                      icon: Icon(Icons.arrow_back),
                      onPressed: () {
                        myController.isSearching.value = false;
                      },
                    ),
                  ),
                ),
              )
            : Text(
                "lbl_home".tr,
                style: CustomTextStyles.titleMediumPrimary.copyWith(
                  color: theme.colorScheme.primary,
                ),
              );
      }),
      centerTitle: true,
      actions: <Widget>[
        IconButton(
          icon: Icon(
            Icons.notifications,
            color: theme.colorScheme.primary,
          ),
          onPressed: () {
            Get.toNamed(AppRoutes.notificationScreen);
          },
        ),
      ],
      leading: IconButton(
        icon: Icon(
          Icons.menu,
          color: theme.colorScheme.primary,
        ),
        onPressed: () {
          Scaffold.of(context).openDrawer();
        },
      ),
    );
  }
}

class MyController extends GetxController {
  var myGlobalReportVariable = 1.obs;

  void updateGlobalVariable(int newValue) {
    myGlobalReportVariable.value = newValue;
  }
}
