// ignore_for_file: unused_local_variable, invalid_use_of_protected_member, unnecessary_null_comparison

import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'dart:async';
import 'package:uuid/uuid.dart';
import 'package:path_provider/path_provider.dart';
import 'package:open_file_plus/open_file_plus.dart';

class Pair {
  final String text;
  final IconData icon;
  const Pair(this.text, this.icon);

  @override
  String toString() {
    return text;
  }
}

class PDFViewerController extends GetxController {
  RxString employeeCode = ''.obs;
  RxString employeeToken = "".obs;

  List<Map<String, dynamic>> reportNameList = [
    {'key': 'dbr', 'value': 'DBR'},
    {'key': 'dsr', 'value': 'DSR'},
    {'key': 'attendance_employee', 'value': 'Employee Attendance'},
    {'key': 'expense_report', 'value': 'Expense Report'},
    {'key': 'beatwise_retailer_report', 'value': 'Beat Wise Retailer Report'},
    {'key': 'target_vs_achivement', 'value': 'Target VS Achievement'},
  ];

  List<Map<String, dynamic>> reportTypeList = [
    {'key': 'brand_wise', 'value': 'Brand Wise'},
    {'key': 'product_wise', 'value': 'Product Wise'},
  ];

  List<Map<String, dynamic>> targetList = [
    {'key': 'primary', 'value': 'Primary'},
    {'key': 'secondary', 'value': 'Secondary'},
    {'key': 'both', 'value': 'Both'},
  ];

  List<Map<String, dynamic>> downloadTypeList = [
    {'key': 'pdf', 'value': 'PDF'},
    {'key': 'excel', 'value': 'EXCEL'},
  ];

  List<Map<String, dynamic>> monthList = [
    {'key': '01', 'value': 'January'},
    {'key': '02', 'value': 'February'},
    {'key': '03', 'value': 'March'},
    {'key': '04', 'value': 'April'},
    {'key': '05', 'value': 'May'},
    {'key': '06', 'value': 'June'},
    {'key': '07', 'value': 'July'},
    {'key': '08', 'value': 'August'},
    {'key': '09', 'value': 'September'},
    {'key': '10', 'value': 'October'},
    {'key': '11', 'value': 'November'},
    {'key': '12', 'value': 'December'},
  ];

  List<String> yearList = [
    '${DateTime.now().year - 1}',
    '${DateTime.now().year}',
    '${DateTime.now().year + 1}',
  ];

  RxString selectedReportName = ''.obs;
  RxString selectedReportType = ''.obs;
  RxString selectedTargetType = ''.obs;
  RxString selectedDownloadType = ''.obs;
  RxString selectedMonth = ''.obs;
  RxString selectedYear = ''.obs;
  final Dio dio = Dio();

  RxBool _downloading = false.obs;
  RxString _filePath = ''.obs;

  bool get downloading => _downloading.value;
  String get filePath => _filePath.value;

  @override
  void onInit() async {
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
  }

  String? getKeyFromValue(
      String selectedType, List<Map<String, dynamic>> arrayList) {
    try {
      final name = arrayList.firstWhere(
        (name) => name['value'] == selectedType,
      );
      return name['key'];
    } catch (e) {
      if (kDebugMode) {
        print('Error in getReportKeyFromValue: $e');
      }
      return null;
    }
  }

  // Function to fetch user data from shared preferences
  Future<Map<String, dynamic>?> getUserData() async {
    String? userDataString =
        await SharedPrefManager.instance.getString(ConstantValues.userData) ??
            "";
    if (userDataString != null) {
      return Map<String, dynamic>.from(
        Map<String, dynamic>.fromIterable(
          userDataString.replaceAll('{', '').replaceAll('}', '').split(','),
          key: (e) => e.split(':')[0].trim(),
          value: (e) => e.split(':')[1].trim(),
        ),
      );
    } else {
      return null;
    }
  }

  String generateUUID() {
    var uuid = Uuid();
    return uuid.v4();
  }

  Future<String?> fetchEmployeeData() async {
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.employee) ??
            "";
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];
      employeeCode.value = empCode!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> fetchedToken() async {
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
    if (employeeDataJson != null) {
      employeeToken.value = employeeDataJson;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> getReport() async {
    _downloading.value = true;
    try {
      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };

      print("EasyLoading.show");
      EasyLoading.show(status: 'Loading...');
      String url = ApiClient.getUrl(ApiClient.getReportEndpoint);
      Map<String, dynamic> data = {
        "report": selectedReportName.value,
        "r_type": selectedReportType.value, //brand_wise,product_wise
        "month": selectedMonth.value,
        "year": selectedYear.value,
        "search_employee": employeeCode.value,
        "prima_secon": selectedTargetType.value, //both,primary,secondary
        "d_type": selectedDownloadType.value, // excel
      };
      print("data: ${data}");
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.getReportEndpoint),
        options: Options(headers: headers, responseType: ResponseType.bytes),
        data: data,
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print("response.statusCode: ${response.statusCode}");
      if (response.statusCode == 200) {
        final Directory? appDir = Platform.isAndroid
            ? await getExternalStorageDirectory()
            : await getApplicationDocumentsDirectory();
        print("appDir: ${appDir}");
        String tempPath = appDir!.path;
        print("tempPath: ${tempPath}");
        final String fileName =
            DateTime.now().microsecondsSinceEpoch.toString() + '-' + 'sfm.pdf';
        print("fileName: ${fileName}");
        File file = new File('$tempPath/$fileName');
        print("file: ${file}");
        if (!await file.exists()) {
          await file.create();
        }
        await file.writeAsBytes(response.data);
        OpenFile.open("${file}");
        // return file;
      } else {
        print('Response data is null');
        showToastMessage("${response.statusMessage}");
      }
      EasyLoading.dismiss();
    } on DioException catch (e) {
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
      }
    } finally {
      EasyLoading.dismiss();
    }
  }
}
