// ignore_for_file:  unnecessary_null_comparison, unused_local_variable,

import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sfm_new/presentation/beat_screen/controller/beat_controller.dart';

import 'dart:async';
import 'dart:io';

import 'package:image/image.dart' as img;
import 'package:sfm_new/presentation/product_screen/controller/product_controller.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/config_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_category_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:sfm_new/sfmDatabase/models/district_model.dart';
import 'package:sfm_new/sfmDatabase/models/grade_model.dart';
import 'package:sfm_new/sfmDatabase/models/state_model.dart';
import 'package:sfm_new/sfmDatabase/models/town_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';

class Pair {
  final String text;
  final IconData icon;
  const Pair(this.text, this.icon);

  @override
  String toString() {
    return text;
  }
}

class AddRetailerController extends GetxController {
  TextEditingController emailController = TextEditingController();
  TextEditingController birthDateController = TextEditingController();
  TextEditingController anniDateController = TextEditingController();
  TextEditingController panController = TextEditingController();

  // new code started
  final RxBool isMobile1Valid = true.obs;
  final RxBool showError = false.obs;
  final Rx<TextEditingController> mobileNo1Controller =
      TextEditingController().obs;

  final RxBool isMobile2Valid = true.obs;
  final Rx<TextEditingController> mobileNo2Controller =
      TextEditingController().obs;

  final RxBool isGSTValid = true.obs;
  final Rx<TextEditingController> gstController = TextEditingController().obs;

  final Rx<TextEditingController> shopNameController =
      TextEditingController().obs;
  final Rx<TextEditingController> contactPersonController =
      TextEditingController().obs;
  final Rx<TextEditingController> addressController =
      TextEditingController().obs;
  final Rx<TextEditingController> areaController = TextEditingController().obs;

  final Rx<TextEditingController> pincodeController =
      TextEditingController().obs;

  var isShopNameValid = true.obs;
  var isContactPersonValid = true.obs;
  var isAddressValid = true.obs;
  var isAreaValid = true.obs;
  var isPincodeValid = true.obs;
  var isGradeIDValid = true.obs;
  var isCategoryIDValid = true.obs;
  var isStateIDValid = true.obs;
  var isDistrictIDValid = true.obs;
  var isTownIDValid = true.obs;

  void validateMobileNumber1() {
    final mobileNumber = mobileNo1Controller.value.text;
    isMobile1Valid.value = mobileNumber.isNotEmpty && mobileNumber.length == 10;
  }

  void validateMobileNumber2() {
    final mobileNumber = mobileNo2Controller.value.text;
    isMobile2Valid.value = mobileNumber.isNotEmpty && mobileNumber.length == 10;
  }

  void validateGST() {
    final gstNumber = gstController.value.text;
    final regex = RegExp(
        r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}[Z]{1}[0-9A-Z]{1}$');
    if (gstNumber.isNotEmpty && regex.hasMatch(gstNumber)) {
      isGSTValid.value = true;
    } else {
      isGSTValid.value = false;
    }
    // isGSTValid.value = gstNumber.isEmpty && gstNumber.length == 10;
  }

  void validateField(String value, RxBool isValid) {
    isValid.value = value.isNotEmpty;
  }

  void validateGradeID() {
    isGradeIDValid.value = selectedGradeID.value != 0;
  }

  void validateCategoryID() {
    isCategoryIDValid.value = selectedCategoryID.value != 0;
  }

  void validateStateID() {
    isStateIDValid.value = selectedStateID.value != 0;
  }

  void validateDistrictID() {
    isDistrictIDValid.value = selectedDistrictID.value != 0;
  }

  void validateTownID() {
    isTownIDValid.value = selectedTownID.value != 0;
  }

  void validateAllFields() {
    validateMobileNumber1();
    validateMobileNumber2();
    validateGST();
    validateField(shopNameController.value.text, isShopNameValid);
    validateField(contactPersonController.value.text, isContactPersonValid);
    validateField(addressController.value.text, isAddressValid);
    validateField(areaController.value.text, isAreaValid);
    validateField(pincodeController.value.text, isPincodeValid);
    validateGradeID();
    validateCategoryID();
    validateStateID();
    validateDistrictID();
    validateTownID();
  }



  SelectionPopupModel? selectedDropDownValue;
  SelectionPopupModel? selectedDropDownValue1;

  final ImagePicker _picker = ImagePicker();

  Rx<File?> imageFile1 = Rx<File?>(null);
  Rx<File?> imageFile2 = Rx<File?>(null);
  Rx<File?> imageFile3 = Rx<File?>(null);
  String storedBase64Image = "";

  RxList<Customer> customerList = <Customer>[].obs;
  RxList<Customer> selectedCustomerList = <Customer>[].obs;
  RxList<CustomerCategory> customerCategoryList = <CustomerCategory>[].obs;
  RxList<StateModel> stateList = <StateModel>[].obs;
  RxList<District> districtList = <District>[].obs;
  RxList<Town> townList = <Town>[].obs;
  RxList<Grade> gradeList = <Grade>[].obs;

  List<String> claimComplainList = ['Claim', 'Complain'];

  RxString selectedCustomerCode = ''.obs;
  RxString selectedBeatCode = ''.obs;
  RxInt selectedCategoryID = 0.obs;
  RxInt selectedGradeID = 0.obs;

  RxInt selectedStateID = 0.obs;
  RxInt selectedDistrictID = 0.obs;
  RxInt selectedTownID = 0.obs;
  RxInt syncStatus = 0.obs;

  RxString employeeCode = ''.obs;

  RxString employeeToken = "".obs;

  RxInt pincode = 0.obs;

  List<String> typeList = ['A', 'B', 'C', 'D'];

  RxInt selectedImageNo = 0.obs;

  final Rx<String?> base64Image = Rx(null);

  var isLoading = false.obs;

  RxDouble lat = 0.0.obs;
  RxDouble long = 0.0.obs;
  RxString locationMessage = ''.obs;

  RxString birthDateToShow = ''.obs;
  RxString birthDateToSave = ''.obs;

  RxString anniDateToShow = ''.obs;
  RxString anniDateToSave = ''.obs;

  List<String> shopImageList = [];

  List<String> updateShopImageList = [];
  String strUpdateImage = "";

  RxBool isFromProductShopInfo = false.obs;
  RxString selectedRetailerCode = ''.obs;

  RxString selectedDistDealerName = ''.obs;
  RxString selectedGradeName = ''.obs;
  RxString selectedCategoryName = ''.obs;
  RxString selectedStateName = ''.obs;
  RxString selectedDistrictName = ''.obs;
  RxString selectedTownName = ''.obs;

  RxDouble outStandingAmount = 0.0.obs;

  RxInt shopImageValue = 0.obs;

  RxList<Config> configList = <Config>[].obs;
  final isValidMobile = false.obs;

  // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer

  @override
  void onInit() async {
    super.onInit();

    // EasyLoading.show(status: 'Loading...');

    selectedBeatCode.value = await SharedPrefManager.instance
            .getString(ConstantValues.selectedCustCode) ??
        "";

    isFromProductShopInfo.value = await SharedPrefManager.instance
            .getBool(ConstantValues.isFromProductShopInfo) ??
        false;

    getLocation();
    await fetchGrade();
    await fetchCustomers();
    await fetchCustomerCategory();
    await fetchState();
    await fetchDistrict();
    await fetchTown();

    await fetchEmployeeData();
    await fetchedToken();
    await fetchConfig();

    if (isFromProductShopInfo.value == true) {
      selectedRetailerCode.value = await SharedPrefManager.instance
              .getString(ConstantValues.selectedRetailerCode) ??
          "";
      print("selectedRetailerCode: ${selectedRetailerCode.value}");
      await loadCustomer(selectedRetailerCode.value);
    }
  }

  // Validation method for mobile number
  void validateMobile(String value) {
    if (value.length == 10) {
      isValidMobile.value = true;
    } else {
      isValidMobile.value = false;
    }
  }

  @override
  void onClose() {
    super.onClose();
    // claimComplainController.dispose();
  }

  Future<void> loadCustomer(String cmCode) async {
    Customer? customer = await CustomerRepository.getCustomerByCode(cmCode);
    if (customer != null) {
      print("Customer loaded: ${customer}");
      shopNameController.value.text = customer.cmName ?? "";
      contactPersonController.value.text = customer.cmContactPerson ?? "";
      addressController.value.text = customer.cmAddress ?? "";

      if (customer.cmImageRelation == null ||
          customer.cmImageRelation!.isEmpty) {
        updateShopImageList = [];
      } else if (!customer.cmImageRelation!.contains(',')) {
        updateShopImageList = [customer.cmImageRelation!];
        strUpdateImage = customer.cmImageRelation!;
      } else {
        updateShopImageList = customer.cmImageRelation!.split(',');
        strUpdateImage = customer.cmImageRelation!;
      }

      print("updateShopImageList: ${updateShopImageList}");

      areaController.value.text = customer.cmArea ?? "";
      mobileNo1Controller.value.text = "${customer.cmMobile ?? 0}";

      if (customer.cmMobile2 == 0) {
        mobileNo2Controller.value.text = "";
      } else {
        mobileNo2Controller.value.text = "${customer.cmMobile2 ?? ""}";
      }
      emailController.text = customer.cmEmail ?? "";
      gstController.value.text = customer.cmGST ?? "";
      panController.text = customer.cmPan ?? "";
      pincodeController.value.text = customer.cmPincode ?? "";
      lat.value = customer.cmLat ?? 0.0;
      long.value = customer.cmLong ?? 0.0;
      selectedTownID.value = customer.cmTownID ?? 0;
      selectedCustomerCode.value = customer.cmRelationCode ?? "";
      selectedBeatCode.value = customer.cmBeatRelationCode ?? "";
      selectedCategoryID.value = customer.cmCategoryRelationID ?? 0;
      selectedGradeID.value = customer.cmGradeRelationID ?? 0;
      syncStatus.value = customer.syncStatus ?? 0;
      outStandingAmount.value = customer.cmOutstandingAmount ?? 0.0;

      //For Dist/Dealer
      selectedCustomerCode.value = '${customer.cmRelationCode ?? ""}';
      final selectedDistName =
          getCustomerNameFromCode(selectedCustomerCode.value);
      selectedDistDealerName.value = selectedDistName ?? "";
      print("selectedDistName: ${selectedDistName}");

      //For Grade
      selectedGradeID.value = customer.cmGradeRelationID ?? 0;
      final selectedGrade = getGradeNameFromID(selectedGradeID.value);
      selectedGradeName.value = selectedGrade ?? "";
      print("selectedGrade: ${selectedGrade}");

      //For Category
      selectedCategoryID.value = customer.cmCategoryRelationID ?? 0;
      final selectedCategory =
          getCustomerCategoryNameFromID(selectedCategoryID.value);
      selectedCategoryName.value = selectedCategory ?? "";
      print("selectedCategory: ${selectedCategory}");

      //For Town
      selectedTownID.value = customer.cmTownID ?? 0;
      final selectedTown = getTownNameFromID(selectedTownID.value);
      selectedTownName.value = selectedTown ?? "";
      print("selectedTown: ${selectedTown}");

      fetchDistrictIDByTownID(selectedTownID.value);
    }
  }

  Future<void> updateCustomer(String cmCode) async {
    int mobileNo1 = int.tryParse(mobileNo1Controller.value.text) ?? 0;
    int mobileNo2 = int.tryParse(mobileNo2Controller.value.text) ?? 0;

    final now = DateTime.now().toIso8601String();
    print("cmCreatedAt: ${now}");

    print("shopImageList: ${shopImageList}");

    if (shopImageList.length > 0) {
      String strShopImages = shopImageList.join(', ');
      print("commaSeparatedString: ${strShopImages}");
      var updatedCustomer = Customer(
        cmCode: cmCode,
        cmName: shopNameController.value.text,
        cmMobile: mobileNo1,
        cmMobile2: mobileNo2,
        cmEmail: emailController.text,
        cmAddress: addressController.value.text,
        cmPincode: pincodeController.value.text,
        cmGST: gstController.value.text,
        cmPan: panController.text,
        cmLat: lat.value,
        cmLong: long.value,
        cmTownID: selectedTownID.value,
        // cmOutstandingAmount: 0,
        cmContactPerson: contactPersonController.value.text,
        cmArea: areaController.value.text,
        cmRelationCode: selectedCustomerCode.value,
        cmType: 5,
        cmCreatedAt: "${now}",
        cmAnniversary: anniDateController.text,
        cmDOB: birthDateController.text,
        cmImageRelation: strShopImages,
        cmBeatRelationCode: selectedBeatCode.value,
        cmCategoryRelationID: selectedCategoryID.value,
        cmGradeRelationID: selectedGradeID.value,
        syncStatus: 0,
      );

      await CustomerRepository.updateCustomer(updatedCustomer);
      print("Customer updated: ${updatedCustomer}");
    } else {

      print("updateShopImageList: ${updateShopImageList}");

      var updatedCustomer = Customer(
        cmCode: cmCode,
        cmName: shopNameController.value.text,
        cmMobile: mobileNo1,
        cmMobile2: mobileNo2,
        cmEmail: emailController.text,
        cmAddress: addressController.value.text,
        cmPincode: pincodeController.value.text,
        cmGST: gstController.value.text,
        cmPan: panController.text,
        cmLat: lat.value,
        cmLong: long.value,
        cmTownID: selectedTownID.value,
        // cmOutstandingAmount: 0,
        cmContactPerson: contactPersonController.value.text,
        cmArea: areaController.value.text,
        cmRelationCode: selectedCustomerCode.value,
        cmType: 5,
        cmCreatedAt: "${now}",
        cmAnniversary: anniDateController.text,
        cmDOB: birthDateController.text,
        cmImageRelation: strUpdateImage,
        cmBeatRelationCode: selectedBeatCode.value,
        cmCategoryRelationID: selectedCategoryID.value,
        cmGradeRelationID: selectedGradeID.value,
        syncStatus: 0,
      );

      await CustomerRepository.updateCustomer(updatedCustomer);
      print("Customer updated: ${updatedCustomer}");
    }
  }

  void fetchDistrictIDByTownID(int townId) {
    // Filter the districtList based on the townId
    List<Town> tempTownList = <Town>[];

    final filteredTowns =
        townList.where((town) => town.town_id == townId).toList();
    tempTownList.assignAll(filteredTowns);

    if (filteredTowns.isNotEmpty) {
      selectedDistrictID.value = filteredTowns.first.district_id ?? 0;
      print("selectedDistrictID: ${selectedDistrictID}");
      filterStateIDByDistrictID(selectedDistrictID.value);
    }
  }

  void filterStateIDByDistrictID(int districtID) {
    // Filter the districtList based on the townId
    List<District> tempDistrictList = <District>[];

    final filteredDistricts =
        districtList.where((dist) => dist.dm_id == districtID).toList();
    tempDistrictList.assignAll(filteredDistricts);

    if (tempDistrictList.isNotEmpty) {
      selectedDistrictName.value = tempDistrictList.first.dm_name ?? "";
      selectedStateID.value = tempDistrictList.first.state_id ?? 0;
      print("selectedDistrictName: ${selectedDistrictName}");
      filterStateNameByStateID(selectedStateID.value);
    }
  }

  void filterStateNameByStateID(int stateID) {
    // Filter the districtList based on the townId
    List<StateModel> tempStateList = <StateModel>[];

    final filteredStates =
        stateList.where((state) => state.state_id == stateID).toList();
    tempStateList.assignAll(filteredStates);
    print("tempDistrictList: ${filteredStates}");

    if (filteredStates.isNotEmpty) {
      selectedStateName.value = filteredStates.first.state_name ?? "";
      print("selectedStateName: ${selectedStateName}");
      print("selectedDistrictName: ${selectedDistrictName}");
      print("selectedTownName: ${selectedTownName}");
    }
  }

  Future<void> getLocation() async {
    Position position;
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();

    if (!serviceEnabled) {
      locationMessage.value = "Location services are disabled";
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        locationMessage.value = 'Location permissions are denied.';
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      locationMessage.value =
          'Location permissions are permanently denied, we cannot request permissions.';
      return;
    }

    if (permission == LocationPermission.unableToDetermine) {}

    position = await Geolocator.getCurrentPosition();
    locationMessage.value =
        'Latitude: ${position.latitude}, Longitude: ${position.longitude}';

    lat.value = position.latitude;
    long.value = position.longitude;
  }

  Future<void> pickImage(ImageSource source) async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      File compressedImage = await compressImage(File(image.path));
      if (selectedImageNo.value == 1) {
        imageFile1.value = compressedImage;
        imageToBase64(imageFile1.value!);
      } else if (selectedImageNo.value == 2) {
        imageFile2.value = compressedImage;
        imageToBase64(imageFile2.value!);
      } else if (selectedImageNo.value == 3) {
        imageFile3.value = compressedImage;
        imageToBase64(imageFile3.value!);
      }
    }
  }

  Future<File> compressImage(File imageFile) async {
    int quality = 75; 

    List<int> compressedBytes = await FlutterImageCompress.compressWithList(
      imageFile.readAsBytesSync(),
      minHeight: 1920,
      minWidth: 1080,
      quality: quality,
    );

    return File(imageFile.path)..writeAsBytesSync(compressedBytes);
  }

  String formatDateTimeToSave(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(dateTime);
  }

  String formatDateTimeToShow(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('dd MMM yyyy');
    return formatter.format(dateTime);
  }

  Future<void> fetchCustomers() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps = await db.query(
      '${TableValues.tableCustomer}',
      where: '${TableValues.customerType} IN (?, ?)',
      whereArgs: [3, 4],
    );
    customerList.value = List.generate(maps.length, (index) {
      return Customer.fromMap(maps[index]);
    });
    print("customerList: ${customerList}");
  }

  Future<void> fetchCustomerCategory() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps = await db.query(
      '${TableValues.tableCustomerCategory}',
    );
    customerCategoryList.value = List.generate(maps.length, (index) {
      return CustomerCategory.fromJson(maps[index]);
    });
    print("customerCategoryList: ${customerCategoryList}");
  }

  Future<void> fetchGrade() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps = await db.query(
      '${TableValues.tableGrade}',
    );
    gradeList.value = List.generate(maps.length, (index) {
      return Grade.fromJson(maps[index]);
    });
    print("gradeList: ${gradeList}");
  }

  Future<void> fetchState() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps = await db.query(
      '${TableValues.tableState}',
    );
    stateList.value = List.generate(maps.length, (index) {
      return StateModel.fromJson(maps[index]);
    });
    print("stateList: ${stateList}");
  }

  Future<void> fetchDistrict() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps = await db.query(
      '${TableValues.tableDistrict}',
    );
    districtList.value = List.generate(maps.length, (index) {
      return District.fromJson(maps[index]);
    });
    print("districtList: ${districtList}");
  }

  Future<void> fetchTown() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps = await db.query(
      '${TableValues.tableTown}',
    );
    townList.value = List.generate(maps.length, (index) {
      return Town.fromJson(maps[index]);
    });
    print("townList: ${townList}");
  }

  String? getCustomerCodeFromName(String selectedType) {
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmName == selectedType,
      );
      return customerType.cmCode;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerCodeFromTypeName: $e');
      }
      return null;
    }
  }

  String? getCustomerNameFromCode(String selectedType) {
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmCode == selectedType,
      );
      return customerType.cmName;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerCodeFromTypeName: $e');
      }
      return null;
    }
  }

  String? getCustomerCategoryNameFromID(int selectedType) {
    try {
      final categoryType = customerCategoryList.firstWhere(
        (categoryType) => categoryType.ccmID == selectedType,
      );
      return categoryType.ccmName;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerCategoryNameFromID: $e');
      }
      return null;
    }
  }

  int? getCustomerCategoryIDFromName(String selectedType) {
    try {
      final categoryType = customerCategoryList.firstWhere(
        (categoryType) => categoryType.ccmName == selectedType,
      );
      return categoryType.ccmID;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerCategoryIDFromName: $e');
      }
      return null;
    }
  }

  String? getGradeNameFromID(int selectedType) {
    try {
      final grade = gradeList.firstWhere(
        (grade) => grade.gm_id == selectedType,
      );
      return grade.gm_name;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getGradeNameFromID: $e');
      }
      return null;
    }
  }

  int? getGradeIDFromName(String selectedType) {
    try {
      final grade = gradeList.firstWhere(
        (grade) => grade.gm_name == selectedType,
      );
      return grade.gm_id;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getGradeIDFromName: $e');
      }
      return null;
    }
  }

  int? getStateIDFromName(String selectedType) {
    try {
      final stateType = stateList.firstWhere(
        (stateType) => stateType.state_name == selectedType,
      );
      return stateType.state_id;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getStateIDFromName: $e');
      }
      return null;
    }
  }

  int? getDistrictIDFromName(String selectedType) {
    try {
      final districtType = districtList.firstWhere(
        (districtType) => districtType.dm_name == selectedType,
      );
      return districtType.dm_id;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getDistrictIDFromName: $e');
      }
      return null;
    }
  }

  String? getTownNameFromID(int selectedType) {
    try {
      final townType = townList.firstWhere(
        (townType) => townType.town_id == selectedType,
      );
      return townType.town_name;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getTownIDFromName: $e');
      }
      return null;
    }
  }

  int? getTownIDFromName(String selectedType) {
    try {
      final townType = townList.firstWhere(
        (townType) => townType.town_name == selectedType,
      );
      return townType.town_id;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getTownIDFromName: $e');
      }
      return null;
    }
  }

  String generateUUID() {
    var uuid = Uuid();
    return uuid.v4();
  }

  Future<String?> fetchEmployeeData() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? employeeDataJson = prefs.getString('employee');
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];
      employeeCode.value = empCode!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.employee) ??
            "";
  }

  Future<void> addRetailerToDB() async {
    final db = await DatabaseProvider.database;

    String formattedDateTime =
        DateFormat("yyyy-MM-ddTHH:mm:ss.000000'Z'").format(DateTime.now());
    print(formattedDateTime);

    String strShopImages = shopImageList.join(', ');
    print("commaSeparatedString: ${strShopImages}");

    String uuid = generateUUID();

    int mobileNo1 = int.tryParse(mobileNo1Controller.value.text) ?? 0;
    int mobileNo2 = int.tryParse(mobileNo2Controller.value.text) ?? 0;


    DateTime now = DateTime.now();
    String dateString = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);

    var customer = Customer(
      cmCode: uuid,
      cmName: shopNameController.value.text,
      cmMobile: mobileNo1,
      cmMobile2: mobileNo2,
      cmEmail: emailController.text,
      cmAddress: addressController.value.text,
      cmPincode: pincodeController.value.text,
      cmGST: gstController.value.text,
      cmPan: panController.text,
      cmLat: lat.value,
      cmLong: long.value,
      // cmMarketType: 1,
      cmTownID: selectedTownID.value,
      cmOutstandingAmount: 0,
      cmContactPerson: contactPersonController.value.text,
      cmArea: areaController.value.text,
      cmRelationCode: selectedCustomerCode.value,
      cmType: 5,
      cmCreatedAt: "${dateString}",
      cmAnniversary: anniDateController.text,
      cmDOB: birthDateController.text,
      cmImageRelation: strShopImages,
      cmBeatRelationCode: selectedBeatCode.value,
      cmCategoryRelationID: selectedCategoryID.value,
      cmGradeRelationID: selectedGradeID.value,
      syncStatus: 0,
    );

    print("customer: ${customer}");

    await db.insert(
      '${TableValues.tableCustomer}',
      customer.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    final List<Map<String, dynamic>> unsyncedCustomers = await db.query(
        '${TableValues.tableCustomer}',
        where: '${TableValues.customerSyncStatus} = ?',
        whereArgs: [0]);

    print("unsyncedCustomers123: ${unsyncedCustomers}");

    showToastMessage("Shop added successfully");

    final beatList = Get.find<BeatController>();
    beatList.checkInternetConnectivity();
    Get.back();
  }

  // shop_imagespackaging_charge
  Future<void> fetchConfig() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableConfig}");
    print(maps);
    configList.value = List.generate(maps.length, (index) {
      return Config.fromJson(maps[index]);
    });
    print("configList: ${configList}");

    if (configList.length != 0) {
      final shopImageConfig = configList.firstWhere(
        (config) => config.config_key == 'shop_images',
      );

      print('shopImageConfig fetched');
      print("shopImageConfig: ${shopImageConfig.config_value}");
      shopImageValue.value = int.tryParse(shopImageConfig.config_value ?? "")!;

      print('Shop Images Value: ${shopImageValue.value}');
    }
  }

  Future<void> requestGalleryPermission() async {
    EasyLoading.show(status: 'Loading...');
    final status = await Permission.storage.request();
    if (status == PermissionStatus.granted) {
      await pickImage(ImageSource.camera);
    } else {
      Get.snackbar(
        "Storage Permission",
        "Please grant storage permission to access gallery.",
        snackPosition: SnackPosition.BOTTOM,
      );
    }
    EasyLoading.dismiss();
  }

  Future<void> requestCameraPermission() async {
    EasyLoading.show(status: 'Loading...');
    final status = await Permission.camera.request();
    if (await Permission.camera.status.isGranted) {
      // Permission already granted, proceed
      await pickImage(ImageSource.camera);
    } else if (await Permission.camera.request().isGranted) {
      // Permission requested and granted, proceed
      await pickImage(ImageSource.camera);
    } else {
      Get.snackbar(
        "Camera Permission",
        "Please grant camera permission to proceed.",
        snackPosition: SnackPosition.BOTTOM,
      );
    }
    EasyLoading.dismiss();
  }

  Future<String> imageToBase64(File imageFile) async {
    // Read the image file
    // List<int> imageBytes = await imageFile.readAsBytes();

    Uint8List imageBytes = await imageFile.readAsBytes();

    // Decode the image
    img.Image? image = img.decodeImage(imageBytes);

    if (image != null) {
      // Compress the image if its size exceeds 500KB
      if (image.lengthInBytes > 250 * 1024) {
        image = img.copyResize(
          image,
          width: image.width ~/ 2,
          height: image.height ~/ 2,
        );
      }
      List<int> compressedImageBytes = img.encodeJpg(image, quality: 75);
      String base64Image = base64Encode(compressedImageBytes);
      storedBase64Image = base64Image;
      print("storedBase64Image:- $storedBase64Image");

      int compressedImageSize = compressedImageBytes.length;
      print('Compressed image size: ${compressedImageSize} bytes');

      if (selectedImageNo.value == 1) {
        shopImageList.insert(0, storedBase64Image);
      } else if (selectedImageNo.value == 2) {
        shopImageList.insert(1, storedBase64Image);
      } else if (selectedImageNo.value == 3) {
        shopImageList.insert(2, storedBase64Image);
      }

      print("shopImageList: ${shopImageList}");
      return base64Image;
    } else {
      throw Exception('Failed to decode the image');
    }
  }

  Future<void> pickImageFromGallery() async {
    final ImagePicker _picker = ImagePicker();
    final XFile? pickedImage =
        await _picker.pickImage(source: ImageSource.gallery);

    if (pickedImage != null) {
    }
  }

  void showPermissionDeniedDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('Permission Denied'),
        content: Text('Please grant contact permission in app settings.'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: Text('Open Settings'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void showPermissionDeniedDialogGallery() {
    Get.dialog(
      AlertDialog(
        title: Text('Permission Denied'),
        content: Text('Please grant permission in app settings.'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: Text('Open Settings'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

class CustomerRepository {
  static Future<Customer?> getCustomerByCode(String cmCode) async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableValues.tableCustomer,
      where: '${TableValues.customerCode} = ?',
      whereArgs: [cmCode],
    );

    if (maps.isNotEmpty) {
      return Customer.fromMap(maps.first);
    } else {
      return null;
    }
  }

  static Future<void> updateCustomer(Customer customer) async {
    final db = await DatabaseProvider.database;
    await db.update(
      TableValues.tableCustomer,
      customer.toMap(),
      where: '${TableValues.customerCode} = ?',
      whereArgs: [customer.cmCode],
    );

    showToastMessage("Shop updated successfully");

    final beatList = Get.find<ProductController>();
    beatList.checkInternetConnectivityCustomer();
    Get.back();

  }
}
