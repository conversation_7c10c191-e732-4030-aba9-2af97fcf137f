import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';

import 'controller/place_order_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

//
// ignore_for_file: must_be_immutable
class PlaceOrderScreen extends GetWidget<PlaceOrderController> {
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        // backgroundColor: Colors.grey[100],
        resizeToAvoidBottomInset: true,
        appBar: _buildAppBar(),
        body: SingleChildScrollView(
          child: SizedBox(
            width: SizeUtils.width,
            child: SingleChildScrollView(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: Form(
                key: _formKey,
                child: Obx(
                  () => Container(
                    width: double.maxFinite,
                    padding: EdgeInsets.all(16.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          controller.isFromBeat.value == true
                              ? "${controller.selectedCustName.value}"
                              : "${controller.selectedCustName.value}",
                          // "${controller.ssController.selectedCustName.value}",
                          style: CustomTextStyles.titleLargeBlack900,
                        ),
                        SizedBox(height: 8.v),
                        CustomTextField(
                          controller: controller.qtyController,
                          hintText:
                              "Total Quantity : ${controller.cartController.totalQty}",
                          isEnabled: false,
                          height: 55,
                        ),
                        SizedBox(height: 8.v),
                        CustomTextField(
                          controller: controller.amountController,
                          hintText:
                              "Total Amount : ₹ ${controller.cartController.totalAmount.toStringAsFixed(2)}",
                          isEnabled: false,
                          height: 55,
                        ),
                        SizedBox(height: 8.v),
                        Visibility(
                          visible: controller.selectedCustType.value == 2
                              ? true
                              : false,
                          child: CustomDropdown.search(
                            decoration: CustomDropdownDecoration(
                              searchFieldDecoration: SearchFieldDecoration(
                                  textStyle:
                                      CustomTextStyles.titleSmallBluegray900),
                              noResultFoundStyle:
                                  CustomTextStyles.bodyMediumBluegray700,
                              hintStyle: CustomTextStyles.titleSmallBluegray900,
                              listItemStyle:
                                  CustomTextStyles.titleSmallBluegray900,
                              headerStyle:
                                  CustomTextStyles.titleSmallBluegray900,
                              expandedFillColor: Colors.white,
                              expandedBorder:
                                  Border.all(color: Colors.grey[300]!),
                              expandedBorderRadius: BorderRadius.circular(12),
                              closedBorder:
                                  Border.all(color: Colors.grey[300]!),
                              closedBorderRadius: BorderRadius.circular(12),
                            ),
                            hintText: 'lbl_select_company'.tr,
                            searchHintText: 'lbl_select_company'.tr,
                            items: controller.customerList
                                .where((cmType) => cmType.cmType == 1)
                                .map((cmType) => cmType.cmName!)
                                .toList(),
                            excludeSelected: false,
                            //initialItem: 'lbl_select_company'.tr,
                            onChanged: (selectedType) {
                              print('Selected Company Name: $selectedType');
                              controller.selectedPartyCode.value = controller
                                  .getCustomerCodeFromName("${selectedType}")!;
                            },
                          ),
                        ),
                        Visibility(
                          visible: controller.selectedCustType.value == 3 ||
                                  controller.selectedCustType.value == 4
                              ? true
                              : false,
                          child: CustomDropdown.search(
                            decoration: CustomDropdownDecoration(
                              searchFieldDecoration: SearchFieldDecoration(
                                  textStyle:
                                      CustomTextStyles.titleSmallBluegray900),
                              noResultFoundStyle:
                                  CustomTextStyles.bodyMediumBluegray700,
                              hintStyle: CustomTextStyles.titleSmallBluegray900,
                              listItemStyle:
                                  CustomTextStyles.titleSmallBluegray900,
                              headerStyle:
                                  CustomTextStyles.titleSmallBluegray900,
                              expandedFillColor: Colors.white,
                              expandedBorder:
                                  Border.all(color: Colors.grey[300]!),
                              expandedBorderRadius: BorderRadius.circular(12),
                              closedBorder:
                                  Border.all(color: Colors.grey[300]!),
                              closedBorderRadius: BorderRadius.circular(12),
                            ),
                            hintText: 'lbl_select_ss'.tr,
                            searchHintText: 'lbl_select_ss'.tr,
                            items: controller.customerList
                                .where((cmType) => cmType.cmType == 2)
                                .map((cmType) => cmType.cmName!)
                                .toList(),
                            excludeSelected: false,
                            //initialItem: 'lbl_select_ss'.tr,
                            onChanged: (selectedType) {
                              print('Selected SS Name: $selectedType');
                              controller.selectedPartyCode.value = controller
                                  .getCustomerCodeFromName("${selectedType}")!;
                            },
                          ),
                        ),
                        Visibility(
                          visible: controller.selectedCustType.value == 5
                              ? true
                              : false,
                          child: CustomDropdown.search(
                            decoration: CustomDropdownDecoration(
                              searchFieldDecoration: SearchFieldDecoration(
                                  textStyle:
                                      CustomTextStyles.titleSmallBluegray900),
                              noResultFoundStyle:
                                  CustomTextStyles.bodyMediumBluegray700,
                              hintStyle: CustomTextStyles.titleSmallBluegray900,
                              listItemStyle:
                                  CustomTextStyles.titleSmallBluegray900,
                              headerStyle:
                                  CustomTextStyles.titleSmallBluegray900,
                              expandedFillColor: Colors.white,
                              expandedBorder:
                                  Border.all(color: Colors.grey[300]!),
                              expandedBorderRadius: BorderRadius.circular(12),
                              closedBorder:
                                  Border.all(color: Colors.grey[300]!),
                              closedBorderRadius: BorderRadius.circular(12),
                            ),
                            hintText: 'lbl_select_distributor'.tr,
                            searchHintText: 'lbl_select_distributor'.tr,
                            items: controller.customerList
                                .where((cmType) =>
                                    cmType.cmType == 3 || cmType.cmType == 4)
                                .map((cmType) => cmType.cmName!)
                                .toList(),
                            excludeSelected: false,
                            // initialItem: controller
                            //             .selectedCustRelationName.value !=
                            //         ""
                            //     ? "${controller.selectedCustRelationName.value}"
                            //     : 'lbl_select_distributor'.tr,
                            onChanged: (selectedType) {
                              print('Selected Distributor Name: $selectedType');
                              controller.selectedPartyCode.value = controller
                                  .getCustomerCodeFromName("${selectedType}")!;
                              print(
                                  "controller.selectedPartyCode.value: ${controller.selectedPartyCode.value}");
                            },
                          ),
                        ),
                        SizedBox(height: 8.v),
                        CustomTextFieldTelephoneOrder(
                          controller: controller.telephoneOrderController,
                          hintText: 'Telephone Order',
                          isChecked: controller.isTelephoneOrderChecked.value,
                          height: 55,
                          readOnly: controller.isTelephoneOrderChecked.value
                              ? true
                              : false,
                          onChanged: (value) {
                            print('Telephone Order Checkbox state: $value');
                          },
                        ),
                        SizedBox(height: 8.v),
                        CustomTextFieldPackagingCharge(
                          controller:
                              controller.packagingChargeController.value,
                          hintText: 'Packaging Charge',
                          isChecked: controller.isPackagingChargeChecked.value,
                          height: 55,
                          onChanged: (value) {
                            print('Packaging Charge Checkbox state: $value');
                          },
                        ),
                        SizedBox(height: 8.v),
                        CustomTextField(
                          controller: controller.transporterNameController,
                          hintText: "Transportation Name",
                          height: 55,
                        ),
                        SizedBox(height: 8.v),
                        CustomTextField(
                          controller: controller.notesController,
                          hintText: "Note",
                          height: 100,
                        ),
                        SizedBox(height: 5.v),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        bottomNavigationBar: Obx(
          () => _buildSubmit(),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      leadingWidth: 44.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 17.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "lbl_place_order".tr,
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildSubmit() {
    return CustomElevatedButton(
      text: "lbl_submit".tr,
      isDisabled: controller.isSubmitting.value,
      margin: EdgeInsets.only(
        left: 26.h,
        right: 24.h,
        bottom: 17.v,
      ),
      onPressed: () async {
        EasyLoading.show(status: 'Loading...');
        print("isSubmitting: ${controller.isSubmitting.value}");
        if (controller.isSubmitting.value) return;

        // Disable the button to prevent multiple taps
        controller.isSubmitting.value = true;

        await Future.delayed(Duration(milliseconds: 100));

        print("selectedPartyCode: ${controller.selectedPartyCode.value}");
        print("selectedCustType: ${controller.selectedCustType.value}");

        await controller.fetchCurrentLocation();
        if (controller.selectedPartyCode.value == "" &&
            controller.selectedCustType.value == 2) {
          showToastMessage("Please select company");
          controller.isSubmitting.value = false;
          EasyLoading.dismiss();
        } else if (controller.selectedPartyCode.value == "" &&
            (controller.selectedCustType.value == 3 ||
                controller.selectedCustType.value == 4)) {
          showToastMessage("Please select ss");
          controller.isSubmitting.value = false;
          EasyLoading.dismiss();
        } else if (controller.selectedPartyCode.value == "" &&
            controller.selectedCustType.value == 5) {
          showToastMessage("Please select distributor");
          controller.isSubmitting.value = false;
          EasyLoading.dismiss();
        } else {
          if (controller.accuracy.value == 0.0 ||
              controller.accuracy.value > controller.requiredAccuracy.value) {
            await controller.fetchCurrentLocation();
            showToastMessage(
                "Wait while getting your location \n If error persist then set location to high accuracy");
            controller.isSubmitting.value = false;
            EasyLoading.dismiss();
          } else {
            EasyLoading.show(status: 'Loading...');
            print("Order placed");
            showToastMessage("Adding order");
            await controller.addOrderToDB();
            controller.isSubmitting.value = false;
          }
        }
      },
    );
  }
}

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final bool isEnabled;

  CustomTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextFormField(
        style: CustomTextStyles.titleSmallBluegray900,
        controller: controller,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
          contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
          border: InputBorder.none,
        ),
        enabled: isEnabled,
      ),
    );
  }
}

class CustomTextFieldTelephoneOrder extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final bool readOnly;
  final bool isChecked;
  final Function(bool)? onChanged;

  CustomTextFieldTelephoneOrder({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.readOnly = false,
    required this.isChecked,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final placePaymentController = Get.put(PlaceOrderController());

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextFormField(
              readOnly: readOnly,
              style: CustomTextStyles.titleSmallBluegray900,
              controller: controller,
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
                contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
                border: InputBorder.none,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              placePaymentController.toggleTelephoneCheckbox();
              onChanged
                  ?.call(placePaymentController.isTelephoneOrderChecked.value);
            },
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Obx(
                () => placePaymentController.isTelephoneOrderChecked.value
                    ? Icon(Icons.check_box_outlined)
                    : Icon(Icons.check_box_outline_blank),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CustomTextFieldPackagingCharge extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final bool readOnly;
  final bool isChecked;
  final Function(bool)? onChanged;
  final FocusNode focusNode = FocusNode();

  CustomTextFieldPackagingCharge({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
    this.readOnly = false,
    required this.isChecked,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final placePaymentController = Get.put(PlaceOrderController());

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextFormField(
              keyboardType: TextInputType.number,
              readOnly: !isChecked,
              style: CustomTextStyles.titleSmallBluegray900,
              controller: controller,
              onChanged: (value) {
                placePaymentController.updatePackagingCharge(value);
              },
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
                contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
                border: InputBorder.none,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              placePaymentController.togglePackagingCheckbox();
              onChanged
                  ?.call(placePaymentController.isPackagingChargeChecked.value);

              print(
                  "isPackagingChargeChecked: ${placePaymentController.isPackagingChargeChecked.value}");

              if (placePaymentController.isPackagingChargeChecked.value ==
                  true) {
                placePaymentController.packagingChargeController.value.text =
                    "${placePaymentController.packagingChargeValue.value}";
                FocusScope.of(context).requestFocus(focusNode);
              } else {
                placePaymentController.packagingChargeController.value.text =
                    "";
                placePaymentController.updatePackagingCharge("0");
                FocusManager.instance.primaryFocus?.unfocus();
              }
            },
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Obx(
                () => placePaymentController.isPackagingChargeChecked.value
                    ? Icon(Icons.check_box_outlined)
                    : Icon(Icons.check_box_outline_blank),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
