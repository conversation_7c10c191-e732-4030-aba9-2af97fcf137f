// ignore_for_file: unnecessary_import

import 'package:sfm_new/data/models/selectionPopupModel/selection_popup_model.dart';
import '../../../core/app_export.dart';


class PlacePaymentModel {
  Rx<List<SelectionPopupModel>> dropdownItemList = Rx([
    SelectionPopupModel(
      id: 1,
      title: "Item One",
      isSelected: true,
    ),
    SelectionPopupModel(
      id: 2,
      title: "Item Two",
    ),
    SelectionPopupModel(
      id: 3,
      title: "Item Three",
    )
  ]);
}
