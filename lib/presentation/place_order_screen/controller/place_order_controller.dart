// ignore_for_file: unused_local_variable, unnecessary_null_comparison, 

import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geolocator/geolocator.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/product_screen/controller/product_controller.dart';
import 'package:sfm_new/presentation/product_screen/models/product_model.dart';
import 'package:sfm_new/presentation/place_order_screen/models/place_order_model.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/presentation/super_stockist_screen/controller/super_stockist_controller.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/call_model.dart';
import 'package:sfm_new/sfmDatabase/models/config_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:sfm_new/sfmDatabase/models/orders_model.dart';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';

class PlaceOrderController extends GetxController {
  TextEditingController qtyController = TextEditingController();
  TextEditingController amountController = TextEditingController();
  TextEditingController distributorController = TextEditingController();

  TextEditingController telephoneOrderController = TextEditingController();

  var packagingChargeController = TextEditingController().obs;

  // TextEditingController packagingChargeController = TextEditingController();
  TextEditingController transporterNameController = TextEditingController();
  TextEditingController notesController = TextEditingController();

  Rx<PlacePaymentModel> placePaymentModelObj = PlacePaymentModel().obs;

  Rx<bool> packagingCharge = false.obs;
  RxDouble packagingChargeValue = 0.0.obs;
  SelectionPopupModel? selectedDropDownValue;
  var isTelephoneOrderChecked = false.obs;
  var isPackagingChargeChecked = false.obs;

  final ProductController cartController = Get.find<ProductController>();
  final SuperStockistController ssController =
      Get.find<SuperStockistController>();

  RxString checkinType = ''.obs;
  RxString fetchedToken = ''.obs;
  RxString employeeCode = ''.obs;

  final RxBool isLoading = false.obs;
  final Dio dio = Dio();
  RxDouble accuracy = 0.0.obs;
  RxDouble requiredAccuracy = 0.0.obs;

  RxDouble latitude = 0.0.obs;
  RxDouble longitude = 0.0.obs;

  List<Map<String, dynamic>> localProducts = [];

  RxList<Customer> customerList = <Customer>[].obs;
  RxList<Config> configList = <Config>[].obs;

  RxBool isFromBeat = false.obs;

  RxString selectedCustName = ''.obs;

  RxInt selectedCustType = 0.obs;

  RxString selectedClientCode = "".obs;
  RxString selectedCustRelationCode = "".obs;
  RxString selectedCustRelationName = "".obs;
  RxString selectedPartyCode = "".obs;

  RxDouble finalAmountWithGST = 0.0.obs;

  var grandTotalAmount = 0.0.obs;
  var baseTotalAmount = 0.0.obs;

  var isSubmitting = false.obs;

  @override
  void onInit() async {
    super.onInit();

    EasyLoading.show(status: 'Loading...');

    isFromBeat.value =
        await SharedPrefManager.instance.getBool(ConstantValues.isFromBeat) ??
            false;

    selectedCustName.value = await SharedPrefManager.instance
            .getString(ConstantValues.selectedCustName) ??
        '';

    selectedCustType.value = await SharedPrefManager.instance
            .getInt(ConstantValues.selectedCustType) ??
        0;

    selectedClientCode.value = await SharedPrefManager.instance
            .getString(ConstantValues.selectedCustCode) ??
        '';

    selectedCustRelationCode.value = await SharedPrefManager.instance
            .getString(ConstantValues.selectedCustRelationCode) ??
        '';
    print("selectedCustRelationCode: ${selectedCustRelationCode}");

  

    requiredAccuracy.value = await SharedPrefManager.instance
            .getDouble(ConstantValues.requiredAccuracy) ??
        500;

    await fetchCustomers();
    await fetchConfig();
    await fetchEmployeeData();

    await fetchCurrentLocation();

    if (cartController.isOrderProductive.value == true) {
      addProducts(cartController.cartList);
    } else {
      showToastMessage("Products not fetched");
    }

    baseTotalAmount.value = cartController.totalAmount.value;

    selectedCustRelationName.value =
        getCustomerNameFromCode(selectedCustRelationCode.value) ?? "";
    print("selectedCustRelationName: ${selectedCustRelationName.value}");
  }

  Future<void> fetchCurrentLocation() async {
    try {
      final location = await Geolocator.getCurrentPosition();
      print("Location: ${location.latitude}, ${location.longitude}");

      accuracy.value = location.accuracy;
      latitude.value = location.latitude;
      longitude.value = location.longitude;
    } catch (e) {

    }
    EasyLoading.dismiss();
  }

  @override
  void onClose() {
    super.onClose();
    telephoneOrderController.dispose();
    packagingChargeController.value.dispose();
    transporterNameController.dispose();
    notesController.dispose();
  }

  void toggleTelephoneCheckbox() {
    isTelephoneOrderChecked.value = !isTelephoneOrderChecked.value;
  }

  void togglePackagingCheckbox() {
    isPackagingChargeChecked.value = !isPackagingChargeChecked.value;
  }

  void updatePackagingCharge(String value) {
    double charge = double.tryParse(value) ?? 0.0;
    packagingChargeValue.value = charge;
    calculateGrandTotal();
  }

  void calculateGrandTotal() {
    // Assuming you have other amounts to add to the grand total
    // double otherCharges = ...;
    print("baseTotalAmount: ${baseTotalAmount.value}");
    print("packagingChargeValue: ${packagingChargeValue.value}");
    cartController.totalAmount.value =
        baseTotalAmount.value + packagingChargeValue.value;
    print("cartController.totalAmount: ${cartController.totalAmount.value}");
  }

  Future<String?> fetchEmployeeData() async {
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.employee) ??
            "";
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];
      employeeCode.value = empCode!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> fetchCustomers() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCustomer}");
    print(maps);
    customerList.value = List.generate(maps.length, (index) {
      return Customer.fromMap(maps[index]);
    });
    print("customerList: ${customerList}");
  }

  Future<void> fetchConfig() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableConfig}");
    print(maps);
    configList.value = List.generate(maps.length, (index) {
      return Config.fromJson(maps[index]);
    });
    print("configList: ${configList}");

    if (configList.length != 0) {
      // Find the packaging charge configuration and set the value
      final packagingChargeConfig = configList.firstWhere(
        (config) => config.config_key == 'packaging_charge',
      );

      if (packagingChargeConfig != null) {
        print('packagingChargeConfig fetched');
        print("packagingChargeConfig: ${packagingChargeConfig.config_value}");
        packagingChargeValue.value = double.tryParse(
            packagingChargeConfig.config_value ??
                "")!;

        print(
            "packagingChargeController: ${packagingChargeController.value.text}");
      } else {
        print('packagingChargeConfig not fetched');
      }

      print('Packaging Charge Value: ${packagingChargeValue.value}');
    }
  }

  String? getCustomerCodeFromName(String selectedType) {
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmName == selectedType,
        orElse: () => throw "No data found for this ID2",
      );
      return customerType.cmCode;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerCodeFromName: $e');
      }
      return null;
    }
  }

  String? getCustomerNameFromCode(String selectedType) {
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmCode == selectedType,
        orElse: () => throw "No data found for this Name",
      );
      return customerType.cmName;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerNameFromCode: $e');
      }
      return null;
    }
  }

  String generateUUID() {
    var uuid = Uuid();
    return uuid.v4();
  }

  Future<void> addOrderToDB() async {
    final db = await DatabaseProvider.database;

    String formattedDateTime = DateFormat("HH:mm:ss").format(DateTime.now());
    print(formattedDateTime);

    double packagingCharge =
        double.tryParse('${packagingChargeController.value.text}') ?? 0.0;
    print("packagingCharge: ${packagingCharge}");

    double transportationCharge = 0.0;

    DateTime now = DateTime.now();
    String dateString = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);
    print("dateString place order: ${dateString}"); 

    String callStartTime = await SharedPrefManager.instance
            .getString(ConstantValues.callStartTime) ??
        "";

    String uuid = generateUUID();
    var call = CallData(
      callCode: uuid,
      callClientCode: selectedClientCode.value,
      callEmpCode: employeeCode.value,
      callLat: latitude.value,
      callLong: longitude.value,
      callAccuracy: accuracy.value,
      callPartyCode: selectedPartyCode.value,
      callTotalQTY: cartController.totalQty.value,
      callGrandTotal: (finalAmountWithGST.value + packagingChargeValue.value),
      callOrderReasonID: 0,
      callOrderType: 1,
      callRemark: '${notesController.text}',
      callPackagingCharge: packagingCharge,
      callTransportationCharge: transportationCharge,
      callTransporterName: '${transporterNameController.text}',
      callStartTime: callStartTime,
      callStopTime: formattedDateTime,
      callOrderSign: "",
      callCreatedAt: dateString,
      callIsTelephonic: isTelephoneOrderChecked.value == true ? 1 : 0,
      callSyncStatus: 0,
    );

    print("call: ${call}");

    for (var product in localProducts) {
      String uuidProduct = generateUUID();

      double grandTotalValue =
          double.tryParse(product['grand_total'].toStringAsFixed(2)) ?? 0.0;

      OrderData? order;

      if (grandTotalValue > 0.0) {
        try {
          order = OrderData(
            orderCallCode: uuid,
            orderCode: uuidProduct,
            orderProductCode: product['product_code'],
            orderProductQty: product['quantity'],
            orderProductMrp: product['mrp'],
            orderProductPts: product['pts'],
            orderProductBaseRate: product['rate_basic'],
            orderProductTotalBeforeTax:
                double.parse(product['total_basic_rate'].toStringAsFixed(2)),
            orderProductTotalPriceWithTax:
                double.parse(product['grand_total'].toStringAsFixed(2)),
            orderProductTax: product['gst'],
            orderProductTaxAmount: product['gst_amount'],
            orderPiece: product['pcs'],
            orderBunch: product['bunch'],
            orderBox: product['box'],
            orderProductSyncStatus: 0,
          );

          print("order: ${order}");

          await db.insert(
            '${TableValues.tableOrder}',
            order.toMap(),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
        } catch (e) {
          print("Error inserting order into database: $e");
          showToastMessage("Failed to add order. Please try again.");
          isSubmitting.value = false;
        }
      } else {
        showToastMessage("Order details not fetched 1..");
        isSubmitting.value = false;
      }
    }

    if (localProducts.length > 0) {
      try {
        await db.insert(
          '${TableValues.tableCall}',
          call.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );

        showToastMessage("Order placed successfully");
        await ApiLogger.logError(
          apiName: 'AddCallsIntoDB',
          apiRequestData: call.toMap(),
          apiResponse: 'Error: nil',
        );

        Get.offAllNamed(AppRoutes.homeScreen);
      } catch (error) {
        showToastMessage("Order details not fetched. Please try again");
        await ApiLogger.logError(
          apiName: 'AddCallsIntoDB',
          apiRequestData: call.toMap(),
          apiResponse: 'Error: $error',
        );
        isSubmitting.value = false;
      }
    } else {
      showToastMessage("Order details not fetched. Please try again");
      await ApiLogger.logError(
        apiName: 'AddCallsIntoDB',
        apiRequestData: call.toMap(),
        apiResponse: 'Call error',
      );
      isSubmitting.value = false;
    }
  }

  void addProducts(List<AddProduct> addProductsList) {
    localProducts.clear();
    String uuid = generateUUID();
    double grandTotalWithGST = 0.0;
    for (AddProduct product in addProductsList) {
      if (product.grandTotal! > 0) {
        double gstAmount =
            product.grandTotal!.value * (product.productGst! / 100);
        print("gstAmount: ${gstAmount}");
        grandTotalWithGST = product.grandTotal!.value + gstAmount;
        print("grandTotalWithGST: ${grandTotalWithGST}");
        finalAmountWithGST.value +=
            double.parse(grandTotalWithGST.toStringAsFixed(2));
        double rateBasicValue = 0.0;

        if (selectedCustType.value == 2) {
          rateBasicValue = product.productPtss!.value;
        } else if (selectedCustType.value == 3) {
          rateBasicValue = product.productPtdistributor!.value;
        } else if (selectedCustType.value == 4) {
          rateBasicValue = product.productPtdealer!.value;
        } else if (selectedCustType.value == 5) {
          rateBasicValue = product.productPtretailer!.value;
        }

        localProducts.add({
          "order_code": "${uuid}",
          "product_code": product.productCode,
          "quantity": product.quantity!.value,
          "pcs": product.pcs!.value,
          "box": product.box!.value,
          "bunch": product.bunch!.value,
          "mrp": product.productMrp,
          "pts": rateBasicValue,
          "rate_basic": rateBasicValue,
          "total_basic_rate": product.grandTotal!.value,
          "gst": product.productGst,
          "gst_amount": gstAmount,
          "grand_total": grandTotalWithGST,
        });
      } else {
        showToastMessage("Order details not fetched 2!!");
      }
    }

    print("finalAmountWithGST: ${finalAmountWithGST}");
    print("localProducts: ${localProducts}");
  }
}
