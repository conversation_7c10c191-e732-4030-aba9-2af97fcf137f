// ignore_for_file:

import 'package:sfm_new/sfmDatabase/models/target_model.dart';
import 'controller/report_one_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_five.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

// ignore_for_file: must_be_immutable
class ReportOneScreen extends GetWidget<ReportOneController> {
  ReportOneScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(),
        body: SingleChildScrollView(
          child: Container(
            width: double.maxFinite,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 8),
              child: Obx(
                () {
                  if (controller.targetList.isEmpty) {
                    print(MediaQuery.of(context).size.height);
                    print(AppBar().preferredSize.height * 1.3);
                    return Container(
                      height: MediaQuery.of(context).size.height -
                          AppBar().preferredSize.height * 1.3,
                      child: Center(
                        child: Text(
                          'No targets found',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                    );
                  } else {
                    return Column(
                      children: [
                        SizedBox(height: 16.v),
                        CustomElevatedButton(
                          height: 30.v,
                          text: "Primary Target",
                          buttonStyle: CustomButtonStyles.fillBlue,
                          buttonTextStyle:
                              CustomTextStyles.bodyMediumPoppinsBlack900,
                        ),
                        _primaryTargetTable1(controller.targetList),
                        SizedBox(height: 20),
                        CustomElevatedButton(
                          height: 30.v,
                          text: "Secondary Target",
                          buttonStyle: CustomButtonStyles.fillBlue,
                          buttonTextStyle:
                              CustomTextStyles.bodyMediumPoppinsBlack900,
                        ),
                        _secondaryTargetTable1(controller.targetList),
                        SizedBox(height: 20),
                      ],
                    );
                  }
                },
              ),
            ),
          ),
        ),
        bottomNavigationBar: _buildDownloadReport(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      leadingWidth: 37.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 17.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFive(
        text: "lbl_report".tr,
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildDownloadReport() {
    return CustomElevatedButton(
      text: "Download Report",
      margin: EdgeInsets.only(
        left: 25.h,
        right: 25.h,
        bottom: 28.v,
      ),
      onPressed: () {
        Get.toNamed(AppRoutes.downloadReportScreen);
      },
    );
  }

  Widget _primaryTargetTable1(RxList<Target> targets) {
    return Table(
      border: null,
      defaultColumnWidth: FlexColumnWidth(),
      children: [
        // Table header row
        TableRow(
          children: [
            Container(
              color: Colors.white,
              padding: EdgeInsets.all(4.0),
              alignment: Alignment.center,
              child: Text(
                'Index',
                style: theme.textTheme.labelLarge!.copyWith(
                  color: appTheme.gray60002,
                ),
              ),
            ),
            Container(
              color: Colors.white,
              padding: EdgeInsets.all(4.0),
              alignment: Alignment.center,
              child: Text(
                'Target',
                style: theme.textTheme.labelLarge!.copyWith(
                  color: appTheme.gray60002,
                ),
              ),
            ),
            Container(
              color: Colors.white,
              padding: EdgeInsets.all(4.0),
              alignment: Alignment.center,
              child: Text(
                'Achievement',
                style: theme.textTheme.labelLarge!.copyWith(
                  color: appTheme.gray60002,
                ),
              ),
            ),
            Container(
              color: Colors.white,
              padding: EdgeInsets.all(4.0),
              alignment: Alignment.center,
              child: Text(
                'Percent',
                style: theme.textTheme.labelLarge!.copyWith(
                  color: appTheme.gray60002,
                ),
              ),
            ),
          ],
        ),
        // Table data rows
        for (int i = 0; i < targets.length; i++)
          TableRow(
            children: [
              _buildTableCell(targets[i].type.toString()),
              _buildTableCell(targets[i].primaryTarget.toString()),
              _buildTableCell(targets[i].primaryAchievement.toString()),
              _buildTableCell(targets[i].primaryPercent.toString()),
            ],
          ),
      ],
    );
  }

  Widget _secondaryTargetTable1(RxList<Target> targets) {
    return Table(
      border: null,
      defaultColumnWidth: FlexColumnWidth(),
      children: [
        // Table header row
        TableRow(
          children: [
            Container(
              color: Colors.white,
              padding: EdgeInsets.all(4.0),
              alignment: Alignment.center,
              child: Text(
                'Index',
                style: theme.textTheme.labelLarge!.copyWith(
                  color: appTheme.gray60002,
                ),
              ),
            ),
            Container(
              color: Colors.white,
              padding: EdgeInsets.all(4.0),
              alignment: Alignment.center,
              child: Text(
                'Target',
                style: theme.textTheme.labelLarge!.copyWith(
                  color: appTheme.gray60002,
                ),
              ),
            ),
            Container(
              color: Colors.white,
              padding: EdgeInsets.all(4.0),
              alignment: Alignment.center,
              child: Text(
                'Achievement',
                style: theme.textTheme.labelLarge!.copyWith(
                  color: appTheme.gray60002,
                ),
              ),
            ),
            Container(
              color: Colors.white,
              padding: EdgeInsets.all(4.0),
              alignment: Alignment.center,
              child: Text(
                'Percent',
                style: theme.textTheme.labelLarge!.copyWith(
                  color: appTheme.gray60002,
                ),
              ),
            ),
          ],
        ),
        // Table data rows
        for (int i = 0; i < targets.length; i++)
          TableRow(
            children: [
              _buildTableCell(targets[i].type.toString()),
              _buildTableCell(targets[i].secondaryTarget.toString()),
              _buildTableCell(targets[i].secondaryAchievement.toString()),
              _buildTableCell(targets[i].secondaryPercent.toString()),
            ],
          ),
      ],
    );
  }

  Widget _buildTableCell(String text) {
    return DecoratedBox(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey),
          bottom: BorderSide(color: Colors.grey),
          left: BorderSide(color: Colors.grey), 
          right: BorderSide(color: Colors.grey),
        ),
      ),
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.all(4.0),
        alignment: Alignment.center,
        child: Text(
          text,
          style: theme.textTheme.bodySmall!.copyWith(
            color: appTheme.black900,
          ),
        ),
      ),
    );
  }
}
