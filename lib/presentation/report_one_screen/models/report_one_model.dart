import '../../../core/app_export.dart';


class ReportOneModel {
  Rx<List<String>> radioList = Rx(["lbl_primary", "lbl_secondary"]);
}


class BarData {
  final double primary;
  final double secondary;
  final String primaryLabel;
  final String secondaryLabel;

  BarData({
    required this.primary,
    required this.secondary,
    required this.primaryLabel,
    required this.secondaryLabel,
  });
}
