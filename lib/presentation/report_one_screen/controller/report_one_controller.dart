import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/report_one_screen/models/report_one_model.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/sfmDatabase/models/target_model.dart';

class ReportOneController extends GetxController {
  Rx<ReportOneModel> reportOneModelObj = ReportOneModel().obs;

  Rx<String> salesReport = "".obs;

  final Color leftBarColor = Colors.yellow;
  final Color rightBarColor = Colors.red;
  final Color avgColor = Colors.blue;

  final double width = 7;

  final barData = BarData(
    primary: 20,
    secondary: 30,
    primaryLabel: "Primary",
    secondaryLabel: "Secondary",
  ).obs;

  late List<BarChartGroupData> rawBarGroups;
  late List<BarChartGroupData> showingBarGroups;

  RxBool isLoading = true.obs;

  RxList<Target> targetList = <Target>[].obs;

  @override
  void onInit() async {
    super.onInit();

    EasyLoading.show(status: 'Loading...');
    final barGroup1 = makeGroupData(0, 5, 12);
    final barGroup2 = makeGroupData(1, 16, 12);
    final barGroup3 = makeGroupData(2, 18, 5);
    final barGroup4 = makeGroupData(3, 20, 16);
    final barGroup5 = makeGroupData(4, 17, 6);
    final barGroup6 = makeGroupData(5, 19, 1.5);
    final barGroup7 = makeGroupData(6, 10, 1.5);

    final items = [
      barGroup1,
      barGroup2,
      barGroup3,
      barGroup4,
      barGroup5,
      barGroup6,
      barGroup7,
    ];

    rawBarGroups = items;

    showingBarGroups = rawBarGroups;

    await fetchTarget();
  }

  Future<void> fetchTarget() async {
    targetList.value = await SharedPrefManager.instance.getTargets();
    if (targetList.isNotEmpty) {
      print('Retrieved targets:');
      targetList.forEach((target) {
        print(target.toJson());
      });
    } else {
      print('No targets stored in SharedPreferences.');
    }

    print("retrievedTargets: ${targetList}");
    EasyLoading.dismiss();
  }

  BarChartGroupData makeGroupData(int x, double y1, double y2) {
    return BarChartGroupData(
      barsSpace: 4,
      x: x,
      barRods: [
        BarChartRodData(
          toY: y1,
          color: leftBarColor,
          width: width,
        ),
        BarChartRodData(
          toY: y2,
          color: rightBarColor,
          width: width,
        ),
      ],
    );
  }
}
