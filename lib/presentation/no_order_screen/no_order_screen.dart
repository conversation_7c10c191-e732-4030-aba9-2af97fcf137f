import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';

import 'controller/no_order_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

// ignore_for_file: must_be_immutable
class NoOrderScreen extends GetWidget<NoOrderController> {
  NoOrderScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(
            horizontal: 16.h,
            vertical: 15.v,
          ),
          child: Obx(
            () => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  controller.selectedCustName.value,
                  style: CustomTextStyles.titleLargeBlack900,
                ),
                SizedBox(height: 10.v),
                CustomDropdown(
                  decoration: CustomDropdownDecoration(
                    expandedFillColor: Colors.white,
                    expandedBorder: Border.all(color: Colors.grey[300]!),
                    expandedBorderRadius: BorderRadius.circular(12),
                    closedBorder: Border.all(color: Colors.grey[300]!),
                    closedBorderRadius: BorderRadius.circular(12),
                    listItemStyle: CustomTextStyles.titleSmallBluegray900,
                    headerStyle: CustomTextStyles.titleSmallBluegray900,
                  ),
                  hintText: 'Select Reason',
                  //initialItem: 'Select Reason',
                  items: controller.nonProdReasonList
                      .map((reason) => reason.nprReason!)
                      .toList(), 
                  onChanged: (selectedType) {
                    print('Selected Claim/Complain: $selectedType');
                    controller.reasonID.value =
                        controller.getIDFromReasonName("${selectedType}")!;
                  },
                ),
                SizedBox(height: 10.v),
                CustomTextField(
                  controller: controller.noteController,
                  hintText: "Note",
                  height: 100,
                ),
                SizedBox(height: 5.v),
              ],
            ),
          ),
        ),
        bottomNavigationBar: Obx(
          () => _buildSubmit(),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "lbl_no_order".tr,
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildSubmit() {
    return CustomElevatedButton(
      text: "lbl_submit".tr,
      isDisabled: controller.isSubmitting.value,
      margin: EdgeInsets.only(
        left: 25.h,
        right: 25.h,
        bottom: 21.v,
      ),
      onPressed: () async {
        if (controller.reasonID.value == 0) {
          showToastMessage("Please select reason");
        } else {
          print("isSubmitting: ${controller.isSubmitting.value}");
          if (controller.isSubmitting.value) return;

          // Disable the button to prevent multiple taps
          controller.isSubmitting.value = true;

          await Future.delayed(Duration(milliseconds: 100));

          print("latitude: ${controller.latitude.value}");
          print("longitude: ${controller.longitude.value}");
          print("accuracy: ${controller.accuracy.value}");
          print("requiredAccuracy: ${controller.requiredAccuracy.value}");

          if (controller.latitude.value < 0.0 ||
              controller.longitude.value < 0.0) {
            showToastMessage("Please allow location to continue");
            controller.isSubmitting.value = false;
          } else {
            if (controller.accuracy.value == 0.0 ||
                controller.accuracy.value > controller.requiredAccuracy.value) {
              await controller.fetchCurrentLocation();
              showToastMessage(
                  "Wait while getting your location \n If error persist then set location to high accuracy");
              controller.isSubmitting.value = false;
            } else {
              EasyLoading.show(status: 'Loading...');
              await controller.addOrderToDB();
              controller.isSubmitting.value = false;
            }
          }
        }
      },
    );
  }
}

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;

  CustomTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextFormField(
        style: CustomTextStyles.titleSmallBluegray900,
        controller: controller,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
          contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
          border: InputBorder.none,
        ),
      ),
    );
  }
}
