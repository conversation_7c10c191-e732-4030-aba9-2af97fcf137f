import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/no_order_screen/models/no_order_model.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/call_model.dart';
import 'package:sfm_new/sfmDatabase/models/nonproductive_reason_model.dart';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';


class NoOrderController extends GetxController {
  TextEditingController noteController = TextEditingController();
  Rx<NoOrderModel> noOrderModelObj = NoOrderModel().obs;
  SelectionPopupModel? selectedDropDownValue;
  RxList<NonProductiveReason> nonProdReasonList = <NonProductiveReason>[].obs;
  RxInt reasonID = 0.obs;

  RxDouble requiredAccuracy = 0.0.obs;
  RxDouble accuracy = 0.0.obs;
  RxDouble latitude = 0.0.obs;
  RxDouble longitude = 0.0.obs;

  RxString employeeCode = ''.obs;

  RxString selectedClientCode = "".obs;
  RxString selectedPartyCode = "".obs;
RxString selectedCustName = "".obs;
  RxString selectedShopName = "".obs;

  var isSubmitting = false.obs;

  @override
  void onClose() {
    super.onClose();
    noteController.dispose();
  }

  @override
  void onInit() async {
    super.onInit();

    selectedCustName.value = await SharedPrefManager.instance
            .getString(ConstantValues.selectedCustName) ??
        '';

    selectedClientCode.value = await SharedPrefManager.instance
            .getString(ConstantValues.selectedCustCode) ??
        '';

    selectedShopName.value = await SharedPrefManager.instance
            .getString(ConstantValues.selectedShopName) ??
        '';

    requiredAccuracy.value = await SharedPrefManager.instance
            .getDouble(ConstantValues.requiredAccuracy) ??
        500;

    await fetchEmployeeData();

    await fetchNonProductiveReason();

    await fetchCurrentLocation();
  }

  Future<void> fetchCurrentLocation() async {
    try {
      final location = await Geolocator.getCurrentPosition();
      print("Location: ${location.latitude}, ${location.longitude}");

      accuracy.value = location.accuracy;
      latitude.value = location.latitude;
      longitude.value = location.longitude;
    } catch (e) {
      Get.snackbar(
        "Error",
        "Failed to get location: $e",
        snackPosition: SnackPosition.BOTTOM,
      );
      showToastMessage("Please allow location to proceed");
    }
  }

  Future<String?> fetchEmployeeData() async {
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.employee);
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];
      employeeCode.value = empCode!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> fetchNonProductiveReason() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableNonProductiveReason}");
    nonProdReasonList.value = List.generate(maps.length, (index) {
      return NonProductiveReason.fromMap(maps[index]);
    });
    print("nonProdReasonList: ${nonProdReasonList}");
  }

  int? getIDFromReasonName(String selectedType) {
    try {
      final npr = nonProdReasonList.firstWhere(
        (ccType) => ccType.nprReason == selectedType,
      );
      return npr.nprID;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getIDFromReasonName: $e');
      }
      return null;
    }
  }

  onSelected(dynamic value) {
    for (var element in noOrderModelObj.value.dropdownItemList.value) {
      element.isSelected = false;
      if (element.id == value.id) {
        element.isSelected = true;
      }
    }
    noOrderModelObj.value.dropdownItemList.refresh();
  }

  String generateUUID() {
    var uuid = Uuid();
    return uuid.v4();
  }

  Future<void> addOrderToDB() async {
    final db = await DatabaseProvider.database;

    String formattedDateTime = DateFormat("HH:mm:ss").format(DateTime.now());
    print(formattedDateTime);

    DateTime now = DateTime.now();
    String dateString = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);
    print("dateString no order: ${dateString}");

    String callStartTime = await SharedPrefManager.instance
            .getString(ConstantValues.callStartTime) ??
        "";

    String uuid = generateUUID();
    var call = CallData(
      callCode: uuid,
      callClientCode: selectedClientCode.value,
      callEmpCode: employeeCode.value,
      callLat: latitude.value,
      callLong: longitude.value,
      callAccuracy: accuracy.value,
      // callPartyCode: "1",
      callTotalQTY: 0,
      callGrandTotal: 0,
      callOrderReasonID: reasonID.value,
      callOrderType: 0,
      callRemark: '${noteController.text}',
      callPackagingCharge: 0,
      callTransportationCharge: 0,
      callTransporterName: '',
      callStartTime: callStartTime,
      callStopTime: formattedDateTime,
      callOrderSign: "",
      callCreatedAt: dateString,
      callIsTelephonic: 0,
      callSyncStatus: 0,
    );

    print("call: ${call}");

    await db.insert(
      '${TableValues.tableCall}',
      call.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    showToastMessage("Order placed successfully");
    Get.offAllNamed(AppRoutes.homeScreen);
  }
}
