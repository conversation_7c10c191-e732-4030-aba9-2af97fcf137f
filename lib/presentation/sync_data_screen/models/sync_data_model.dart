import 'package:get/get_rx/src/rx_types/rx_types.dart';


class SyncDataModel {}

class TotalTask {
  String taskName;
  RxInt taskStatus; // 1 - processing, 2 - completed, 3 - failed
  int taskType; // 1 - download, 2 - upload

  TotalTask({
    required this.taskName,
    required this.taskStatus,
    required this.taskType,
  });

  @override
  String toString() {
    return 'TotalTask { taskName: $taskName, taskStatus: $taskStatus, taskType: $taskType }';
  }
}
