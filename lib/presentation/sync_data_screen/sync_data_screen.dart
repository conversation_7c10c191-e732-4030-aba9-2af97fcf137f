import 'package:sfm_new/data/constant.dart';

import 'controller/sync_data_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:flutter_animation_progress_bar/flutter_animation_progress_bar.dart';

// ignore_for_file: must_be_immutable
class SyncDataScreen extends GetWidget<SyncDataController> {
  SyncDataScreen({Key? key})
      : super(
          key: key,
        );

  int completedOrFailedCount = 0;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(),
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(horizontal: 16.h),
          child: Column(
            children: [
              SizedBox(height: 10.v),
              _buildSyncProcessStatus(),
              SizedBox(height: 8.v),
              CustomProgressBar(),
              _buildTasksList(),
              Obx(
                () => Visibility(
                  visible: controller.isDailyLogoutDone == true ||
                          controller.tasks.length == 0
                      ? false
                      : controller.progress.value !=
                              controller.tasksProgress.length.toDouble()
                          ? false
                          : controller.progress.value ==
                                  controller.tasksProgress.length.toDouble()
                              ? true
                              : false,
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width * 0.5,
                      height: 45.0,
                      child: ElevatedButton(
                        onPressed: () {
                          print("to go home screen called");
                          if (controller.tasks
                              .any((task) => task.taskStatus.value == 3)) {
                            controller.syncAllData();
                          } else {
                            if (controller.isDailyLogoutDone == true) {
                              controller.checkout();
                            } else {
                              Get.offNamed(AppRoutes.homeScreen);
                            }
                          }
                        },
                        child: Text(
                          controller.tasks.length == 0
                              ? "Syncing Data 1"
                              : controller.tasks
                                      .any((task) => task.taskStatus.value == 3)
                                  ? "Sync Again"
                                  : "Go to Home",
                          style: CustomTextStyles.titleSmallBluegray900logout,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  int findNextProcessingIndex(
      List<Map<String, dynamic>> items, int currentIndex) {
    for (int i = currentIndex + 1; i < items.length; i++) {
      if (items[i]["status"] == "processing") {
        return i;
      }
    }
    return -1;
  }

  Widget buildItemWidget(Map<String, dynamic> item, String status, int index) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          // Your row content here
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "Sync Data",
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildSyncProcessStatus() {
    return Obx(
      () => Container(
        padding: EdgeInsets.symmetric(
          horizontal: 16.h,
          vertical: 13.v,
        ),
        decoration: AppDecoration.outlineBlack90012.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            Visibility(
              visible: controller.progress.value !=
                  controller.tasksProgress.length.toDouble(),
              child: RotationTransition(
                turns: AlwaysStoppedAnimation(controller.progress.value /
                    controller.tasksProgress.length.toDouble()),
                child: CustomImageView(
                  svgPath: ImageConstant.imgSync31,
                  height: 45.adaptSize,
                  width: 45.adaptSize,
                  margin: EdgeInsets.only(top: 1.v),
                ),
              ),
            ),
            Visibility(
              visible: controller.progress.value ==
                      controller.tasksProgress.length.toDouble() &&
                  !controller.tasks.any((task) => task.taskStatus.value == 3),
              child: CustomImageView(
                svgPath: ImageConstant.imgSyncCompleted,
                height: 45.adaptSize,
                width: 45.adaptSize,
                margin: EdgeInsets.only(top: 1.v),
              ),
            ),
            Visibility(
              visible: controller.progress.value ==
                      controller.tasksProgress.length.toDouble() &&
                  controller.tasks.any((task) => task.taskStatus.value == 3),
              child: CustomImageView(
                svgPath: ImageConstant.imgSyncFailed,
                height: 45.adaptSize,
                width: 45.adaptSize,
                margin: EdgeInsets.only(top: 1.v),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 25.h,
                top: 13.v,
                bottom: 12.v,
              ),
              child: Text(
                controller.tasks.length == 0
                    ? "Syncing Data"
                    : controller.progress.value !=
                            controller.tasksProgress.length.toDouble()
                        ? "Syncing Data"
                        : controller.tasks
                                .any((task) => task.taskStatus.value == 3)
                            ? "Sync Failed"
                            : "Sync Completed",
                style: CustomTextStyles.titleMediumOnPrimaryContainer18,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTasksList() {
    return Obx(
      () => Expanded(
        child: ListView.builder(
          physics: BouncingScrollPhysics(),
          shrinkWrap: true,
          controller: controller.scrollController,
          itemCount: controller.tasks.length,
          itemBuilder: (context, index) => CustomTasksListItem(index: index),
        ),
      ),
    );
  }
}

class CustomTasksListItem extends StatelessWidget {
  final int index;
  final SyncDataController controller = Get.put(SyncDataController());

  CustomTasksListItem({
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 1.h,
        right: 4.h,
        bottom: 10.h,
      ),
      child: Container(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(left: 8.h, top: 0, bottom: 0.v),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 20,
                          child: ColorFiltered(
                            colorFilter: ColorFilter.mode(
                              theme.colorScheme.primary,
                              BlendMode.srcIn,
                            ),
                            child: controller.tasks[index].taskType == 1
                                ? Image.asset('assets/images/img_download.png')
                                : Image.asset('assets/images/img_upload.png'),
                          ),
                        ),
                        SizedBox(width: 20.0),
                        Text("-"),
                        SizedBox(width: 20.0),
                        Container(
                          width: 125,
                          child: Text(controller.tasks[index].taskName,
                              style: CustomTextStyles.titleSmallBluegray900),
                        ),
                        SizedBox(width: 8.0),
                        Text("-"),
                        SizedBox(width: 20.0),
                        Obx(
                          () {
                            return Text(
                              controller.tasks[index].taskStatus.value == 1
                                  ? "Processing"
                                  : controller.tasks[index].taskStatus.value ==
                                          2
                                      ? "Completed"
                                      : "Failed",
                              style: TextStyle(
                                fontFamily: ConstantValues.OpenSans,
                                fontSize: 13.fSize,
                                fontWeight: FontWeight.w600,
                                color:
                                    controller.tasks[index].taskStatus.value ==
                                            1
                                        ? Colors.orange
                                        : controller.tasks[index].taskStatus
                                                    .value ==
                                                2
                                            ? Colors.green
                                            : Colors.red,
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomProgressBar extends StatelessWidget {
  final SyncDataController controller = Get.put(SyncDataController());

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Padding(
        padding: const EdgeInsets.only(top: 8, bottom: 8),
        child: FAProgressBar(
          currentValue: controller.progress.value,
          size: 10,
          maxValue: controller.tasksProgress.length.toDouble(),
          changeColorValue: 100,
          changeProgressColor: Colors.pink,
          backgroundColor:
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
          progressColor: Theme.of(context).colorScheme.primary,
          // animatedDuration: const Duration(seconds: 1),
          direction: Axis.horizontal,
          verticalDirection: VerticalDirection.up,
          displayText: null,
          formatValueFixed: 2,
        ),
      ),
    );
  }
}
