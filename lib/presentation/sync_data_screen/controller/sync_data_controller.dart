// ignore_for_file: unused_catch_clause

import 'dart:convert';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/convert_data_type.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/sync_data_screen/models/sync_data_model.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/expense_model.dart';
import 'package:sfm_new/sfmDatabase/models/target_model.dart';
import 'dart:async';
import 'package:sqflite/sqflite.dart';

class SyncDataController extends GetxController
    with GetSingleTickerProviderStateMixin {
  Rx<SyncDataModel> syncDataModelObj = SyncDataModel().obs;

  final progress = 0.0.obs;
  final progressMaxValue = 0.obs;
  final RxBool isLoading = false.obs;
  RxString token = ''.obs;

  RxList<TotalTask> tasks = <TotalTask>[].obs;
  RxList<TotalTask> tasksProgress = <TotalTask>[].obs;

  final Dio dio = Dio();

  final String taskAddCustomer = "Add Customer";
  final String taskAddOrder = "Add Order";
  final String taskAddExpense = "Add Expense";
  final String taskAddLeave = "Add Leave";
  final String taskAddComplain = "Add Complain";
  final String taskAddPayment = "Add Payment";

  final String taskTerritory = "Territory";
  final String taskProductBrand = "Product Brand";
  final String taskProductType = "Product Type";
  final String taskUOM = "UOM";
  final String taskProduct = "Product";
  final String taskMarketType = "Market Type";
  final String taskPriceGroup = "Price Group";
  final String taskCustomerCategory = "Customer Category";
  final String taskGrade = "Grade";
  final String taskBeat = "Beat";
  final String taskCustomer = "Customer";
  final String taskBank = "Bank";
  final String taskClaimComplainType = "Claim Complain Type";
  final String taskClaimComplain = "Claim Complain";
  final String taskExpenseType = "Expense Type";
  final String taskExpense = "Expense";
  final String taskNonproductiveReason = "Non-Productive Reason";
  final String taskPaymentType = "Payment Type";
  final String taskPaymentCondition = "Payment Condition";
  final String taskPaymentCollection = "Payment Collection";
  final String taskLeave = "Leave";
  final String taskCall = "Call";
  final String taskOrders = "Order";
  final String taskTarget = "Target";
  final String taskConfig = "Config";
  final String taskLanguage = "Language";
  final String taskDashboard = "Dashboard";

  RxString employeeToken = "".obs;
  bool isCustomerAPISuccess = false;
  bool isExpenseAPISuccess = false;
  bool isLeaveAPISuccess = false;
  bool isComplainAPISuccess = false;
  bool isPaymentAPISuccess = false;
  bool isOrderCallAPISuccess = false;

  RxString employeeCode = ''.obs;
  var imagePath = ''.obs;

  int syncDays = 0;

  final _scrollController = ScrollController();
  ScrollController get scrollController => _scrollController;

  bool isDailyLogoutDone = false;

  RxDouble lat = 0.0.obs;
  RxDouble long = 0.0.obs;
  RxDouble accuracy = 0.0.obs;
  RxDouble requiredAccuracy = 0.0.obs;

  RxString locationMessage = ''.obs;

  var localizedStrings = <String, String>{}.obs;

  final Connectivity _connectivity = Connectivity();

  @override
  void onInit() async {
    super.onInit();

    syncAllData();
  }

  String translate(String key) {
    return localizedStrings[key] ?? key;
  }

  void syncAllData() async {
    progress.value = 0;
    progressMaxValue.value = 0;

    token.value = '';

    tasks.value = [];
    tasksProgress.value = [];

    requiredAccuracy.value = await SharedPrefManager.instance
            .getDouble(ConstantValues.requiredAccuracy) ??
        500;

    isDailyLogoutDone = await SharedPrefManager.instance
            .getBool(ConstantValues.isDailyLogOut) ??
        false;

    print("isDailyLogoutDone: ${isDailyLogoutDone}");

    await setSyncDays();
    await fetchToken();
    await getLocation();
    await addTasks();

    await checkInternetConnectivity();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      scrollToBottom();
    });

    if (isDailyLogoutDone == true) {}
  }

  Future<bool> checkInternetConnectivity() async {
    var connectivityResult = await _connectivity.checkConnectivity();

    if (connectivityResult != ConnectivityResult.none) {
      try {
        // Check if there is actual internet access by pinging a well-known server (e.g., Google)
        final result = await InternetAddress.lookup('google.com');
        if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
          // Internet is available, proceed with syncing customers
          await syncCustomersWithServer();
          await syncCallsWithServer();
          await syncExpensesWithServer();
          await syncLeavesWithServer();
          await syncComplainsWithServer();
          await syncPaymentsWithServer();

          await fetchEmployeeData();
          await getTerritory();
          await getProductBrand();
          await getProductType();
          await getUOM();
          await getProduct();
          await getMarketType();
          await getPriceGroup();
          await getCustomerCategory();
          await getGrade();
          await getBeats();
          await getCustomer();
          await getBank();
          await getClaimComplainType();
          await getClaimComplain();
          await getExpenseType();
          await getExpense();
          await getNonProductiveReason();
          await getPaymentType();
          await getPaymentCondition();
          await getPaymentCollection();
          await getLeave();
          await getCall();
          await getOrder();
          await getTarget();
          await getConfig();
          await getLanguage();
          await getDashboardData();
          return true;
        } else {
         
          print("Connected to a network but no internet access.");
          editTask(taskProductBrand, 3); 
          showToastMessage("No internet access. Please check your connection.");
          await ApiLogger.logError(
            apiName: 'checkInternetConnectivity',
            apiRequestData: null,
            apiResponse: 'Connected to a network but no internet access.',
          );
          return false;
        }
      } on SocketException catch (e) {
        
        print(e);
        print(
            "Connected to a network but no internet access (SocketException).");
        editTask(taskProductBrand, 3);
        showToastMessage("No internet access. Please check your connection.");

        await ApiLogger.logError(
          apiName: 'checkInternetConnectivity',
          apiRequestData: null,
          apiResponse: 'Connected to a network but no internet access.',
        );
        return false;
      }
    } else {
     
      print("No network connectivity.");
      editTask(taskProductBrand, 3);
      showToastMessage("No network connection. Please check your network.");
      await ApiLogger.logError(
        apiName: 'checkInternetConnectivity',
        apiRequestData: null,
        apiResponse: 'No network connection. Please check your network.',
      );
      return false;
    }
  }

  void scrollToBottom() async {
    await _scrollController.animateTo(
      _scrollController.position.maxScrollExtent + 20,
      duration: Duration(milliseconds: 500),
      curve: Curves.easeOut,
    );
  }

  @override
  void onClose() {
    super.onClose();
  }

  void reloadScreen() {
    update();
  }

  Future<void> getLocation() async {
    Position position;
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();

    if (!serviceEnabled) {
      locationMessage.value = "Location services are disabled";
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        locationMessage.value = 'Location permissions are denied.';
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      locationMessage.value =
          'Location permissions are permanently denied, we cannot request permissions.';
      return;
    }

    if (permission == LocationPermission.unableToDetermine) {}

    position = await Geolocator.getCurrentPosition();
    locationMessage.value =
        'Latitude: ${position.latitude}, Longitude: ${position.longitude}';

    lat.value = position.latitude;
    long.value = position.longitude;
    accuracy.value = position.accuracy;
  }

  Future<void> checkout() async {
    try {
      isLoading.value = true;

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      print("headers: ${headers}");
      print("token.value: ${token.value}");
      print("latitude: ${lat.value}");
      print("longitude: ${long.value}");
      print("accuracy checkout sync: ${accuracy.value}");
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.checkoutEndpoint),
        data: {
          'latitude': '${lat.value}',
          'longitude': '${long.value}',
        },
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.data);
      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            showToastMessage("Logged out sucessfully");
            Get.offNamed(AppRoutes.loginOneScreen);
          }
        } else {
          print('Response data is null');
        }
      } catch (e) {
        print('Error parsing response data: $e');
      }
    } catch (error) {
      isLoading.value = false;
      if (error is DioException) {
        if (error.response != null) {
          print(error.response?.data);
          var errorMessage = error.response?.data['message'];
          print('Error message: $errorMessage');
        } else {
          print('Dio error: $error');
        }
      } else {
        isExpenseAPISuccess = false;
        editTask(taskAddExpense, 3);
        print('Error: $error');
      }
    }
  }

  Future<void> addTasks() async {
    // Define tasks to add
    List<TotalTask> tasksToAdd = [
      TotalTask(taskName: taskAddCustomer, taskStatus: RxInt(1), taskType: 2),
      TotalTask(taskName: taskAddOrder, taskStatus: RxInt(1), taskType: 2),
      TotalTask(taskName: taskAddExpense, taskStatus: RxInt(1), taskType: 2),
      TotalTask(taskName: taskAddLeave, taskStatus: RxInt(1), taskType: 2),
      TotalTask(taskName: taskAddComplain, taskStatus: RxInt(1), taskType: 2),
      TotalTask(taskName: taskAddPayment, taskStatus: RxInt(1), taskType: 2),
      TotalTask(taskName: taskTerritory, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskProductBrand, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskProductType, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskUOM, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskProduct, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskMarketType, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskPriceGroup, taskStatus: RxInt(1), taskType: 1),
      TotalTask(
          taskName: taskCustomerCategory, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskGrade, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskBeat, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskCustomer, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskBank, taskStatus: RxInt(1), taskType: 1),
      TotalTask(
          taskName: taskClaimComplainType, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskClaimComplain, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskExpenseType, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskExpense, taskStatus: RxInt(1), taskType: 1),
      TotalTask(
          taskName: taskNonproductiveReason, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskPaymentType, taskStatus: RxInt(1), taskType: 1),
      TotalTask(
          taskName: taskPaymentCondition, taskStatus: RxInt(1), taskType: 1),
      TotalTask(
          taskName: taskPaymentCollection, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskLeave, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskCall, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskOrders, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskTarget, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskConfig, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskLanguage, taskStatus: RxInt(1), taskType: 1),
      TotalTask(taskName: taskDashboard, taskStatus: RxInt(1), taskType: 1),
    ];

    // Add tasks to the repository or perform any other actions
    for (TotalTask task in tasksToAdd) {
      tasksProgress.add(task);
    }
    print("tasks total data: ${tasksProgress}");
  }

  Future<void> addTask(TotalTask task) async {
    // Add the task to the repository or perform any other actions
    tasks.add(task);
    print("Task added: ${task.taskName}");
  }

  void editTask(String taskName, int taskStatus) {
    int index = tasks.indexWhere((task) => task.taskName == taskName);
    if (index != -1) {
      tasks[index].taskStatus.value = taskStatus;
      print("Task: ${tasks[index].taskStatus}");
    } else {
      print('Task with name $taskName not found');
    }
  }

  // Method to fetch the token asynchronously
  Future<void> fetchToken() async {
    String? fetchedToken = await getTokenFromSharedPreferences();
    if (fetchedToken != null) {
      token.value = getTokenFromInput(fetchedToken);
    }
  }

  Future<String?> fetchEmployeeData() async {
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.employee) ??
            "";
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];
      employeeCode.value = empCode!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future setSyncDays() async {
    syncDays =
        (await SharedPrefManager.instance.getInt(ConstantValues.syncDays))!;
    print("syncDays: ${syncDays}");
  }

  Future<String?> getTokenFromSharedPreferences() async {
    return await SharedPrefManager.instance.getString(ConstantValues.token);
  }

  String getTokenFromInput(String input) {
    List<String> parts = input.split('|');
    if (parts.length > 1) {
      return parts[1];
    } else {
      return input; 
    }
  }

  Future fetchProgressValueAndStore() async {
    if (tasks.any((task) => task.taskStatus.value == 3)) {
      SharedPrefManager.instance
          .setBool(ConstantValues.isSyncSuccessful, false);
    } else {
      SharedPrefManager.instance.setBool(ConstantValues.isSyncSuccessful, true);
    }

    if (isDailyLogoutDone == true) {
      print(
          "isDailyLogoutDone fetchProgressValueAndStore: ${isDailyLogoutDone}");
      if (accuracy.value > requiredAccuracy.value) {
        await getLocation();
        showToastMessage(
            "Wait while getting your location \n If error persist then set location to high accuracy");
      } else {
        if (accuracy.value > requiredAccuracy.value) {
          await getLocation();
          showToastMessage(
              "Wait while getting your location \n If error persist then set location to high accuracy");
        } else {
          checkout();
        }
      }
    }
  }

  Future<void> syncCustomersWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedCustomers = await db.query(
        '${TableValues.tableCustomer}',
        where: '${TableValues.customerSyncStatus} = ?',
        whereArgs: [0]);

    TotalTask task =
        TotalTask(taskName: taskAddCustomer, taskStatus: RxInt(1), taskType: 2);
    addTask(task);
    progress.value += 1;

    if (unsyncedCustomers.isNotEmpty) {
      print('Syncing customers with the server...');
      print("unsyncedCustomers: ${unsyncedCustomers}");

      for (final customer in unsyncedCustomers) {
        try {
          print("Customer: ${customer}");
          await addCustomerAPI(customer);

          if (isCustomerAPISuccess == true) {
            await db.update('${TableValues.tableCustomer}',
                {'${TableValues.customerSyncStatus}': 1},
                where: '${TableValues.customerCode} = ?',
                whereArgs: [customer['${TableValues.customerCode}']]);
            print(
                'Customer with ID ${customer['${TableValues.customerCode}']} synced successfully');
            editTask(taskAddCustomer, 2);
            await ApiLogger.logError(
              apiName: 'addCustomerToDB',
              apiRequestData: customer,
              apiResponse:
                  'Customer with ID ${customer['${TableValues.customerCode}']} synced successfully',
            );
          } else {
            print(
                'Error syncing Customer with ID ${customer['${TableValues.customerCode}']}');
            editTask(taskAddCustomer, 3);
            await ApiLogger.logError(
              apiName: 'addCustomerToDB',
              apiRequestData: customer,
              apiResponse:
                  'Error syncing Customer with ID ${customer['${TableValues.customerCode}']}',
            );
          }
        } catch (error) {
          print(
              'Error syncing Customer with ID ${customer['${TableValues.customerCode}']}: $error');
          editTask(taskAddCustomer, 3);
          await ApiLogger.logError(
            apiName: 'addCustomerToDB',
            apiRequestData: customer,
            apiResponse:
                'Error syncing Customer with ID ${customer['${TableValues.customerCode}']}: $error',
          );
        }
      }
    } else {
      print('No customers to sync.');
      editTask(taskAddCustomer, 2);
    }
  }

  Future<void> addCustomerAPI(Map<String, dynamic> customer) async {
    try {
      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> data = {};

      String strImage = "${customer['cm_image_relation']}";
      List<String> strImagesList = strImage.split(",");
      print("strImagesList: ${strImagesList}");

      data = {
        "cm_code": "${customer['cm_code']}",
        "cm_name": "${customer['cm_name']}",
        "cm_mobile": customer['cm_mobile'],
        "cm_mobile2": customer['cm_mobile2'],
        "cm_email": "${customer['cm_email']}",
        "cm_address": "${customer['cm_address']}",
        "cm_pincode": customer['cm_pincode'],
        "cm_gst": "${customer['cm_gst']}",
        "cm_pan": "${customer['cm_pan']}",
        "latitude": "${customer['cm_lat']}",
        "longitude": "${customer['cm_long']}",
        "cm_town_id": customer['cm_town_id'],
        "cm_outstanding_amount": customer['cm_outstanding_amount'],
        "cm_contact_person": "${customer['cm_contact_person']}",
        "cm_area": "${customer['cm_area']}",
        "cm_relation_code": "${customer['cm_relation_code']}",
        "cm_type": customer['cm_type'],
        "beat_code": "${customer['cm_beat_relation']}",
        "birth_date": "${customer['cm_dob']}",
        "anni_date": "${customer['cm_anniversary']}",
        "category_id": customer['cm_category_relation'],
        "grade_id": customer['cm_grade_relation'],
        "shop_images": strImagesList,
      };

      print("data: ${data}");

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };

      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.postAddCustomerEndpoint),
        data: data,
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskAddCustomer, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.data);
      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            isCustomerAPISuccess = true;
            editTask(taskAddCustomer, 2);
          }
        } else {
          isCustomerAPISuccess = false;
          print('Response data is null');
          editTask(taskAddCustomer, 3);
        }
      } catch (e) {
        isCustomerAPISuccess = false;
        print('Error parsing response data: $e');
        editTask(taskAddCustomer, 3);
      }
    } on DioException catch (e) {
      isCustomerAPISuccess = false;
      editTask(taskAddCustomer, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskAddCustomer,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskAddCustomer, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskAddCustomer,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      isCustomerAPISuccess = false;
      editTask(taskAddCustomer, 3);
      print('Unexpected error: $e');
    }
  }

  Future<void> syncCallsWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedCalls = await db.query(
        '${TableValues.tableCall}',
        where: '${TableValues.callSyncStatus} = ?',
        whereArgs: [0]);

    TotalTask task =
        TotalTask(taskName: taskAddOrder, taskStatus: RxInt(1), taskType: 2);
    addTask(task);
    progress.value += 1;

    if (unsyncedCalls.isNotEmpty) {
      print('Syncing calls with the server...');
      print("unsyncedcalls: ${unsyncedCalls}");

      for (final call in unsyncedCalls) {
        try {
          await syncOrdersWithServer(call);
        } catch (error) {
          print(
              'Error1 syncing call with ID ${call['${TableValues.callCode}']}: $error');
          editTask(taskAddOrder, 3);
          await ApiLogger.logError(
            apiName: 'addOrderToDB',
            apiRequestData: call,
            apiResponse:
                'Error1 syncing call with ID ${call['${TableValues.callCode}']}: $error',
          );
        }
      }
    } else {
      print('No calls to sync.');
      editTask(taskAddOrder, 2);
    }
  }

  Future<void> syncOrdersWithServer(Map<String, dynamic> call) async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedOrders = await db.query(
        '${TableValues.tableOrder}',
        where: '${TableValues.orderProductSyncStatus} = ?',
        whereArgs: [0]);

    if (unsyncedOrders.isNotEmpty) {
      print('Syncing orders with the server...');
      print("unsyncedorders: ${unsyncedOrders}");

      String? callCode = call['${TableValues.callCode}'];
      print("callCode: ${callCode}");

      // Filter unsynced orders by matching callCode
      List<Map<String, dynamic>> filteredOrders = unsyncedOrders.where((order) {
        return order[TableValues.orderCallCode] == callCode;
      }).toList();
      print("filteredOrders Home: ${filteredOrders}");

      await placeOrder(call, filteredOrders);

      if (isOrderCallAPISuccess == true) {
        await db.update(
            '${TableValues.tableCall}', {'${TableValues.callSyncStatus}': 1},
            where: '${TableValues.callCode} = ?',
            whereArgs: [call['${TableValues.callCode}']]);
        print(
            'Call with ID ${call['${TableValues.callCode}']} synced successfully');
        editTask(taskAddOrder, 2);
        await ApiLogger.logError(
          apiName: 'addCallToDB',
          apiRequestData: call,
          apiResponse:
              'Call with ID ${call['${TableValues.callCode}']} synced successfully',
        );
      } else {
        print('Error2 syncing call with ID ${call['${TableValues.callCode}']}');
        editTask(taskAddOrder, 3);
        await ApiLogger.logError(
          apiName: 'addCallToDB',
          apiRequestData: call,
          apiResponse:
              'Error2 syncing call with ID ${call['${TableValues.callCode}']}',
        );
      }
    } else {
      print("unsyncedorders: ${unsyncedOrders}");
      int? callType = call['${TableValues.callOrderType}'];
      print("callType: ${callType}");

      if (callType == 0) {
        List<Map<String, dynamic>> filteredOrders = [];
        await placeOrder(call, filteredOrders);

        if (isOrderCallAPISuccess == true) {
          await db.update(
              '${TableValues.tableCall}', {'${TableValues.callSyncStatus}': 1},
              where: '${TableValues.callCode} = ?',
              whereArgs: [call['${TableValues.callCode}']]);
          print(
              'Call with ID ${call['${TableValues.callCode}']} synced successfully');
          editTask(taskAddOrder, 2);
          await ApiLogger.logError(
            apiName: 'addCallToDB',
            apiRequestData: call,
            apiResponse:
                'Call with ID ${call['${TableValues.callCode}']} synced successfully',
          );
        } else {
          print(
              'Error3 syncing call with ID ${call['${TableValues.callCode}']}');
          editTask(taskAddOrder, 3);
          await ApiLogger.logError(
            apiName: 'addCallToDB',
            apiRequestData: call,
            apiResponse:
                'Error3 syncing call with ID ${call['${TableValues.callCode}']}',
          );
        }
      }

      print('No orders to sync.');
    }
  }

  Future<void> placeOrder(
      Map<String, dynamic> call, List<Map<String, dynamic>> orders) async {
    print("placeOrder order: ${orders}");

    List<Map<String, dynamic>> localProducts = [];

    for (var order in orders) {
      localProducts.add({
        "order_code": order['order_code'],
        "product_code": order['order_product_code'],
        "quantity": order['order_product_qty'],
        "pcs": order['order_piece'],
        "box": order['order_box'],
        "bunch": order['order_bunch'],
        "mrp": order['order_product_mrp'],
        "pts": order['order_product_pts'],
        "rate_basic": order['order_product_base_rate'],
        "total_basic_rate": order['order_product_total_before_tax'],
        "gst": order['order_product_tax'],
        "gst_amount": order['order_product_tax_amount'],
        "grand_total": order['order_product_total_price_with_tax'],
      });
    }

    print("localProducts final: ${localProducts}");

    try {
      Map<String, dynamic> data = {};
      if ('${call['call_order_type']}' == '1') {
        print("is productive");
        data = {
          "call_code": call['${TableValues.callCode}'],
          "emp_code": call['${TableValues.callEmpCode}'],
          "client_code": call['${TableValues.callClientCode}'],
          "latitude": call['${TableValues.callLat}'],
          "longitude": call['${TableValues.callLong}'],
          "accuracy": call['${TableValues.callAccuracy}'],
          "total_quantity": call['${TableValues.callTotalQTY}'],
          "party_code": call['${TableValues.callPartyCode}'],
          "grand_total": call['${TableValues.callGrandTotal}'],
          "product_order_type": call['${TableValues.callOrderType}'],
          "start_time": call['${TableValues.callStartTime}'],
          "stop_time": call['${TableValues.callStopTime}'],
          "packaging_charge": call['${TableValues.callPackagingCharge}'],
          "transportation_charge":
              call['${TableValues.callTransportationCharge}'],
          "transportation_name": call['${TableValues.callTransporterName}'],
          "remarks": call['${TableValues.callRemark}'],
          "is_telephonic": call['${TableValues.callIsTelephonic}'],
          "products": localProducts,
        };
      } else {
        print("is not productive");
        data = {
          "call_code": call['${TableValues.callCode}'],
          "emp_code": call['${TableValues.callEmpCode}'],
          "client_code": call['${TableValues.callClientCode}'],
          "latitude": call['${TableValues.callLat}'],
          "longitude": call['${TableValues.callLong}'],
          "accuracy": call['${TableValues.callAccuracy}'],
          // "party_code": call['${TableValues.callPartyCode}'],
          "product_order_type": call['${TableValues.callOrderType}'],
          "start_time": call['${TableValues.callStartTime}'],
          "stop_time": call['${TableValues.callStopTime}'],
          "remarks": call['${TableValues.callRemark}'],
          "reason_id": call['${TableValues.callOrderReasonID}'],
          "is_telephonic": 0,
        };
      }

      print("data: ${data}");
      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };

      // Send POST request
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.postAddOrderEndpoint),
        data: data,
        options: Options(
          headers: headers,
        ),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskAddOrder, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      if (response.statusCode == 200) {
        print('Data posted successfully');
        print('Response: ${response.data}');
        isOrderCallAPISuccess = true;
        editTask(taskAddOrder, 2);
      } else {
        print('Failed to post data');
        isOrderCallAPISuccess = false;
        editTask(taskAddOrder, 3);
      }
    } on DioException catch (e) {
      isOrderCallAPISuccess = false;
      editTask(taskAddOrder, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskAddOrder,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskAddOrder, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskAddOrder,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } finally {}
  }

  Future<void> syncExpensesWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedExpenses = await db.query(
        '${TableValues.tableExpense}',
        where: '${TableValues.expenseSyncStatus} = ?',
        whereArgs: [0]);

    TotalTask task =
        TotalTask(taskName: taskAddExpense, taskStatus: RxInt(1), taskType: 2);
    addTask(task);
    progress.value += 1;

    if (unsyncedExpenses.isNotEmpty) {
      print('Syncing expenses with the server...');
      print("unsyncedExpenses: ${unsyncedExpenses}");
      for (final expense in unsyncedExpenses) {
        Expense expenseData = Expense.fromMap(expense);

        print("expenseData: ${expenseData}");

        if (expenseData.billPicture!.isEmpty) {
          expenseData.billPicture = "Picture not added";
        } else {
          expenseData.billPicture = "Picture added";
        }

        print("expenseData: ${expenseData}");

        try {
          await addExpenseAPI(expense);

          if (isExpenseAPISuccess == true) {
            await db.update('${TableValues.tableExpense}',
                {'${TableValues.expenseSyncStatus}': 1},
                where: '${TableValues.expenseCode} = ?',
                whereArgs: [expense['${TableValues.expenseCode}']]);
            print(
                'Expenses with ID ${expense['${TableValues.expenseCode}']} synced successfully');
            editTask(taskAddExpense, 2);
            await ApiLogger.logError(
              apiName: 'addExpenseToDB',
              apiRequestData: expenseData.toMap(),
              apiResponse:
                  'Expenses with ID ${expense['${TableValues.expenseCode}']} synced successfully',
            );
          } else {
            print(
                'Error syncing expense with ID ${expense['${TableValues.expenseCode}']}');
            editTask(taskAddExpense, 3);
            await ApiLogger.logError(
              apiName: 'addExpenseToDB',
              apiRequestData: expenseData.toMap(),
              apiResponse:
                  'Error syncing expense with ID ${expense['${TableValues.expenseCode}']}',
            );
          }
        } catch (error) {
          print(
              'Error syncing expense with ID ${expense['${TableValues.expenseCode}']}: $error');

          editTask(taskAddExpense, 3);
          await ApiLogger.logError(
            apiName: 'addExpenseToDB',
            apiRequestData: expenseData.toMap(),
            apiResponse:
                'Error syncing expense with ID ${expense['${TableValues.expenseCode}']}: $error',
          );
        }
      }
    } else {
      print('No expenses to sync.');
      editTask(taskAddExpense, 2);
    }
  }

  Future<void> addExpenseAPI(Map<String, dynamic> expense) async {
    print("addExpenseAPI expense : ${expense}");

    try {
      print(progress.value);
      print(tasks.length.toDouble());

      ApiClient.dioClient.options.headers['Authorization'] =
          'Bearer ${token.value}';

      // Define the API endpoint
      String apiUrl = ApiClient.getUrl(ApiClient.postAddExpenseEndpoint);
      print("apiUrl: ${apiUrl}");

      Map<String, dynamic> data = {};

      data = {
        "em_expense_type": "${expense['em_expense_type']}",
        "em_code": "${expense['em_code']}",
        "em_expense_detail": "${expense['em_expense_detail']}",
        "em_amount": "${expense['em_amount']}",
        "em_approved_amount": "${expense['em_approved_amount']}",
        "em_bill_picture": "${expense['em_bill_picture']}",
        "file_extension": "${expense['file_extension']}",
        "latitude": "${expense['em_lat']}",
        "longitude": "${expense['em_long']}",
        "emp_code": "${expense['emp_code']}",
        "em_expense_status": "${expense['em_expense_status']}",
        "em_date": "${expense['em_date']}",
        "em_km": "${expense['em_km']}",
      };

      print("data: ${data}");

      final response = await ApiClient.dioClient.post(apiUrl, data: data);

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            isExpenseAPISuccess = true;
            editTask(taskAddExpense, 2);
          }
        } else {
          print('Response data is null');
          isExpenseAPISuccess = false;
          editTask(taskAddExpense, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        isExpenseAPISuccess = false;
        editTask(taskAddExpense, 3);
      }
    } on DioException catch (e) {
      isExpenseAPISuccess = false;
      editTask(taskAddExpense, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskAddExpense,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskAddExpense, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskAddOrder,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    }
  }

  Future<void> syncLeavesWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedLeaves = await db.query(
        '${TableValues.tableLeaveRequest}',
        where: '${TableValues.leaveSyncStatus} = ?',
        whereArgs: [0]);

    TotalTask task =
        TotalTask(taskName: taskAddLeave, taskStatus: RxInt(1), taskType: 2);
    addTask(task);
    progress.value += 1;

    if (unsyncedLeaves.isNotEmpty) {
      print('Syncing Leaves with the server...');
      print("unsyncedLeaves: ${unsyncedLeaves}");

      for (final leave in unsyncedLeaves) {
        try {
          await addLeaveAPI(leave);
          await db.update('${TableValues.tableLeaveRequest}',
              {'${TableValues.leaveSyncStatus}': 1},
              where: '${TableValues.leaveCode} = ?',
              whereArgs: [leave['${TableValues.leaveCode}']]);
          print(
              'Leave with ID ${leave['${TableValues.leaveCode}']} synced successfully');
          editTask(taskAddLeave, 2);
          await ApiLogger.logError(
            apiName: 'addLeaveToDB',
            apiRequestData: leave,
            apiResponse:
                'Leave with ID ${leave['${TableValues.leaveCode}']} synced successfully',
          );
        } catch (error) {
          print(
              'Error syncing leave with ID ${leave['${TableValues.leaveCode}']}: $error');
          editTask(taskAddLeave, 3);
          await ApiLogger.logError(
            apiName: 'addLeaveToDB',
            apiRequestData: leave,
            apiResponse:
                'Error syncing leave with ID ${leave['${TableValues.leaveCode}']}: $error',
          );
        }
      }
      print('All Leaves synced successfully');
    } else {
      print('No Leaves to sync.');
      editTask(taskAddLeave, 2);
    }
  }

  Future<void> addLeaveAPI(Map<String, dynamic> leave) async {
    try {
      print(progress.value);
      print(tasks.length.toDouble());
      ApiClient.dioClient.options.headers['Authorization'] =
          'Bearer ${token.value}';

      // Define the API endpoint
      String apiUrl = ApiClient.getUrl(ApiClient.postAddLeaveEndpoint);
      print("apiUrl: ${apiUrl}");

      final response = await ApiClient.dioClient.post(apiUrl, data: leave);

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            isLeaveAPISuccess = true;
            editTask(taskAddLeave, 2);
          }
        } else {
          print('Response data is null');
          isLeaveAPISuccess = false;
          editTask(taskAddLeave, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        isLeaveAPISuccess = false;
        editTask(taskAddLeave, 3);
      }
    } on DioException catch (e) {
      isLeaveAPISuccess = false;
      editTask(taskAddLeave, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskAddLeave,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskAddLeave, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskAddLeave,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    }
  }

  Future<void> syncComplainsWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedComplains = await db.query(
        '${TableValues.tableClaimComplain}',
        where: '${TableValues.ccSyncStatus} = ?',
        whereArgs: [0]);

    TotalTask task =
        TotalTask(taskName: taskAddComplain, taskStatus: RxInt(1), taskType: 2);
    addTask(task);
    progress.value += 1;

    if (unsyncedComplains.isNotEmpty) {
      print('Syncing complains with the server...');
      print("unsyncedComplains: ${unsyncedComplains}");

      for (final complain in unsyncedComplains) {
        try {
          await addComplainAPI(complain);

          if (isComplainAPISuccess == true) {
            await db.update('${TableValues.tableClaimComplain}',
                {'${TableValues.ccSyncStatus}': 1},
                where: '${TableValues.ccCode} = ?',
                whereArgs: [complain['${TableValues.ccCode}']]);
            print(
                'Complain with ID ${complain['${TableValues.ccCode}']} synced successfully');
            editTask(taskAddComplain, 2);
            await ApiLogger.logError(
              apiName: 'addComplainToDB',
              apiRequestData: complain,
              apiResponse:
                  'Complain with ID ${complain['${TableValues.ccCode}']} synced successfully',
            );
          } else {
            print(
                'Error syncing Complain with ID ${complain['${TableValues.ccCode}']}');
            editTask(taskAddComplain, 3);
            await ApiLogger.logError(
              apiName: 'addComplainToDB',
              apiRequestData: complain,
              apiResponse:
                  'Error syncing Complain with ID ${complain['${TableValues.ccCode}']}',
            );
          }
        } catch (error) {
          print(
              'Error syncing Complain with ID ${complain['${TableValues.ccCode}']}: $error');
          editTask(taskAddComplain, 3);
          await ApiLogger.logError(
            apiName: 'addComplainToDB',
            apiRequestData: complain,
            apiResponse:
                'Error syncing Complain with ID ${complain['${TableValues.ccCode}']}: $error',
          );
        }
      }
    } else {
      print('No complains to sync.');
      editTask(taskAddComplain, 2);
    }
  }

  Future<void> addComplainAPI(Map<String, dynamic> complain) async {
    try {
      Map<String, dynamic> data = {};

      ApiClient.dioClient.options.headers['Authorization'] =
          'Bearer ${token.value}';

      String apiUrl = ApiClient.getUrl(ApiClient.postAddComplainEndpoint);
      print("apiUrl: ${apiUrl}");

      data = {
        "ccm_code": "${complain['ccm_code']}",
        "ccm_type_id": "${complain['ccm_type_id']}",
        "ccm_note": "${complain['ccm_note']}",
        "cm_code": "${complain['cm_code']}",
        "emp_code": "${complain['emp_code']}",
        "ccm_cc_type": "${complain['ccm_cc_type']}",
        "ccm_image": "${complain['ccm_image']}",
      };

      print("data: ${data}");

      final response = await ApiClient.dioClient.post(apiUrl, data: data);

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            isComplainAPISuccess = true;
            editTask(taskAddComplain, 2);
          }
        } else {
          print('Response data is null');
          isComplainAPISuccess = false;
          editTask(taskAddComplain, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        isComplainAPISuccess = false;
        editTask(taskAddComplain, 3);
      }
    } on DioException catch (e) {
      isComplainAPISuccess = false;
      editTask(taskAddComplain, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskAddLeave,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskAddComplain, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskAddComplain,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    }
  }

  Future<void> syncPaymentsWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedPayments = await db.query(
        '${TableValues.tablePaymentCollection}',
        where: '${TableValues.pcSyncStatus} = ?',
        whereArgs: [0]);

    TotalTask task =
        TotalTask(taskName: taskAddPayment, taskStatus: RxInt(1), taskType: 2);
    addTask(task);
    progress.value += 1;

    if (unsyncedPayments.isNotEmpty) {
      print('Syncing payments with the server...');
      print("unsyncedPayments: ${unsyncedPayments}");

      for (final payment in unsyncedPayments) {
        try {
          await addPaymentAPI(payment);

          if (isPaymentAPISuccess == true) {
            await db.update('${TableValues.tablePaymentCollection}',
                {'${TableValues.pcSyncStatus}': 1},
                where: '${TableValues.pcCode} = ?',
                whereArgs: [payment['${TableValues.pcCode}']]);

            print(
                'Payment with ID ${payment['${TableValues.pcCode}']} synced successfully');
            editTask(taskAddPayment, 2);
            await ApiLogger.logError(
              apiName: 'addPaymentToDB',
              apiRequestData: payment,
              apiResponse:
                  'Payment with ID ${payment['${TableValues.pcCode}']} synced successfully',
            );
          } else {
            print(
                'Error syncing payment with ID ${payment['${TableValues.pcCode}']}');
            editTask(taskAddPayment, 3);
            await ApiLogger.logError(
              apiName: 'addComplainToDB',
              apiRequestData: payment,
              apiResponse:
                  'Error syncing payment with ID ${payment['${TableValues.pcCode}']}',
            );
          }
        } catch (error) {
          print(
              'Error syncing Payment with ID ${payment['${TableValues.pcCode}']}: $error');
          editTask(taskAddPayment, 3);
          await ApiLogger.logError(
            apiName: 'addComplainToDB',
            apiRequestData: payment,
            apiResponse:
                'Error syncing Payment with ID ${payment['${TableValues.pcCode}']}: $error',
          );
        }
      }
    } else {
      print('No payments to sync.');
      editTask(taskAddPayment, 2);
    }
  }

  Future<void> addPaymentAPI(Map<String, dynamic> payment) async {
    try {
      Map<String, dynamic> data = {};

      Dio dio = Dio();
      dio.options.headers['Authorization'] = 'Bearer ${token.value}';

      if (payment['pc_type'] == 1) {
        data = {
          'pc_code': "${payment['pc_code']}",
          'cm_code': payment['pc_cm_code'],
          'pc_type': payment['pc_type'],
          'pc_amount': "${payment['pc_amount']}",
          'pc_note': "${payment['pc_note']}",
          'emp_code': payment['pc_emp_code'],
          'pc_condition_id': payment['pc_condition_id'],
        };
      } else if (payment['pc_type'] == 2) {
        data = {
          'pc_code': "${payment['pc_code']}",
          'cm_code': "${payment['pc_cm_code']}",
          'pc_type': "${payment['pc_type']}",
          'pc_amount': "${payment['pc_amount']}",
          'pc_note': "${payment['pc_note']}",
          'emp_code': "${payment['pc_emp_code']}",
          'pc_condition_id': "${payment['pc_condition_id']}",
          'pc_bank_id': "${payment['pc_bank_id']}",
          'pc_cheque_date': "${payment['pc_cheque_date']}",
          'pc_cheque_no': "${payment['pc_cheque_no']}",
          'pc_account_no': "${payment['pc_account_no']}",
          'pc_cheque_photo': "${payment['pc_cheque_photo']}",
          'pc_status': "${payment['pc_status']}",
          'created_at': "${payment['created_at']}",
          'updated_at': "${payment['updated_at']}",
        };
      } else if (payment['pc_type'] == 3) {
        data = {
          'pc_code': "${payment['pc_code']}",
          'cm_code': "${payment['pc_cm_code']}",
          'pc_type': "${payment['pc_type']}",
          'pc_amount': "${payment['pc_amount']}",
          'pc_note': "${payment['pc_note']}",
          'emp_code': "${payment['pc_emp_code']}",
          'pc_condition_id': "${payment['pc_condition_id']}",
          'pc_bank_id': "${payment['pc_bank_id']}",
          'pc_cheque_date': "${payment['pc_cheque_date']}",
          'pc_cheque_no': "${payment['pc_cheque_no']}",
          'pc_account_no': "${payment['pc_account_no']}",
          'pc_cheque_photo': "${payment['pc_cheque_photo']}",
          'pc_status': "${payment['pc_status']}",
          'created_at': "${payment['created_at']}",
          'updated_at': "${payment['updated_at']}",
        };
      }

      print("data: ${data}");

      final response = await ApiClient.dioClient.post(
        ApiClient.getUrl(ApiClient.postAddPaymentEndpoint),
        data: data,
      );

      if (response.statusCode == 200) {
        print('Request successful');
        print('Response data: ${response.data}');
        isPaymentAPISuccess = true;
        editTask(taskAddPayment, 2);
      } else {
        print('Unexpected status code: ${response.statusCode}');
        isPaymentAPISuccess = false;
        editTask(taskAddPayment, 3);
      }
      print('Response status: ${response.statusCode}');
      print('Response data: ${response.data}');
    } on DioException catch (e) {
      isPaymentAPISuccess = false;
      editTask(taskAddPayment, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskAddPayment,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskAddPayment, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskAddPayment,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    }
  }

  Future<void> getTerritory() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskTerritory, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getTerritoryEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskTerritory, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> states =
                List<Map<String, dynamic>>.from(response.data['state']);
            List<Map<String, dynamic>> filteredStates = states.map((pc) {
              int stateId = 0;
              String stateName = "";
              String stateZone = "";
              int stateGSTCode = 0;

              if (pc['state_id'] != null) {
                try {
                  stateId = convertValue(pc['state_id'], int);
                } catch (e) {
                  print("Error converting stateId: $e");
                }
              }

              if (pc['state_name'] != null) {
                try {
                  stateName = convertValue(pc['state_name'], String);
                } catch (e) {
                  print("Error converting stateName: $e");
                }
              }

              if (pc['state_zone'] != null) {
                try {
                  stateZone = convertValue(pc['state_zone'], String);
                } catch (e) {
                  print("Error converting state_zone: $e");
                }
              }

              if (pc['state_gst_code'] != null) {
                try {
                  stateGSTCode = convertValue(pc['state_gst_code'], int);
                } catch (e) {
                  print("Error converting state_gst_code: $e");
                }
              }

              return {
                'state_id': stateId,
                'state_name': stateName,
                'state_zone': stateZone,
                'state_gst_code': stateGSTCode,
              };
            }).toList();
            await insertValues(
                toTable: "${TableValues.tableState}", withData: filteredStates);

            final List<Map<String, dynamic>> headquarters =
                List<Map<String, dynamic>>.from(response.data['hq']);
            List<Map<String, dynamic>> filteredHQ = headquarters.map((pc) {
              int hqId = 0;
              String hqName = "";

              if (pc['hq_id'] != null) {
                try {
                  hqId = convertValue(pc['hq_id'], int);
                } catch (e) {
                  print("Error converting hqId: $e");
                }
              }

              if (pc['hq_name'] != null) {
                try {
                  hqName = convertValue(pc['hq_name'], String);
                } catch (e) {
                  print("Error converting hq_name: $e");
                }
              }

              return {
                'hq_id': hqId,
                'hq_name': hqName,
              };
            }).toList();
            await insertValues(
                toTable: "${TableValues.tableHeadQuarter}",
                withData: filteredHQ);

            final List<Map<String, dynamic>> districts =
                List<Map<String, dynamic>>.from(response.data['district_town']);

            await insertDistrictsAndTowns(districts);

            editTask(taskTerritory, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskTerritory, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskTerritory, 3);
      }
    } on DioException catch (e) {
      editTask(taskTerritory, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskTerritory,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskTerritory, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskTerritory,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getProductBrand() async {
    try {
      TotalTask task = TotalTask(
          taskName: taskProductBrand, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };

      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getProductBrandEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskProductBrand, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> productBrands =
                List<Map<String, dynamic>>.from(response.data['product_brand']);

            List<Map<String, dynamic>> filteredBrands = productBrands.map((pc) {
              int brandId = 0;
              String brandName = "";

              if (pc['brand_id'] != null) {
                try {
                  brandId = convertValue(pc['brand_id'], int);
                } catch (e) {
                  print("Error converting brand_id: $e");
                }
              }

              if (pc['brand_name'] != null) {
                try {
                  brandName = convertValue(pc['brand_name'], String);
                } catch (e) {
                  print("Error converting brand_name: $e");
                }
              }

              return {
                'brand_id': brandId,
                'brand_name': brandName,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableProductBrand}",
                withData: filteredBrands);

            List<String> apiDataCodes =
                filteredBrands.map((data) => "${data["brand_id"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableProductBrand}",
                forCode: "${TableValues.brandID}",
                withData: apiDataCodes);

            editTask(taskProductBrand, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskProductBrand, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskProductBrand, 3);
      }
    } on DioException catch (e) {
      editTask(taskProductBrand, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskProductBrand,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskProductBrand, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskProductBrand,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getProductType() async {
    try {
      TotalTask task = TotalTask(
          taskName: taskProductType, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getProductTypeEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskProductType, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> productTypes =
                List<Map<String, dynamic>>.from(response.data['product_type']);

            // Create a new list containing only the desired fields
            List<Map<String, dynamic>> filteredProductTypes =
                productTypes.map((productType) {
              int ptId = 0;
              String ptName = "";
              int brandId = 0;

              if (productType['pt_id'] != null) {
                try {
                  ptId = convertValue(productType['pt_id'], int);
                } catch (e) {
                  print("Error converting pt_id: $e");
                }
              }

              if (productType['pt_name'] != null) {
                try {
                  ptName = convertValue(productType['pt_name'], String);
                } catch (e) {
                  print("Error converting pt_name: $e");
                }
              }

              if (productType['brand_id'] != null) {
                try {
                  brandId = convertValue(productType['brand_id'], int);
                } catch (e) {
                  print("Error converting brand_id: $e");
                }
              }

              return {
                'pt_id': ptId,
                'pt_name': ptName,
                'brand_id': brandId,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableProductType}",
                withData: filteredProductTypes);

            List<String> apiDataCodes =
                filteredProductTypes.map((data) => "${data["pt_id"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableProductType}",
                forCode: "${TableValues.productTypeID}",
                withData: apiDataCodes);

            editTask(taskProductType, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskProductType, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskProductType, 3);
      }
    } on DioException catch (e) {
      editTask(taskProductType, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskProductType,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskProductType, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskProductType,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getUOM() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskUOM, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getUOMEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskUOM, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> uoms =
                List<Map<String, dynamic>>.from(response.data['uom']);

            List<Map<String, dynamic>> filteredUOM = uoms.map((pc) {
              int uomId = 0;
              String uomName = "";

              if (pc['uom_id'] != null) {
                try {
                  uomId = convertValue(pc['uom_id'], int);
                } catch (e) {
                  print("Error converting uom_id: $e");
                }
              }

              if (pc['uom_name'] != null) {
                try {
                  uomName = convertValue(pc['uom_name'], String);
                } catch (e) {
                  print("Error converting uom_name: $e");
                }
              }

              return {
                'uom_id': uomId,
                'uom_name': uomName,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableUOM}", withData: filteredUOM);

            List<String> apiDataCodes =
                filteredUOM.map((data) => "${data["uom_id"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableUOM}",
                forCode: "${TableValues.uomID}",
                withData: apiDataCodes);

            editTask(taskUOM, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskUOM, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskUOM, 3);
      }
    } on DioException catch (e) {
      editTask(taskUOM, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskUOM,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskUOM, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskUOM,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getProduct() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskProduct, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getProductEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskProduct, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> products =
                List<Map<String, dynamic>>.from(response.data['product']);

            List<Map<String, dynamic>> filteredProducts = products.map((pc) {
              String productCode = "";
              int pmPtId = 0;
              int productBrand = 0;
              String productName = "";
              int uom = 0;
              int unitSize = 0;
              int innerCase = 0;
              int outerCase = 0;
              String productMrp = "";
              String productGst = "";
              String productHsn = "";
              String productImage = "";
              int productStatus = 0;
              String productBarcode = "";
              int productMarketType = 0;
              String productPtss = "";
              String productPtdist = "";
              String productPtdealer = "";
              String productPtretailer = "";

              if (pc['product_code'] != null) {
                try {
                  productCode = convertValue(pc['product_code'], String);
                } catch (e) {
                  print("Error converting product_code: $e");
                }
              }

              if (pc['pm_pt_id'] != null) {
                try {
                  pmPtId = convertValue(pc['pm_pt_id'], String);
                } catch (e) {
                  print("Error converting pm_pt_id: $e");
                }
              }

              if (pc['product_brand'] != null) {
                try {
                  productBrand = convertValue(pc['product_brand'], int);
                } catch (e) {
                  print("Error converting product_brand: $e");
                }
              }

              if (pc['product_name'] != null) {
                try {
                  productName = convertValue(pc['product_name'], String);
                } catch (e) {
                  print("Error converting product_name: $e");
                }
              }

              if (pc['uom'] != null) {
                try {
                  uom = convertValue(pc['uom'], int);
                } catch (e) {
                  print("Error converting uom: $e");
                }
              }

              if (pc['unit_size'] != null) {
                try {
                  unitSize = convertValue(pc['unit_size'], int);
                } catch (e) {
                  print("Error converting unit_size: $e");
                }
              }

              if (pc['inner_case_size'] != null) {
                try {
                  innerCase = convertValue(pc['inner_case_size'], int);
                } catch (e) {
                  print("Error converting inner_case_size: $e");
                }
              }

              if (pc['outer_case_size'] != null) {
                try {
                  outerCase = convertValue(pc['outer_case_size'], int);
                } catch (e) {
                  print("Error converting outer_case_size: $e");
                }
              }

              if (pc['product_mrp'] != null) {
                try {
                  productMrp = convertValue(pc['product_mrp'], String);
                } catch (e) {
                  print("Error converting product_mrp: $e");
                }
              }

              if (pc['product_gst'] != null) {
                try {
                  productGst = convertValue(pc['product_gst'], String);
                } catch (e) {
                  print("Error converting product_gst: $e");
                }
              }

              if (pc['product_hsn_sac_code'] != null) {
                try {
                  productHsn = convertValue(pc['product_hsn_sac_code'], String);
                } catch (e) {
                  print("Error converting product_hsn_sac_code: $e");
                }
              }

              if (pc['product_image'] != null) {
                try {
                  productImage = convertValue(pc['product_image'], String);
                } catch (e) {
                  print("Error converting product_image: $e");
                }
              }

              if (pc['product_status'] != null) {
                try {
                  productStatus = convertValue(pc['product_status'], int);
                } catch (e) {
                  print("Error converting product_status: $e");
                }
              }

              if (pc['barcode'] != null) {
                try {
                  productBarcode = convertValue(pc['barcode'], String);
                } catch (e) {
                  print("Error converting barcode: $e");
                }
              }

              if (pc['market_type'] != null) {
                try {
                  productMarketType = convertValue(pc['market_type'], int);
                } catch (e) {
                  print("Error converting market_type: $e");
                }
              }

              if (pc['product_ptss'] != null) {
                try {
                  productPtss = convertValue(pc['product_ptss'], String);
                } catch (e) {
                  print("Error converting product_ptss: $e");
                }
              }

              if (pc['product_ptdistributor'] != null) {
                try {
                  productPtdist =
                      convertValue(pc['product_ptdistributor'], String);
                } catch (e) {
                  print("Error converting product_ptdistributor: $e");
                }
              }

              if (pc['product_ptdealer'] != null) {
                try {
                  productPtdealer =
                      convertValue(pc['product_ptdealer'], String);
                } catch (e) {
                  print("Error converting product_ptdealer: $e");
                }
              }

              if (pc['product_ptretailer'] != null) {
                try {
                  productPtretailer =
                      convertValue(pc['product_ptretailer'], String);
                } catch (e) {
                  print("Error converting product_ptretailer: $e");
                }
              }

              return {
                'product_code': productCode,
                'pm_pt_id': pmPtId,
                'product_brand': productBrand,
                'product_name': productName,
                'uom': uom,
                'unit_size': unitSize,
                'inner_case_size': innerCase,
                'outer_case_size': outerCase,
                'product_mrp': productMrp,
                'product_gst': productGst,
                'product_hsn_sac_code': productHsn,
                'product_image': productImage,
                'product_status': productStatus,
                'barcode': productBarcode,
                'market_type': productMarketType,
                'product_ptss': productPtss,
                'product_ptdistributor': productPtdist,
                'product_ptdealer': productPtdealer,
                'product_ptretailer': productPtretailer,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableProduct}",
                withData: filteredProducts);

            List<String> apiDataCodes = filteredProducts
                .map((data) => "${data["product_code"]}")
                .toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableProduct}",
                forCode: "${TableValues.productCode}",
                withData: apiDataCodes);

            editTask(taskProduct, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskProduct, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskProduct, 3);
      }
    } on DioException catch (e) {
      editTask(taskProduct, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskProduct,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskProduct, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskProduct,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getMarketType() async {
    try {
      TotalTask task = TotalTask(
          taskName: taskMarketType, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getMarketTypeEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskMarketType, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> marketTypes =
                List<Map<String, dynamic>>.from(response.data['market_type']);

            List<Map<String, dynamic>> filteredMT = marketTypes.map((pc) {
              int mtmId = 0;
              String mtmName = "";

              if (pc['mtm_id'] != null) {
                try {
                  mtmId = convertValue(pc['mtm_id'], int);
                } catch (e) {
                  print("Error converting mtm_id: $e");
                }
              }

              if (pc['mtm_type'] != null) {
                try {
                  mtmName = convertValue(pc['mtm_type'], String);
                } catch (e) {
                  print("Error converting mtm_type: $e");
                }
              }

              return {
                'mtm_id': mtmId,
                'mtm_type': mtmName,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableMarketType}",
                withData: filteredMT);

            List<String> apiDataCodes =
                filteredMT.map((data) => "${data["mtm_id"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableMarketType}",
                forCode: "${TableValues.marketID}",
                withData: apiDataCodes);

            editTask(taskMarketType, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskMarketType, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskMarketType, 3);
      }
    } on DioException catch (e) {
      editTask(taskMarketType, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskProduct,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskMarketType, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskMarketType,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getPriceGroup() async {
    try {
      TotalTask task = TotalTask(
          taskName: taskPriceGroup, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getPriceGroupEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskPriceGroup, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> priceGroups =
                List<Map<String, dynamic>>.from(response.data['price_group']);

            List<Map<String, dynamic>> filteredPriceGroups =
                priceGroups.map((leave) {
              int id = 0;
              String productCode = '';
              int marketType = 0;
              String productPtss = '';
              String productPtDistributor = '';
              String productPtDealer = '';
              String productPtRetailer = '';

              if (leave['id'] != null) {
                try {
                  id = convertValue(leave['id'], int);
                } catch (e) {
                  print("Error converting id: $e");
                }
              }

              if (leave['product_code'] != null) {
                try {
                  productCode = convertValue(leave['product_code'], String);
                } catch (e) {
                  print("Error converting product_code: $e");
                }
              }

              if (leave['market_type'] != null) {
                try {
                  marketType = convertValue(leave['market_type'], int);
                } catch (e) {
                  print("Error converting market_type: $e");
                }
              }

              if (leave['product_ptss'] != null) {
                try {
                  productPtss = convertValue(leave['product_ptss'], String);
                } catch (e) {
                  print("Error converting product_ptss: $e");
                }
              }

              if (leave['product_ptdistributor'] != null) {
                try {
                  productPtDistributor =
                      convertValue(leave['product_ptdistributor'], String);
                } catch (e) {
                  print("Error converting product_ptdistributor: $e");
                }
              }

              if (leave['product_ptdealer'] != null) {
                try {
                  productPtDealer =
                      convertValue(leave['product_ptdealer'], String);
                } catch (e) {
                  print("Error converting product_ptdealer: $e");
                }
              }

              if (leave['product_ptretailer'] != null) {
                try {
                  productPtRetailer =
                      convertValue(leave['product_ptretailer'], String);
                } catch (e) {
                  print("Error converting product_ptretailer: $e");
                }
              }

              return {
                'id': id,
                'product_code': productCode,
                'market_type': marketType,
                'product_ptss': productPtss,
                'product_ptdistributor': productPtDistributor,
                'product_ptdealer': productPtDealer,
                'product_ptretailer': productPtRetailer,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tablePriceGroup}",
                withData: filteredPriceGroups);

            editTask(taskPriceGroup, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskPriceGroup, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskPriceGroup, 3);
      }
    } on DioException catch (e) {
      editTask(taskPriceGroup, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskPriceGroup,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskPriceGroup, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskPriceGroup,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getCustomerCategory() async {
    try {
      TotalTask task = TotalTask(
          taskName: taskCustomerCategory, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getCustomerCategoryEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskCustomerCategory, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> customerCategories =
                List<Map<String, dynamic>>.from(response.data['category']);

            List<Map<String, dynamic>> filteredCategory =
                customerCategories.map((pc) {
              int ccmId = 0;
              String ccmName = "";

              if (pc['ccm_id'] != null) {
                try {
                  ccmId = convertValue(pc['ccm_id'], int);
                } catch (e) {
                  print("Error converting ccm_id: $e");
                }
              }

              if (pc['ccm_name'] != null) {
                try {
                  ccmName = convertValue(pc['ccm_name'], String);
                } catch (e) {
                  print("Error converting ccm_name: $e");
                }
              }

              return {
                'ccm_id': ccmId,
                'ccm_name': ccmName,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableCustomerCategory}",
                withData: filteredCategory);

            List<String> apiDataCodes =
                filteredCategory.map((data) => "${data["ccm_id"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableCustomerCategory}",
                forCode: "${TableValues.ccmID}",
                withData: apiDataCodes);

            editTask(taskCustomerCategory, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskCustomerCategory, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskCustomerCategory, 3);
      }
    } on DioException catch (e) {
      editTask(taskCustomerCategory, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskCustomerCategory,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskCustomerCategory, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskCustomerCategory,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getGrade() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskGrade, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getGradeEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskGrade, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> grades =
                List<Map<String, dynamic>>.from(response.data['grade']);

            List<Map<String, dynamic>> filteredGrades = grades.map((pc) {
              int gmId = 0;
              String gmName = "";

              if (pc['gm_id'] != null) {
                try {
                  gmId = convertValue(pc['gm_id'], int);
                } catch (e) {
                  print("Error converting gm_id: $e");
                }
              }

              if (pc['gm_name'] != null) {
                try {
                  gmName = convertValue(pc['gm_name'], String);
                } catch (e) {
                  print("Error converting gm_name: $e");
                }
              }

              return {
                'gm_id': gmId,
                'gm_name': gmName,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableGrade}", withData: filteredGrades);

            List<String> apiDataCodes =
                filteredGrades.map((data) => "${data["gm_id"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableGrade}",
                forCode: "${TableValues.gmID}",
                withData: apiDataCodes);

            editTask(taskGrade, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskGrade, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskGrade, 3);
      }
    } on DioException catch (e) {
      editTask(taskGrade, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskGrade,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskGrade, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskGrade,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getBeats() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskBeat, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getBeatRouteEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskBeat, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> beatRoute =
                List<Map<String, dynamic>>.from(response.data['beat_route']);
            print(beatRoute);

            // Create a new list containing only the desired fields
            List<Map<String, dynamic>> filteredBeats = beatRoute.map((beat) {
              String beatCode = '';
              String beatName = '';
              int beatStatus = 0;
              String bdmDate = '';
              int bdmNumberwise = 0;
              int bdmType = 0;
              String bdmDay = '';
              int bdmPeriod = 0;
              int beatSyncStatus = 1; 

              // Process values
              if (beat['beat'] != null && beat['beat']['beat_code'] != null) {
                try {
                  beatCode = convertValue(beat['beat']['beat_code'], String);
                } catch (e) {
                  print("Error converting beat_code: $e");
                }
              }

              if (beat['beat'] != null && beat['beat']['beat_name'] != null) {
                try {
                  beatName = convertValue(beat['beat']['beat_name'], String);
                } catch (e) {
                  print("Error converting beat_name: $e");
                }
              }

              if (beat['beat'] != null && beat['beat']['beat_status'] != null) {
                try {
                  beatStatus = convertValue(beat['beat']['beat_status'], int);
                } catch (e) {
                  print("Error converting beat_status: $e");
                }
              }

              if (beat['bdm_date'] != null) {
                try {
                  bdmDate = convertValue(beat['bdm_date'], String);
                } catch (e) {
                  print("Error converting bdm_date: $e");
                }
              }

              if (beat['bdm_numberwise'] != null) {
                try {
                  bdmNumberwise = convertValue(beat['bdm_numberwise'], int);
                } catch (e) {
                  print("Error converting bdm_numberwise: $e");
                }
              }

              if (beat['bdm_type'] != null) {
                try {
                  bdmType = convertValue(beat['bdm_type'], int);
                } catch (e) {
                  print("Error converting bdm_type: $e");
                }
              }

              if (beat['bdm_day'] != null) {
                try {
                  bdmDay = convertValue(beat['bdm_day'], String);
                } catch (e) {
                  print("Error converting bdm_day: $e");
                }
              }

              if (beat['bdm_period'] != null) {
                try {
                  bdmPeriod = convertValue(beat['bdm_period'], int);
                } catch (e) {
                  print("Error converting bdm_period: $e");
                }
              }

              return {
                'beat_code': beatCode,
                'beat_name': beatName,
                'beat_status': beatStatus,
                'bdm_date': bdmDate,
                'bdm_numberwise': bdmNumberwise,
                'bdm_type': bdmType,
                'bdm_day': bdmDay,
                'bdm_period': bdmPeriod,
                'beat_sync_status': beatSyncStatus,
              };
            }).toList();

            print("filteredBeats: ${filteredBeats}");

            await insertValues(
                toTable: "${TableValues.tableBeats}", withData: filteredBeats);

            List<String> apiBeatCodes =
                filteredBeats.map((beat) => "${beat["beat_code"]}").toList();
            print("apiBeatCodes: ${apiBeatCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableBeats}",
                forCode: "${TableValues.beatCode}",
                withData: apiBeatCodes);

            editTask(taskBeat, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskBeat, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskBeat, 3);
      }
    } on DioException catch (e) {
      editTask(taskBeat, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskBeat,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskBeat, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskBeat,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getCustomer() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskCustomer, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getCustomerEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 90), onTimeout: () {
        print('API call timed out');
        editTask(taskCustomer, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> customers =
                List<Map<String, dynamic>>.from(response.data['customer']);

            List<Map<String, dynamic>> filteredCustomers = customers.map(
              (customer) {
                String cmCode = '';
                String cmName = '';
                int cmMobile = 0;
                int cmMobile2 = 0;
                String cmEmail = '';
                String cmAddress = '';
                int cmPincode = 0;
                String cmGst = '';
                String cmPan = '';
                double cmLat = 0.0;
                double cmLong = 0.0;
                int cmMarketType = 0;
                int cmStatus = 0;
                int cmTownId = 0;
                double cmOutstandingAmount = 0.0;
                String cmContactPerson = '';
                String cmArea = '';
                String cmRelationCode = '';
                int cmType = 0;
                String cmCreatedAt = '';
                String anniversaryDate = '';
                String birthDate = '';
                String strShopImages = '';
                String beatCode = '';
                int categoryID = 0;
                int gradeID = 0;
                int syncStatus = 1;

                // Process values
                if (customer['cm_code'] != null) {
                  try {
                    cmCode = convertValue(customer['cm_code'], String);
                  } catch (e) {
                    print("Error converting cm_code: $e");
                  }
                }

                if (customer['cm_name'] != null) {
                  try {
                    cmName = convertValue(customer['cm_name'], String);
                  } catch (e) {
                    print("Error converting cm_name: $e");
                  }
                }

                if (customer['cm_mobile'] != null) {
                  try {
                    cmMobile = convertValue(customer['cm_mobile'], int);
                  } catch (e) {
                    print("Error converting cm_mobile: $e");
                  }
                }

                if (customer['cm_mobile2'] != null) {
                  try {
                    cmMobile2 = convertValue(customer['cm_mobile2'], int);
                  } catch (e) {
                    print("Error converting cm_mobile2: $e");
                  }
                }

                if (customer['cm_email'] != null) {
                  try {
                    cmEmail = convertValue(customer['cm_email'], String);
                  } catch (e) {
                    print("Error converting cm_email: $e");
                  }
                }

                if (customer['cm_address'] != null) {
                  try {
                    cmAddress = convertValue(customer['cm_address'], String);
                  } catch (e) {
                    print("Error converting cm_address: $e");
                  }
                }

                if (customer['cm_pincode'] != null) {
                  try {
                    cmPincode = convertValue(customer['cm_pincode'], int);
                  } catch (e) {
                    print("Error converting cm_pincode: $e");
                  }
                }

                if (customer['cm_gst'] != null) {
                  try {
                    cmGst = convertValue(customer['cm_gst'], String);
                  } catch (e) {
                    print("Error converting cm_gst: $e");
                  }
                }

                if (customer['cm_pan'] != null) {
                  try {
                    cmPan = convertValue(customer['cm_pan'], String);
                  } catch (e) {
                    print("Error converting cm_pan: $e");
                  }
                }

                if (customer['cm_lat'] != null) {
                  try {
                    cmLat = convertValue(customer['cm_lat'], double);
                  } catch (e) {
                    print("Error converting cm_lat: $e");
                  }
                }

                if (customer['cm_long'] != null) {
                  try {
                    cmLong = convertValue(customer['cm_long'], double);
                  } catch (e) {
                    print("Error converting cm_long: $e");
                  }
                }

                if (customer['cm_market_type'] != null) {
                  try {
                    cmMarketType =
                        convertValue(customer['cm_market_type'], int);
                  } catch (e) {
                    print("Error converting cm_market_type: $e");
                  }
                }

                if (customer['cm_status'] != null) {
                  try {
                    cmStatus = convertValue(customer['cm_status'], int);
                  } catch (e) {
                    print("Error converting cm_status: $e");
                  }
                }

                if (customer['cm_town_id'] != null) {
                  try {
                    cmTownId = convertValue(customer['cm_town_id'], int);
                  } catch (e) {
                    print("Error converting cm_town_id: $e");
                  }
                }

                if (customer['cm_outstanding_amount'] != null) {
                  try {
                    cmOutstandingAmount =
                        convertValue(customer['cm_outstanding_amount'], double);
                  } catch (e) {
                    print("Error converting cm_outstanding_amount: $e");
                  }
                }

                if (customer['cm_contact_person'] != null) {
                  try {
                    cmContactPerson =
                        convertValue(customer['cm_contact_person'], String);
                  } catch (e) {
                    print("Error converting cm_contact_person: $e");
                  }
                }

                if (customer['cm_area'] != null) {
                  try {
                    cmArea = convertValue(customer['cm_area'], String);
                  } catch (e) {
                    print("Error converting cm_area: $e");
                  }
                }

                if (customer['cm_relation_code'] != null) {
                  try {
                    cmRelationCode =
                        convertValue(customer['cm_relation_code'], String);
                  } catch (e) {
                    print("Error converting cm_relation_code: $e");
                  }
                }

                if (customer['cm_type'] != null) {
                  try {
                    cmType = convertValue(customer['cm_type'], int);
                  } catch (e) {
                    print("Error converting cm_type: $e");
                  }
                }

                if (customer['cm_created_at'] != null) {
                  try {
                    String timestamp =
                        convertValue(customer['created_at'], String);
                    cmCreatedAt = convertTimestamp(timestamp);
                  } catch (e) {
                    print("Error converting cm_created_at: $e");
                  }
                }

                if (customer['cm_anniversary'] != null) {
                  try {
                    anniversaryDate =
                        convertValue(customer['cm_anniversary'], String);
                  } catch (e) {
                    print("Error converting cm_anniversary: $e");
                  }
                }

                if (customer['cm_dob'] != null) {
                  try {
                    birthDate = convertValue(customer['cm_dob'], String);
                  } catch (e) {
                    print("Error converting cm_dob: $e");
                  }
                }

                if (customer['market_type_master'] != null) {
                  try {
                    cmMarketType = convertValue(
                        customer['market_type_master']['mtm_id'], int);
                  } catch (e) {
                    print("Error converting market_type_master: $e");
                  }
                }

                if (customer['cm_beat_relation'] != null) {
                  try {
                    beatCode = convertValue(
                        customer['cm_beat_relation']['beat']['beat_code'],
                        String);
                  } catch (e) {
                    print("Error converting cm_beat_relation: $e");
                  }
                }

                if (customer['cm_category_relation'] != null) {
                  try {
                    categoryID = convertValue(
                        customer['cm_category_relation']['category']['ccm_id'],
                        int);
                  } catch (e) {
                    print("Error converting cm_category_relation: $e");
                  }
                }

                if (customer['cm_grade_relation'] != null) {
                  try {
                    gradeID = convertValue(
                        customer['cm_grade_relation']['grade']['gm_id'], int);
                  } catch (e) {
                    print("Error converting cm_grade_relation: $e");
                  }
                }

                List<dynamic> shopImageList = customer['cm_image_relation'];
                print("shopImageList: ${shopImageList}");

                if (customer['anni_birth_relation'] != null) {
                  anniversaryDate = convertValue(
                      customer['anni_birth_relation']['anni_date'], String);
                  birthDate = convertValue(
                      customer['anni_birth_relation']['birth_date'], String);
                }

                for (var imageMap in shopImageList) {
                  print("imageMap: ${imageMap}");
                  if (imageMap is Map<String, dynamic>) {
                    String? imageName = imageMap['image_name'];
                    print("imageName: ${imageName}");
                    if (imageName != null) {
                      strShopImages = imageName;
                      print("strShopImages1: ${strShopImages}");
                    }
                  }
                }

                return {
                  'cm_code': cmCode,
                  'cm_name': cmName,
                  'cm_mobile': cmMobile,
                  'cm_mobile2': cmMobile2,
                  'cm_email': cmEmail,
                  'cm_address': cmAddress,
                  'cm_pincode': cmPincode,
                  'cm_gst': cmGst,
                  'cm_pan': cmPan,
                  'cm_lat': cmLat,
                  'cm_long': cmLong,
                  'cm_market_type': cmMarketType,
                  'cm_status': cmStatus,
                  'cm_town_id': cmTownId,
                  'cm_outstanding_amount': cmOutstandingAmount,
                  'cm_contact_person': cmContactPerson,
                  'cm_area': cmArea,
                  'cm_relation_code': cmRelationCode,
                  'cm_type': cmType,
                  'cm_created_at': cmCreatedAt,
                  'cm_anniversary': anniversaryDate,
                  'cm_dob': birthDate,
                  'cm_image_relation': strShopImages,
                  'cm_beat_relation': beatCode,
                  'cm_category_relation': categoryID,
                  'cm_grade_relation': gradeID,
                  'syncStatus': syncStatus,
                };
              },
            ).toList();

            await insertValues(
                toTable: "${TableValues.tableCustomer}",
                withData: filteredCustomers);

            List<String> apiDataCodes =
                filteredCustomers.map((data) => "${data["cm_code"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableCustomer}",
                forCode: "${TableValues.customerCode}",
                withData: apiDataCodes);

            editTask(taskCustomer, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskCustomer, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskCustomer, 3);
      }
    } on DioException catch (e) {
      editTask(taskCustomer, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskCustomer,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskCustomer, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskCustomer,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      editTask(taskCustomer, 3);
      print('Unexpected error: $e');
    }
  }

  Future<void> getBank() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskBank, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getBankEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskBank, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> banks =
                List<Map<String, dynamic>>.from(response.data['bank']);

            List<Map<String, dynamic>> filteredBanks = banks.map((pc) {
              int bankId = 0;
              String bankName = "";

              if (pc['bank_id'] != null) {
                try {
                  bankId = convertValue(pc['bank_id'], int);
                } catch (e) {
                  print("Error converting bank_id: $e");
                }
              }

              if (pc['bank_name'] != null) {
                try {
                  bankName = convertValue(pc['bank_name'], String);
                } catch (e) {
                  print("Error converting bank_name: $e");
                }
              }

              return {
                'bank_id': bankId,
                'bank_name': bankName,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableBank}", withData: filteredBanks);

            List<String> apiDataCodes =
                filteredBanks.map((data) => "${data["bank_id"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableBank}",
                forCode: "${TableValues.bankID}",
                withData: apiDataCodes);

            editTask(taskBank, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskBank, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskBank, 3);
      }
    } on DioException catch (e) {
      editTask(taskBank, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskBank,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskBank, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskBank,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getClaimComplainType() async {
    try {
      TotalTask task = TotalTask(
          taskName: taskClaimComplainType, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getClaimComplainTypeEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskClaimComplainType, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> claimComplainTypes =
                List<Map<String, dynamic>>.from(
                    response.data['claim_complain_type']);

            List<Map<String, dynamic>> filteredCCT =
                claimComplainTypes.map((pc) {
              int cctId = 0;
              String cctName = "";
              int cctType = 0;

              if (pc['cct_id'] != null) {
                try {
                  cctId = convertValue(pc['cct_id'], int);
                } catch (e) {
                  print("Error converting cct_id: $e");
                }
              }

              if (pc['cct_type_name'] != null) {
                try {
                  cctName = convertValue(pc['cct_type_name'], String);
                } catch (e) {
                  print("Error converting cct_type_name: $e");
                }
              }

              if (pc['cct_type'] != null) {
                try {
                  cctType = convertValue(pc['cct_type'], int);
                } catch (e) {
                  print("Error converting cct_type: $e");
                }
              }

              return {
                'cct_id': cctId,
                'cct_type_name': cctName,
                'cct_type': cctType,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableClaimComplainType}",
                withData: filteredCCT);

            List<String> apiDataCodes =
                filteredCCT.map((data) => "${data["cct_id"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableClaimComplainType}",
                forCode: "${TableValues.claimComplainID}",
                withData: apiDataCodes);

            editTask(taskClaimComplainType, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskClaimComplainType, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskClaimComplainType, 3);
      }
    } on DioException catch (e) {
      editTask(taskClaimComplainType, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskClaimComplainType,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskClaimComplainType, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskClaimComplainType,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getClaimComplain() async {
    try {
      TotalTask task = TotalTask(
          taskName: taskClaimComplain, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getClaimComplainEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskClaimComplain, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> claimComplains =
                List<Map<String, dynamic>>.from(
                    response.data['claim_complain']);

            // Create a new list containing only the desired fields
            List<Map<String, dynamic>> filteredComplains =
                claimComplains.map((cc) {
              String ccmCode = '';
              int ccmTypeId = 0;
              String ccmNote = '';
              String ccmImage = '';
              String cmCode = '';
              String empCode = '';
              int ccmCcType = 0;
              int ccmStatus = 0;
              String ccmRemarks = '';
              String createdAt = '';
              String updatedAt = '';
              int ccmSyncStatus = 1;

             
              if (cc['ccm_code'] != null) {
                try {
                  ccmCode = convertValue(cc['ccm_code'], String);
                } catch (e) {
                  print("Error converting ccm_code: $e");
                }
              }

              if (cc['ccm_type_id'] != null) {
                try {
                  ccmTypeId = convertValue(cc['ccm_type_id'], int);
                } catch (e) {
                  print("Error converting ccm_type_id: $e");
                }
              }

              if (cc['ccm_note'] != null) {
                try {
                  ccmNote = convertValue(cc['ccm_note'], String);
                } catch (e) {
                  print("Error converting ccm_note: $e");
                }
              }

              if (cc['ccm_image'] != null) {
                try {
                  ccmImage = convertValue(cc['ccm_image'], String);
                } catch (e) {
                  print("Error converting ccm_image: $e");
                }
              }

              if (cc['cm_code'] != null) {
                try {
                  cmCode = convertValue(cc['cm_code'], String);
                } catch (e) {
                  print("Error converting cm_code: $e");
                }
              }

              if (cc['emp_code'] != null) {
                try {
                  empCode = convertValue(cc['emp_code'], String);
                } catch (e) {
                  print("Error converting emp_code: $e");
                }
              }

              if (cc['ccm_cc_type'] != null) {
                try {
                  ccmCcType = convertValue(cc['ccm_cc_type'], int);
                } catch (e) {
                  print("Error converting ccm_cc_type: $e");
                }
              }

              if (cc['ccm_status'] != null) {
                try {
                  ccmStatus = convertValue(cc['ccm_status'], int);
                } catch (e) {
                  print("Error converting ccm_status: $e");
                }
              }

              if (cc['ccm_remarks'] != null) {
                try {
                  ccmRemarks = convertValue(cc['ccm_remarks'], String);
                } catch (e) {
                  print("Error converting ccm_remarks: $e");
                }
              }

              if (cc['created_at'] != null) {
                try {
                  String timestamp = convertValue(cc['created_at'], String);
                  createdAt = convertTimestamp(timestamp);
                } catch (e) {
                  print("Error converting created_at: $e");
                }
              }

              if (cc['updated_at'] != null) {
                try {
                  updatedAt = convertValue(cc['updated_at'], String);
                } catch (e) {
                  print("Error converting updated_at: $e");
                }
              }

              return {
                'ccm_code': ccmCode,
                'ccm_type_id': ccmTypeId,
                'ccm_note': ccmNote,
                'ccm_image': ccmImage,
                'cm_code': cmCode,
                'emp_code': empCode,
                'ccm_cc_type': ccmCcType,
                'ccm_status': ccmStatus,
                'ccm_remarks': ccmRemarks,
                'created_at': createdAt,
                'updated_at': updatedAt,
                'ccm_sync_status': ccmSyncStatus,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableClaimComplain}",
                withData: filteredComplains);

            List<String> apiDataCodes =
                filteredComplains.map((data) => "${data["ccm_code"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableClaimComplain}",
                forCode: "${TableValues.ccCode}",
                withData: apiDataCodes);

            editTask(taskClaimComplain, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskClaimComplain, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskClaimComplain, 3);
      }
    } on DioException catch (e) {
      editTask(taskClaimComplain, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskClaimComplain,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskClaimComplain, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskClaimComplain,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getExpenseType() async {
    try {
      TotalTask task = TotalTask(
          taskName: taskExpenseType, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getExpenseTypeEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskExpenseType, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> expenseTypes =
                List<Map<String, dynamic>>.from(response.data['expense_type']);

            print("expenseTypes: ${expenseTypes}");

            List<Map<String, dynamic>> filteredET = expenseTypes.map((pc) {
              int etId = 0;
              String etName = "";

              if (pc['et_id'] != null) {
                try {
                  etId = convertValue(pc['et_id'], int);
                } catch (e) {
                  print("Error converting et_id: $e");
                }
              }

              if (pc['et_name'] != null) {
                try {
                  etName = convertValue(pc['et_name'], String);
                } catch (e) {
                  print("Error converting et_name: $e");
                }
              }

              return {
                'et_id': etId,
                'et_name': etName,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableExpenseType}",
                withData: filteredET);

            List<String> apiDataCodes =
                filteredET.map((data) => "${data["et_id"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableExpenseType}",
                forCode: "${TableValues.expenseTypeID}",
                withData: apiDataCodes);

            editTask(taskExpenseType, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskExpenseType, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskExpenseType, 3);
      }
    } on DioException catch (e) {
      editTask(taskExpenseType, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskExpenseType,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskExpenseType, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskExpenseType,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getExpense() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskExpense, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      print(
          "Expense URL: ${ApiClient.getUrl(ApiClient.getExpenseEndpoint + '${syncDays}')}");
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getExpenseEndpoint + '${syncDays}'),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskExpense, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> expenses =
                List<Map<String, dynamic>>.from(response.data['expense']);

            List<Map<String, dynamic>> filteredExpenses =
                expenses.map((expense) {
              int emExpenseType = 0;
              String emCode = '';
              String emExpenseDetail = '';
              double emAmount = 0.0;
              double emApprovedAmount = 0.0;
              String emBillPicture = '';
              String emFileExtension = '';
              double latitude = 0.0;
              double longitude = 0.0;
              String empCode = '';
              int emExpenseStatus = 0;
              String emDate = '';
              int emKm = 0;
              int emSyncStatus = 1;
              // Process values
              if (expense['em_expense_type'] != null) {
                try {
                  emExpenseType = convertValue(expense['em_expense_type'], int);
                } catch (e) {
                  print("Error converting em_expense_type: $e");
                }
              }

              if (expense['em_code'] != null) {
                try {
                  emCode = convertValue(expense['em_code'], String);
                } catch (e) {
                  print("Error converting em_code: $e");
                }
              }

              if (expense['em_expense_detail'] != null) {
                try {
                  emExpenseDetail =
                      convertValue(expense['em_expense_detail'], String);
                } catch (e) {
                  print("Error converting em_expense_detail: $e");
                }
              }

              if (expense['em_amount'] != null) {
                try {
                  emAmount = convertValue(expense['em_amount'], double);
                } catch (e) {
                  print("Error converting em_amount: $e");
                }
              }

              if (expense['em_approved_amount'] != null) {
                try {
                  emApprovedAmount =
                      convertValue(expense['em_approved_amount'], double);
                } catch (e) {
                  print("Error converting em_approved_amount: $e");
                }
              }

              if (expense['em_bill_picture'] != null) {
                try {
                  emBillPicture =
                      convertValue(expense['em_bill_picture'], String);
                } catch (e) {
                  print("Error converting em_bill_picture: $e");
                }
              }

              if (expense['file_extension'] != null) {
                try {
                  emFileExtension =
                      convertValue(expense['file_extension'], String);
                } catch (e) {
                  print("Error converting file_extension: $e");
                }
              }

              if (expense['em_lat'] != null) {
                try {
                  latitude = convertValue(expense['em_lat'], double);
                } catch (e) {
                  print("Error converting latitude: $e");
                }
              }

              if (expense['em_long'] != null) {
                try {
                  longitude = convertValue(expense['em_long'], double);
                } catch (e) {
                  print("Error converting longitude: $e");
                }
              }

              if (expense['emp_code'] != null) {
                try {
                  empCode = convertValue(expense['emp_code'], String);
                } catch (e) {
                  print("Error converting emp_code: $e");
                }
              }

              if (expense['em_expense_status'] != null) {
                try {
                  emExpenseStatus =
                      convertValue(expense['em_expense_status'], int);
                } catch (e) {
                  print("Error converting em_expense_status: $e");
                }
              }

              if (expense['em_date'] != null) {
                try {
                  emDate = convertValue(expense['em_date'], String);
                } catch (e) {
                  print("Error converting em_date: $e");
                }
              }

              if (expense['em_km'] != null) {
                try {
                  emKm = convertValue(expense['em_km'], int);
                } catch (e) {
                  print("Error converting em_km: $e");
                }
              }

              return {
                'em_expense_type': emExpenseType,
                'em_code': emCode,
                'em_expense_detail': emExpenseDetail,
                'em_amount': emAmount,
                'em_approved_amount': emApprovedAmount,
                'em_bill_picture': emBillPicture,
                'file_extension': emFileExtension,
                'latitude': latitude,
                'longitude': longitude,
                'emp_code': empCode,
                'em_expense_status': emExpenseStatus,
                'em_date': emDate,
                'em_km': emKm,
                'em_sync_status': emSyncStatus,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableExpense}",
                withData: filteredExpenses);

            List<String> apiDataCodes =
                filteredExpenses.map((data) => "${data["em_code"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableExpense}",
                forCode: "${TableValues.expenseCode}",
                withData: apiDataCodes);

            editTask(taskExpense, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskExpense, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskExpense, 3);
      }
    } on DioException catch (e) {
      editTask(taskExpense, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskExpense,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskExpense, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskExpense,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getNonProductiveReason() async {
    try {
      TotalTask task = TotalTask(
          taskName: taskNonproductiveReason, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getNonProductiveReasonEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskNonproductiveReason, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> nonproductiveReasons =
                List<Map<String, dynamic>>.from(
                    response.data['non_productive_reason']);

            List<Map<String, dynamic>> filteredNPR =
                nonproductiveReasons.map((pc) {
              int nprId = 0;
              String nprName = "";

              if (pc['npr_id'] != null) {
                try {
                  nprId = convertValue(pc['npr_id'], int);
                } catch (e) {
                  print("Error converting npr_id: $e");
                }
              }

              if (pc['npr_reason'] != null) {
                try {
                  nprName = convertValue(pc['npr_reason'], String);
                } catch (e) {
                  print("Error converting npr_reason: $e");
                }
              }

              return {
                'npr_id': nprId,
                'npr_reason': nprName,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableNonProductiveReason}",
                withData: filteredNPR);

            List<String> apiDataCodes =
                filteredNPR.map((data) => "${data["npr_id"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableNonProductiveReason}",
                forCode: "${TableValues.nprID}",
                withData: apiDataCodes);

            editTask(taskNonproductiveReason, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskNonproductiveReason, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskNonproductiveReason, 3);
      }
    } on DioException catch (e) {
      editTask(taskNonproductiveReason, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskExpenseType,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskExpenseType, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskNonproductiveReason,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getPaymentType() async {
    try {
      TotalTask task = TotalTask(
          taskName: taskPaymentType, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getPaymentTypeEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskPaymentType, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> paymentTypes =
                List<Map<String, dynamic>>.from(response.data['payment_type']);

            List<Map<String, dynamic>> filteredPT = paymentTypes.map((pc) {
              int ptId = 0;
              String ptName = "";

              if (pc['pt_id'] != null) {
                try {
                  ptId = convertValue(pc['pt_id'], int);
                } catch (e) {
                  print("Error converting pt_id: $e");
                }
              }

              if (pc['pt_name'] != null) {
                try {
                  ptName = convertValue(pc['pt_name'], String);
                } catch (e) {
                  print("Error converting pt_name: $e");
                }
              }

              return {
                'pt_id': ptId,
                'pt_name': ptName,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tablePaymentType}",
                withData: filteredPT);

            List<String> apiDataCodes =
                filteredPT.map((data) => "${data["pt_id"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tablePaymentType}",
                forCode: "${TableValues.paymentTypeID}",
                withData: apiDataCodes);

            editTask(taskPaymentType, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskPaymentType, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskPaymentType, 3);
      }
    } on DioException catch (e) {
      editTask(taskPaymentType, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskPaymentType,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskPaymentType, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskPaymentType,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getPaymentCondition() async {
    try {
      TotalTask task = TotalTask(
          taskName: taskPaymentCondition, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getPaymentConditionEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskPaymentCondition, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print("payment_condition: ${response.data}");

            final List<Map<String, dynamic>> paymentConditions =
                List<Map<String, dynamic>>.from(
                    response.data['payment_condition']);

            List<Map<String, dynamic>> filteredPC = paymentConditions.map((pc) {
              int pcId = 0;
              String pcName = "";

              if (pc['pc_id'] != null) {
                try {
                  pcId = convertValue(pc['pc_id'], int);
                } catch (e) {
                  print("Error converting pc_id: $e");
                }
              }

              if (pc['pc_name'] != null) {
                try {
                  pcName = convertValue(pc['pc_name'], String);
                } catch (e) {
                  print("Error converting pc_name: $e");
                }
              }

              return {
                'pc_id': pcId,
                'pc_name': pcName,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tablePaymentCondition}",
                withData: filteredPC);

            List<String> apiDataCodes =
                filteredPC.map((data) => "${data["pc_id"]}").toList();
            print("apiDataCodes: ${apiDataCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tablePaymentCondition}",
                forCode: "${TableValues.pcID}",
                withData: apiDataCodes);

            editTask(taskPaymentCondition, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskPaymentCondition, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskPaymentCondition, 3);
      }
    } on DioException catch (e) {
      editTask(taskPaymentCondition, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskPaymentCondition,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskPaymentCondition, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskPaymentCondition,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getPaymentCollection() async {
    try {
      TotalTask task = TotalTask(
          taskName: taskPaymentCollection, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };

      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(
            ApiClient.getPaymentCollectionEndpoint + '${syncDays}'),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskPaymentCollection, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> paymentCollections =
                List<Map<String, dynamic>>.from(
                    response.data['payment_collection']);

            // Create a new list containing only the desired fields
            List<Map<String, dynamic>> filteredPC =
                paymentCollections.map((pc) {
              String pcCode = '';
              String pcCmCode = '';
              int pcType = 0;
              String pcChequeDate = '';
              double pcAmount = 0.0;
              int pcBankId = 0;
              String pcChequeNo = '';
              String pcNote = '';
              String pcEmpCode = '';
              String pcAccountNo = '';
              String pcChequePhoto = '';
              int pcConditionId = 0;
              int pcStatus = 0;
              String createdAt = '';
              String updatedAt = '';
              int pcSyncStatus = 1; 

             
              if (pc['pc_code'] != null) {
                try {
                  pcCode = convertValue(pc['pc_code'], String);
                } catch (e) {
                  print("Error converting pc_code: $e");
                }
              }

              if (pc['cm_code'] != null) {
                try {
                  pcCmCode = convertValue(pc['cm_code'], String);
                } catch (e) {
                  print("Error converting pc_cm_code: $e");
                }
              }

              if (pc['pc_type'] != null) {
                try {
                  pcType = convertValue(pc['pc_type'], int);
                } catch (e) {
                  print("Error converting pc_type: $e");
                }
              }

              if (pc['pc_cheque_date'] != null) {
                try {
                  pcChequeDate = convertValue(pc['pc_cheque_date'], String);
                } catch (e) {
                  print("Error converting pc_cheque_date: $e");
                }
              }

              if (pc['pc_amount'] != null) {
                try {
                  pcAmount = convertValue(pc['pc_amount'], double);
                } catch (e) {
                  print("Error converting pc_amount: $e");
                }
              }

              if (pc['pc_bank_id'] != null) {
                try {
                  pcBankId = convertValue(pc['pc_bank_id'], int);
                } catch (e) {
                  print("Error converting pc_bank_id: $e");
                }
              }

              if (pc['pc_cheque_no'] != null) {
                try {
                  pcChequeNo = convertValue(pc['pc_cheque_no'], String);
                } catch (e) {
                  print("Error converting pc_cheque_no: $e");
                }
              }

              if (pc['pc_note'] != null) {
                try {
                  pcNote = convertValue(pc['pc_note'], String);
                } catch (e) {
                  print("Error converting pc_note: $e");
                }
              }

              if (pc['emp_code'] != null) {
                try {
                  pcEmpCode = convertValue(pc['emp_code'], String);
                } catch (e) {
                  print("Error converting pc_emp_code: $e");
                }
              }

              if (pc['pc_account_no'] != null) {
                try {
                  pcAccountNo = convertValue(pc['pc_account_no'], String);
                } catch (e) {
                  print("Error converting pc_account_no: $e");
                }
              }

              if (pc['pc_cheque_photo'] != null) {
                try {
                  pcChequePhoto = convertValue(pc['pc_cheque_photo'], String);
                } catch (e) {
                  print("Error converting pc_cheque_photo: $e");
                }
              }

              if (pc['pc_condition_id'] != null) {
                try {
                  pcConditionId = convertValue(pc['pc_condition_id'], int);
                } catch (e) {
                  print("Error converting pc_condition_id: $e");
                }
              }

              if (pc['pc_status'] != null) {
                try {
                  pcStatus = convertValue(pc['pc_status'], int);
                } catch (e) {
                  print("Error converting pc_status: $e");
                }
              }

              if (pc['created_at'] != null) {
                try {
                  String timestamp = convertValue(pc['created_at'], String);
                  createdAt = convertTimestamp(timestamp);
                } catch (e) {
                  print("Error converting created_at: $e");
                }
              }

              if (pc['updated_at'] != null) {
                try {
                  updatedAt = convertValue(pc['updated_at'], String);
                } catch (e) {
                  print("Error converting updated_at: $e");
                }
              }

              return {
                'pc_code': pcCode,
                'pc_cm_code': pcCmCode,
                'pc_type': pcType,
                'pc_cheque_date': pcChequeDate,
                'pc_amount': pcAmount,
                'pc_bank_id': pcBankId,
                'pc_cheque_no': pcChequeNo,
                'pc_note': pcNote,
                'pc_emp_code': pcEmpCode,
                'pc_account_no': pcAccountNo,
                'pc_cheque_photo': pcChequePhoto,
                'pc_condition_id': pcConditionId,
                'pc_status': pcStatus,
                'created_at': createdAt,
                'updated_at': updatedAt,
                'pc_sync_status': pcSyncStatus,
              };
            }).toList();
            await insertValues(
                toTable: "${TableValues.tablePaymentCollection}",
                withData: filteredPC);

            editTask(taskPaymentCollection, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskPaymentCollection, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskPaymentCollection, 3);
      }
    } on DioException catch (e) {
      editTask(taskPaymentCollection, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskPaymentCollection,
        apiRequestData: null,
        apiResponse: 'DioException: ${e}',
      );
    } on TimeoutException catch (e) {
      editTask(taskPaymentCollection, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskPaymentCollection,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getLeave() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskLeave, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getLeaveEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskLeave, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> leaves =
                List<Map<String, dynamic>>.from(
                    response.data['leave_application']);

            // Create a new list containing only the desired fields
            List<Map<String, dynamic>> filteredLeaves = leaves.map((leave) {
              // Declare values
              String laCode = '';
              int laTypeId = 0;
              String laFromDate = '';
              String laToDate = '';
              int laTotalDay = 0;
              int laApprovedStatus = 0;
              String laReason = '';
              String laEmpCode = '';
              int laNotificationStatus = 0;

              int laApprovedBy = 0;
              String laCreatedAt = '';
              int leaveSyncStatus = 1; 

              
              if (leave['la_code'] != null) {
                try {
                  laCode = convertValue(leave['la_code'], String);
                } catch (e) {
                  print("Error converting la_code: $e");
                }
              }

              if (leave['la_type_id'] != null) {
                try {
                  laTypeId = convertValue(leave['la_type_id'], int);
                } catch (e) {
                  print("Error converting la_type_id: $e");
                }
              }

              if (leave['la_from_date'] != null) {
                try {
                  laFromDate = convertValue(leave['la_from_date'], String);
                } catch (e) {
                  print("Error converting la_from_date: $e");
                }
              }

              if (leave['la_to_date'] != null) {
                try {
                  laToDate = convertValue(leave['la_to_date'], String);
                } catch (e) {
                  print("Error converting la_to_date: $e");
                }
              }

              if (leave['la_total_day'] != null) {
                try {
                  laTotalDay = convertValue(leave['la_total_day'], int);
                } catch (e) {
                  print("Error converting la_total_day: $e");
                }
              }

              if (leave['la_approved_status'] != null) {
                try {
                  laApprovedStatus =
                      convertValue(leave['la_approved_status'], int);
                } catch (e) {
                  print("Error converting la_approved_status: $e");
                }
              }

              if (leave['la_reason'] != null) {
                try {
                  laReason = convertValue(leave['la_reason'], String);
                } catch (e) {
                  print("Error converting la_reason: $e");
                }
              }

              if (leave['emp_code'] != null) {
                try {
                  laEmpCode = convertValue(leave['emp_code'], String);
                } catch (e) {
                  print("Error converting la_emp_code: $e");
                }
              }

              if (leave['la_notification_status'] != null) {
                try {
                  laNotificationStatus =
                      convertValue(leave['la_notification_status'], int);
                } catch (e) {
                  print("Error converting la_notification_status: $e");
                }
              }

              if (leave['la_approved_by'] != null) {
                try {
                  laApprovedBy = convertValue(leave['la_approved_by'], int);
                } catch (e) {
                  print("Error converting la_approved_by: $e");
                }
              }

              if (leave['created_at'] != null) {
                try {
                  String timestamp = convertValue(leave['created_at'], String);
                  laCreatedAt = convertTimestamp(timestamp);
                } catch (e) {
                  print("Error converting created_at: $e");
                }
              }

              return {
                'la_code': laCode,
                'la_type_id': laTypeId,
                'la_from_date': laFromDate,
                'la_to_date': laToDate,
                'la_total_day': laTotalDay,
                'la_approved_status': laApprovedStatus,
                'la_reason': laReason,
                'la_emp_code': laEmpCode,
                'la_notification_status': laNotificationStatus,
                'la_approved_by': laApprovedBy,
                'created_at': laCreatedAt,
                'leave_sync_status': leaveSyncStatus,
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableLeaveRequest}",
                withData: filteredLeaves);

            editTask(taskLeave, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskLeave, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskLeave, 3);
      }
    } on DioException catch (e) {
      editTask(taskLeave, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskLeave,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskLeave, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskLeave,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getCall() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskCall, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      print(
          "Call URL: ${ApiClient.getUrl(ApiClient.getCallEndpoint + '${syncDays}')}");
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.getCallEndpoint + '${syncDays}'),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskCall, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> calls =
                List<Map<String, dynamic>>.from(response.data['call_master']);

            print("calls.length: ${calls.length}");
            print("calls.data: ${calls}");

            // Create a new list containing only the desired fields
            List<Map<String, dynamic>> filteredCalls = calls.map((call) {
              // Declare values
              String callCode = '';
              String callEmpCode = '';
              String callClientCode = '';
              double callLatitude = 0.0;
              double callLongitude = 0.0;
              String callAccuracy = '';
              String callPartyCode = '';
              int callTotalQty = 0;
              double callGrandTotal = 0.0;
              int callOrderReasonId = 0;
              int callOrderType = 0;
              String callRemark = '';
              double callPackagingCharge = 0.0;
              double callTransportationCharge = 0.0;
              String callTransporterName = '';
              String callStartTime = '';
              String callStopTime = '';
              String callOrderSign = '';
              int callIsTelephonic = 0;
              String callCreatedAt = '';
              int callOrderSyncStatus = 1;

              
              if (call['call_code'] != null) {
                try {
                  callCode = convertValue(call['call_code'], String);
                } catch (e) {
                  print("Error converting call_code: $e");
                }
              }

              if (call['emp_code'] != null) {
                try {
                  callEmpCode = convertValue(call['emp_code'], String);
                } catch (e) {
                  print("Error converting call_emp_code: $e");
                }
              }

              if (call['client_code'] != null) {
                try {
                  callClientCode = convertValue(call['client_code'], String);
                } catch (e) {
                  print("Error converting call_client_code: $e");
                }
              }

              if (call['call_latitude'] != null) {
                try {
                  callLatitude = convertValue(call['call_latitude'], double);
                } catch (e) {
                  print("Error converting call_latitude: $e");
                }
              }

              if (call['call_longitude'] != null) {
                try {
                  callLongitude = convertValue(call['call_longitude'], double);
                } catch (e) {
                  print("Error converting call_longitude: $e");
                }
              }

              if (call['accuracy'] != null) {
                try {
                  callAccuracy = convertValue(call['accuracy'], String);
                } catch (e) {
                  print("Error converting call_accuracy: $e");
                }
              }

              if (call['party_code'] != null) {
                try {
                  callPartyCode = convertValue(call['party_code'], String);
                } catch (e) {
                  print("Error converting call_party_code: $e");
                }
              }

              if (call['total_quantity'] != null) {
                try {
                  callTotalQty = convertValue(call['total_quantity'], int);
                } catch (e) {
                  print("Error converting call_total_qty: $e");
                }
              }

              if (call['grand_total'] != null) {
                try {
                  callGrandTotal = convertValue(call['grand_total'], double);
                } catch (e) {
                  print("Error converting call_grand_total: $e");
                }
              }

              if (call['reason_id'] != null) {
                try {
                  callOrderReasonId = convertValue(call['reason_id'], int);
                } catch (e) {
                  print("Error converting call_order_reason_id: $e");
                }
              }

              if (call['product_order_type'] != null) {
                try {
                  callOrderType = convertValue(call['product_order_type'], int);
                } catch (e) {
                  print("Error converting call_order_type: $e");
                }
              }

              if (call['remarks'] != null) {
                try {
                  callRemark = convertValue(call['remarks'], String);
                } catch (e) {
                  print("Error converting call_remark: $e");
                }
              }

              if (call['packaging_charge'] != null) {
                try {
                  callPackagingCharge =
                      convertValue(call['packaging_charge'], double);
                } catch (e) {
                  print("Error converting call_packaging_charge: $e");
                }
              }

              if (call['transportation_charge'] != null) {
                try {
                  callTransportationCharge =
                      convertValue(call['transportation_charge'], double);
                } catch (e) {
                  print("Error converting call_transportation_charge: $e");
                }
              }

              if (call['transportation_name'] != null) {
                try {
                  callTransporterName =
                      convertValue(call['transportation_name'], String);
                } catch (e) {
                  print("Error converting call_transportation_name: $e");
                }
              }

              if (call['start_time'] != null) {
                try {
                  callStartTime = convertValue(call['start_time'], String);
                } catch (e) {
                  print("Error converting call_start_time: $e");
                }
              }

              if (call['stop_time'] != null) {
                try {
                  callStopTime = convertValue(call['stop_time'], String);
                } catch (e) {
                  print("Error converting call_stop_time: $e");
                }
              }

              if (call['order_sign'] != null) {
                try {
                  callOrderSign = convertValue(call['order_sign'], String);
                } catch (e) {
                  print("Error converting call_order_sign: $e");
                }
              }

              if (call['is_telephonic'] != null) {
                try {
                  callIsTelephonic = convertValue(call['is_telephonic'], int);
                } catch (e) {
                  print("Error converting call_is_telephonic: $e");
                }
              }

              if (call['created_at'] != null) {
                try {
                  String timestamp = convertValue(call['created_at'], String);
                  callCreatedAt = convertTimestamp(timestamp);
                } catch (e) {
                  print("Error converting call_created_at: $e");
                }
              }

              return {
                'call_code': callCode,
                'call_emp_code': callEmpCode,
                'call_client_code': callClientCode,
                'call_latitude': callLatitude,
                'call_longitude': callLongitude,
                'call_accuracy': callAccuracy,
                'call_party_code': callPartyCode,
                'call_total_qty': callTotalQty,
                'call_grand_total': callGrandTotal,
                'call_order_reason_id': callOrderReasonId,
                'call_order_type': callOrderType,
                'call_remark': callRemark,
                'call_packaging_charge': callPackagingCharge,
                'call_transportation_charge': callTransportationCharge,
                'call_transporter_name': callTransporterName,
                'call_start_time': callStartTime,
                'call_stop_time': callStopTime,
                'call_order_sign': callOrderSign,
                'call_is_telephonic': callIsTelephonic,
                'call_created_at': callCreatedAt,
                'call_order_sync_status': callOrderSyncStatus,
              };
            }).toList();

            print('filteredCalls.length: ${filteredCalls.length}');
            print('filteredCalls: ${filteredCalls}');

            await insertValues(
                toTable: "${TableValues.tableCall}", withData: filteredCalls);

            editTask(taskCall, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskCall, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskCall, 3);
      }
    } on DioException catch (e) {
      editTask(taskCall, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskCall,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskCall, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskCall,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getOrder() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskOrders, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.getOrderEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskOrders, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> calls =
                List<Map<String, dynamic>>.from(response.data['call_master']);

            for (Map<String, dynamic> call in calls) {
              print("orders: ${call}");

              final List<Map<String, dynamic>> orders =
                  List<Map<String, dynamic>>.from(call['orders']);

              List<Map<String, dynamic>> filteredOrders = orders.map((order) {
                return {
                  'order_call_code': call['call_code'],
                  'order_code': order['order_code'],
                  'order_product_code': order['product_code'],
                  'order_product_qty': order['quantity'],
                  'order_product_mrp': order['mrp'],
                  'order_product_pts': order['pts'],
                  'order_product_base_rate': order['rate_basic'],
                  'order_product_total_before_tax': order['total_basic_rate'],
                  'order_product_total_price_with_tax': order['grand_total'],
                  'order_product_tax': order['gst'],
                  'order_product_tax_amount': order['gst_amount'],
                  'order_piece': order['pcs'],
                  'order_bunch': order['bunch'],
                  'order_box': order['box'],
                  'order_product_sync_status': 1,
                };
              }).toList();

              print('filteredOrders: ${filteredOrders}');

              await insertValues(
                  toTable: "${TableValues.tableOrder}",
                  withData: filteredOrders);
            }

            editTask(taskOrders, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskOrders, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskOrders, 3);
      }
    } on DioException catch (e) {
      editTask(taskOrders, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskOrders,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskOrders, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskOrders,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getTarget() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskTarget, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };

      print("Target URL: ${ApiClient.getUrl(ApiClient.getReportEndpoint)}");

      DateTime now = DateTime.now();
      int currentMonth = now.month;
      int currentYear = now.year;

      String formattedMonth = currentMonth.toString().padLeft(2, '0');
      String formattedYear = currentYear.toString().padLeft(4, '0');

      print('Current year (with leading zeros if necessary): $formattedYear');
      print('Current month number (with leading zeros): $formattedMonth');

      Map<String, dynamic> data = {
        "report": "target_vs_achivement",
        "month": formattedMonth,
        "year": formattedYear,
        "search_employee": employeeCode.value,
        "d_type": "json",
      };
      print("data: ${data}");
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.getReportEndpoint),
        options: Options(headers: headers),
        data: data,
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print("response.data: ${response.data}");

            final List<Map<String, dynamic>> targets =
                List<Map<String, dynamic>>.from(response.data['target']);
            print("targets: ${targets}");

            final List<Target> targetObjects =
                targets.map((target) => Target.fromJson(target)).toList();
            print("targetObjects: ${targetObjects}");

            await SharedPrefManager.instance.setTargets(targetObjects);
            print('Targets stored in SharedPreferences.');

            // Retrieve targets from SharedPreferences
            final List<Target> retrievedTargets =
                await SharedPrefManager.instance.getTargets();
            if (retrievedTargets.isNotEmpty) {
              print('Retrieved targets:');
              retrievedTargets.forEach((target) {
                print(target.toJson());
              });
            } else {
              print('No targets stored in SharedPreferences.');
            }

            print("retrievedTargets: ${retrievedTargets}");

            editTask(taskTarget, 2);
          }
        } else {
          print('Response data is null');
          editTask(taskTarget, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskTarget, 2);
      }
    } on DioException catch (e) {
      editTask(taskTarget, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskTarget,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskTarget, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskTarget,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      editTask(taskTarget, 3);
      print('Unexpected error: $e');
    }
  }

  Future<void> getConfig() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskConfig, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getConfigEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskConfig, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.statusCode == 200) {
          if (response.data != null && response.data['config'] != null) {
            print("config: ${response.data}");

            final List<Map<String, dynamic>> configs =
                List<Map<String, dynamic>>.from(response.data['config']);

            List<Map<String, dynamic>> filteredConfigs = configs.map((pc) {
              return {
                'config_id': pc['id'],
                'config_key': pc['key'],
                'config_value': pc['value'],
                'config_type': pc['type'],
              };
            }).toList();

            await insertValues(
                toTable: "${TableValues.tableConfig}",
                withData: filteredConfigs);

            editTask(taskConfig, 2);
          } else {
            print('Response data or config is null');
            editTask(taskConfig, 3);
          }
        } else {
          print('Request failed with status: ${response.statusCode}');
          editTask(taskConfig, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskConfig, 3);
      }
    } on DioException catch (e) {
      editTask(taskConfig, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskConfig,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskConfig, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskConfig,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getLanguage() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskLanguage, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getLanguageEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskLanguage, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.statusCode == 200) {
          if (response.data != null && response.data['language_data'] != null) {
            print("language_data: ${response.data}");

            final Map<String, dynamic> languageData =
                Map<String, dynamic>.from(response.data['language_data']);
            List<Map<String, dynamic>> languages = [];

            languageData.forEach((key, value) {
              final langCode = value['lang_code'] ?? '';
              final langName = value['lang_name'] ?? '';
              final translations =
                  Map<String, dynamic>.from(value['translations'] ?? {});

              translations.forEach((transKey, transValue) {
                languages.add({
                  'lang_code': langCode,
                  'lang_name': langName,
                  'lang_key': transKey,
                  'lang_value': transValue,
                });
              });
            });

            debugPrint("languages: ${languages}");

            await insertValues(
                toTable: "${TableValues.tableLanguage}", withData: languages);

            await loadLocalizedStrings();

            editTask(taskLanguage, 2);
          } else {
            print('Response data or config is null');
            editTask(taskLanguage, 3);
          }
        } else {
          print('Request failed with status: ${response.statusCode}');
          editTask(taskLanguage, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskLanguage, 3);
      }
    } on DioException catch (e) {
      editTask(taskLanguage, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskLanguage,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskLanguage, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskLanguage,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {}
  }

  Future<void> getDashboardData() async {
    try {
      TotalTask task =
          TotalTask(taskName: taskDashboard, taskStatus: RxInt(1), taskType: 1);
      addTask(task);
      progress.value += 1;

      print(progress.value);
      print(tasks.length.toDouble());

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getDashboardDataEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        editTask(taskDashboard, 3);
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.statusCode == 200) {
          if (response.data != null && response.data['tc_pc'] != null) {
            print("language_data: ${response.data}");

            Map<String, dynamic> user = response.data['tc_pc'];

            SharedPrefManager.instance
                .setString(ConstantValues.TCPC, jsonEncode(user));

            var dashboardData =
                await SharedPrefManager.instance.getString(ConstantValues.TCPC);
            print("dashboardData: ${dashboardData}");
            editTask(taskDashboard, 2);
          } else {
            print('Response data or config is null');
            editTask(taskDashboard, 3);
          }
        } else {
          print('Request failed with status: ${response.statusCode}');
          editTask(taskDashboard, 3);
        }
      } catch (e) {
        print('Error parsing response data: $e');
        editTask(taskDashboard, 3);
      }
    } on DioException catch (e) {
      editTask(taskDashboard, 3);
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: taskDashboard,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      editTask(taskDashboard, 3);
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: taskDashboard,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {
      if (progress.value == tasksProgress.length) {
        print("fetchProgressValueAndStore called");
        fetchProgressValueAndStore();
      } else {
        print("fetchProgressValueAndStore not called");
      }
    }
  }

  Future<void> loadLocalizedStrings() async {
    Map<String, String> localizationData = {};

    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query('${TableValues.tableLanguage}');

    if (maps.isNotEmpty) {
      for (var map in maps) {
        localizationData[map['${TableValues.langTranslateKey}']] =
            map['${TableValues.langTranslateValue}'];
      }

      debugPrint("localizationData: ${localizationData}");
    }
    print("localizationData home: ${localizationData}");
  }

  String convertTimestamp(String timestamp) {
    DateTime dateTime = DateTime.parse(timestamp);
    String formattedDate = DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
    return formattedDate;
  }

  static Future<void> deleteDataNotInList(
      {required String fromTable,
      required String forCode,
      required List<String> withData}) async {
    final db = await DatabaseProvider.database;
    final beatCodesString = withData.map((code) => "'$code'").join(', ');
    await db.delete("${fromTable}",
        where: '${forCode} NOT IN ($beatCodesString)');
  }

  static Future<void> insertValues(
      {required String toTable,
      required List<Map<String, dynamic>> withData}) async {
    final db = await DatabaseProvider.database;
    try {
      await db.transaction((txn) async {
        Batch batch = txn.batch();
        for (var data in withData) {
          batch.insert(toTable, data,
              conflictAlgorithm: ConflictAlgorithm.replace);
        }
        await batch.commit(noResult: false);
      });
    } catch (e) {
      throw Exception('Failed to insert data: $e');
    }
  }

  Future<Map<String, String>> getLocalizationStrings(String langCode) async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps = await db.query(
      '${TableValues.tableLanguage}',
      where: '${TableValues.langCode} = ?',
      whereArgs: [langCode],
    );
    return {
      for (var map in maps)
        map['${TableValues.langTranslateKey}']:
            map['${TableValues.langTranslateValue}']
    };
  }

  Future<void> insertDistrictsAndTowns(
      List<Map<String, dynamic>> districtsAndTowns) async {
    final db = await DatabaseProvider.database;
    await db.transaction(
      (txn) async {
        for (var districtTown in districtsAndTowns) {
          print("districtTown: ${districtTown}");
          int districtId = await txn.insert(
              '${TableValues.tableDistrict}',
              {
                '${TableValues.dmID}': districtTown['${TableValues.dmID}'],
                '${TableValues.stateID}':
                    districtTown['${TableValues.stateID}'],
                '${TableValues.dmName}': districtTown['${TableValues.dmName}'],
              },
              conflictAlgorithm: ConflictAlgorithm.replace);

          // Insert towns
          List<Map<String, dynamic>> towns =
              List<Map<String, dynamic>>.from(districtTown['towns']);
          print("towns: ${towns}");
          for (var town in towns) {
            await txn.insert(
                '${TableValues.tableTown}',
                {
                  '${TableValues.townID}': town['${TableValues.townID}'],
                  '${TableValues.townName}': town['${TableValues.townName}'],
                  '${TableValues.districtID}': districtId,
                },
                conflictAlgorithm: ConflictAlgorithm.replace);
          }
        }
      },
    );
  }
}
