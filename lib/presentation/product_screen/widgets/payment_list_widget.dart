// ignore_for_file: invalid_use_of_protected_member, must_be_immutable

import 'package:flutter/material.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/filter_option.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/product_screen/controller/product_controller.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/sfmDatabase/models/payment_collection_model.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';
import 'package:sfm_new/widgets/custom_icon_button.dart';

class PaymentListWidget extends StatelessWidget {
  final ProductController controller = Get.put(ProductController());

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 8.v),
        CustomSearchBarPayment(),
        SizedBox(height: 8.v),
        _buildPaymentList(),
        <PERSON><PERSON><PERSON><PERSON>(height: 12.v),
        _buildBottomDetails(),
      ],
    );
  }

  Widget _buildPaymentList() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 4.h),
        child: Obx(
          () {
            if (controller.isLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            } else if (controller.filteredPaymentList.isEmpty) {
              return Center(
                child: Text(
                  'No payment found',
                  style: TextStyle(fontSize: 16),
                ),
              );
            } else {
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.h),
                child: ListView.separated(
                  itemCount: controller.filteredPaymentList.length,
                  physics: BouncingScrollPhysics(),
                  shrinkWrap: true,
                  separatorBuilder: (
                    context,
                    index,
                  ) {
                    return SizedBox(
                      height: 10.v,
                    );
                  },
                  itemBuilder: (context, index) => CustomPaymentListItem(
                    payment: controller.filteredPaymentList[index],
                  ),
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildBottomDetails() {
    return Obx(
      () => Container(
        color: Colors.transparent,
        height: 107.v,
        width: double.maxFinite,
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                height: 60.v,
                width: double.maxFinite,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(10.h),
                  ),
                ),
              ),
            ),
            Align(
              alignment: Alignment.center,
              child: Container(
                width: double.maxFinite,
                margin: EdgeInsets.only(top: 40.v),
                padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                decoration: AppDecoration.outlineBlack9007,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Total Payment",
                          style: CustomTextStyles.titleSmallOnErrorContainerBold
                              .copyWith(
                            color: theme.colorScheme.onErrorContainer
                                .withValues(alpha: 1),
                          ),
                        ),
                        Text(
                          '₹${controller.totalAmount.value}',
                          style: CustomTextStyles.titleSmallOnErrorContainerBold
                              .copyWith(
                            color: theme.colorScheme.onErrorContainer
                                .withValues(alpha: 1),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            Align(
              alignment: Alignment.topRight,
              child: Container(
                height: 70.adaptSize,
                width: 70.adaptSize,
                margin: EdgeInsets.only(right: 18.h),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Align(
                      alignment: Alignment.center,
                      child: Container(
                        height: 70.adaptSize,
                        width: 70.adaptSize,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          borderRadius: BorderRadius.circular(
                            35.h,
                          ),
                          border: Border.all(
                            color: theme.colorScheme.onErrorContainer
                                .withValues(alpha: 1),
                            width: 6.h,
                          ),
                        ),
                      ),
                    ),
                    CustomIconButton(
                      height: 70.adaptSize,
                      width: 70.adaptSize,
                      padding: EdgeInsets.all(19.h),
                      decoration: IconButtonStyleHelper.outlineOnErrorContainer,
                      alignment: Alignment.center,
                      child: CustomImageView(
                        imagePath: ImageConstant.imgGroup29,
                      ),
                      onTap: () {
                        print("Plus button pressed");
                        FocusManager.instance.primaryFocus?.unfocus();
                        SharedPrefManager.instance.setBool(
                            ConstantValues.isFromNormalAddPayment, false);
                        Get.toNamed(AppRoutes.addPaymentScreen);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomSearchBarPayment extends StatelessWidget {
  ProductController controller = Get.put(ProductController());

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 12, right: 12, top: 4, bottom: 4),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.5),
              spreadRadius: 2,
              blurRadius: 7,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                style: CustomTextStyles.bodyMediumBluegray700,
                onChanged: (value) {
                  print('Search value: $value');
                },
                decoration: InputDecoration(
                  hintText: 'Search Payment',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 10.0),
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(10.0),
                  bottomRight: Radius.circular(10.0),
                ),
              ),
              child: IconButton(
                icon: Container(
                  child: CustomImageView(
                    svgPath: ImageConstant.imgFilterIcon,
                    height: 20.adaptSize,
                    width: 20.adaptSize,
                  ),
                ),
                onPressed: () {
                  print('Filter button pressed');
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return CustomDialogFilterPayment();
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomDialogFilterPayment extends StatelessWidget {
  CustomDialogFilterPayment({
    Key? key,
  }) : super(key: key);

  ProductController controller = Get.put(ProductController());

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      insetPadding: EdgeInsets.all(12),
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  "Filters",
                  style: CustomTextStyles.titleLargeGray900,
                ),
              ],
            ),
            SizedBox(height: 8.v),
            filterPaymentByType(),
            SizedBox(height: 16.v),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  "Sort By",
                  style: CustomTextStyles.titleLargeGray900,
                ),
              ],
            ),
            SizedBox(height: 8.v),
            filterPaymentByStatus(),
            SizedBox(height: 16.v),
            _buildDate(context),
            SizedBox(height: 20.v),
            Container(
              width: MediaQuery.of(context).size.width * 0.75,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomElevatedButton(
                    height: 45.v,
                    width: MediaQuery.of(context).size.width * 0.35,
                    text: "lbl_cancel".tr,
                    buttonStyle: CustomButtonStyles.fillOnErrorContainer,
                    buttonTextStyle: CustomTextStyles.titleSmallPoppinsOnError,
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  CustomElevatedButton(
                    height: 45.v,
                    width: MediaQuery.of(context).size.width * 0.35,
                    text: "lbl_apply".tr,
                    margin: EdgeInsets.only(left: 16.h),
                    buttonStyle: CustomButtonStyles.fillPrimaryTL8,
                    buttonTextStyle:
                        CustomTextStyles.titleSmallPoppinsOnErrorContainer,
                    onPressed: () {
                      print(
                          "controller.filterFromDate.value: ${controller.filterFromDate.value}");
                      print(
                          "controller.filterToDate.value: ${controller.filterToDate.value}");
                      print(
                          "controller.currentPaymentTypes.value: ${controller.currentPaymentTypes.value}");
                      print(
                          "controller.currentPaymentStatuses.value: ${controller.currentPaymentStatuses.value}");
                      controller.filterPaymentsByDateRange(
                          controller.filterFromDate.value,
                          controller.filterToDate.value,
                          controller.currentPaymentTypes.value,
                          controller.currentPaymentStatuses.value);
                      Get.back();
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget filterPaymentByType() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          FilterOption(
            text: "All",
            isSelected:
                controller.currentPaymentTypes.contains(PaymentFilterType.all)
                    ? true
                    : false,
            onTap: () {
              if (controller.currentPaymentTypes
                  .contains(PaymentFilterType.all)) {
                controller.currentPaymentTypes.remove(PaymentFilterType.all);
              } else {
                controller.currentPaymentTypes.add(PaymentFilterType.all);
              }

              if (controller.currentPaymentTypes
                  .contains(PaymentFilterType.cash)) {
                controller.currentPaymentTypes.remove(PaymentFilterType.cash);
              }

              if (controller.currentPaymentTypes
                  .contains(PaymentFilterType.cheque)) {
                controller.currentPaymentTypes.remove(PaymentFilterType.cheque);
              }

              if (controller.currentPaymentTypes
                  .contains(PaymentFilterType.netbanking)) {
                controller.currentPaymentTypes
                    .remove(PaymentFilterType.netbanking);
              }

              print(
                  "controller.currentPaymentTypes: ${controller.currentPaymentTypes}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Cash",
            isSelected:
                controller.currentPaymentTypes.contains(PaymentFilterType.cash)
                    ? true
                    : false,
            onTap: () {
              if (controller.currentPaymentTypes
                  .contains(PaymentFilterType.cash)) {
                controller.currentPaymentTypes.remove(PaymentFilterType.cash);
              } else {
                controller.currentPaymentTypes.add(PaymentFilterType.cash);
              }

              if (controller.currentPaymentTypes
                  .contains(PaymentFilterType.all)) {
                controller.currentPaymentTypes.remove(PaymentFilterType.all);
              }

              print(
                  "controller.currentPaymentTypes: ${controller.currentPaymentTypes}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Cheque",
            isSelected: controller.currentPaymentTypes
                    .contains(PaymentFilterType.cheque)
                ? true
                : false,
            onTap: () {
              if (controller.currentPaymentTypes
                  .contains(PaymentFilterType.cheque)) {
                controller.currentPaymentTypes.remove(PaymentFilterType.cheque);
              } else {
                controller.currentPaymentTypes.add(PaymentFilterType.cheque);
              }

              if (controller.currentPaymentTypes
                  .contains(PaymentFilterType.all)) {
                controller.currentPaymentTypes.remove(PaymentFilterType.all);
              }

              print(
                  "controller.currentPaymentTypes: ${controller.currentPaymentTypes}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Net Banking",
            isSelected: controller.currentPaymentTypes
                    .contains(PaymentFilterType.netbanking)
                ? true
                : false,
            onTap: () {
              if (controller.currentPaymentTypes
                  .contains(PaymentFilterType.netbanking)) {
                controller.currentPaymentTypes
                    .remove(PaymentFilterType.netbanking);
              } else {
                controller.currentPaymentTypes
                    .add(PaymentFilterType.netbanking);
              }

              if (controller.currentPaymentTypes
                  .contains(PaymentFilterType.all)) {
                controller.currentPaymentTypes.remove(PaymentFilterType.all);
              }

              print(
                  "controller.currentPaymentTypes: ${controller.currentPaymentTypes}");
            },
          ),
        ],
      ),
    );
  }

  Widget filterPaymentByStatus() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          FilterOption(
            text: "All",
            isSelected:
                controller.currentPaymentStatuses.contains(PaymentStatus.all)
                    ? true
                    : false,
            onTap: () {
              if (controller.currentPaymentStatuses
                  .contains(PaymentStatus.all)) {
                controller.currentPaymentStatuses.remove(PaymentStatus.all);
              } else {
                controller.currentPaymentStatuses.add(PaymentStatus.all);
              }

              if (controller.currentPaymentStatuses
                  .contains(PaymentStatus.pending)) {
                controller.currentPaymentStatuses.remove(PaymentStatus.pending);
              }

              if (controller.currentPaymentStatuses
                  .contains(PaymentStatus.approved)) {
                controller.currentPaymentStatuses
                    .remove(PaymentStatus.approved);
              }

              if (controller.currentPaymentStatuses
                  .contains(PaymentStatus.rejected)) {
                controller.currentPaymentStatuses
                    .remove(PaymentStatus.rejected);
              }

              print(
                  "controller.currentPaymentStatuses: ${controller.currentPaymentStatuses}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Pending",
            isSelected: controller.currentPaymentStatuses
                    .contains(PaymentStatus.pending)
                ? true
                : false,
            onTap: () {
              if (controller.currentPaymentStatuses
                  .contains(PaymentStatus.pending)) {
                controller.currentPaymentStatuses.remove(PaymentStatus.pending);
              } else {
                controller.currentPaymentStatuses.add(PaymentStatus.pending);
              }

              if (controller.currentPaymentStatuses
                  .contains(PaymentStatus.all)) {
                controller.currentPaymentStatuses.remove(PaymentStatus.all);
              }

              print(
                  "controller.currentPaymentStatuses: ${controller.currentPaymentStatuses}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Approved",
            isSelected: controller.currentPaymentStatuses
                    .contains(PaymentStatus.approved)
                ? true
                : false,
            onTap: () {
              if (controller.currentPaymentStatuses
                  .contains(PaymentStatus.approved)) {
                controller.currentPaymentStatuses
                    .remove(PaymentStatus.approved);
              } else {
                controller.currentPaymentStatuses.add(PaymentStatus.approved);
              }

              if (controller.currentPaymentStatuses
                  .contains(PaymentStatus.all)) {
                controller.currentPaymentStatuses.remove(PaymentStatus.all);
              }

              print(
                  "controller.currentPaymentStatuses: ${controller.currentPaymentStatuses}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Rejected",
            isSelected: controller.currentPaymentStatuses
                    .contains(PaymentStatus.rejected)
                ? true
                : false,
            onTap: () {
              if (controller.currentPaymentStatuses
                  .contains(PaymentStatus.rejected)) {
                controller.currentPaymentStatuses
                    .remove(PaymentStatus.rejected);
              } else {
                controller.currentPaymentStatuses.add(PaymentStatus.rejected);
              }

              if (controller.currentPaymentStatuses
                  .contains(PaymentStatus.all)) {
                controller.currentPaymentStatuses.remove(PaymentStatus.all);
              }

              print(
                  "controller.currentPaymentStatuses: ${controller.currentPaymentStatuses}");
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDate(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          "lbl_date".tr,
          style: CustomTextStyles.titleLargeGray900,
        ),
        SizedBox(height: 9.v),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                  builder: (context, child) {
                    return Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: ColorScheme.light(
                          primary: theme.colorScheme.primary,
                        ),
                      ),
                      child: child!,
                    );
                  },
                ).then((value) {
                  if (value != null) {
                    print("fromdate value: $value");
                    String dateValue = value.toString();
                    controller.formattedFromDate.value =
                        controller.formatDateTime(dateValue);
                    print(controller.formattedFromDate.value);
                    controller.filterFromDate.value =
                        controller.formatFilterDateTime(dateValue);
                    print(controller.formattedFromDate.value);
                    controller.fromDate.value = value;
                  }
                });
              },
              child: Obx(
                () => Container(
                  // width: 115.h,
                  decoration: AppDecoration.outlineBlack90013,
                  child: Row(
                    children: [
                      _buildDateInputColored(
                        dateText: controller.formattedFromDate.value,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                  builder: (context, child) {
                    return Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: ColorScheme.light(
                          primary: theme.colorScheme.primary,
                        ),
                      ),
                      child: child!,
                    );
                  },
                ).then((value) {
                  if (value != null) {
                    print("todate value: $value");
                    String dateValue = value.toString();
                    controller.formattedToDate.value =
                        controller.formatDateTime(dateValue);
                    print(controller.formattedToDate.value);
                    controller.filterToDate.value =
                        controller.formatFilterDateTime(dateValue);
                    print(controller.filterToDate.value);
                    controller.toDate.value = value;
                  }
                });
              },
              child: Obx(
                () => Container(
                  // width: 115.h,
                  decoration: AppDecoration.outlineBlack90013,
                  child: Row(
                    children: [
                      _buildDateInputColored(
                        dateText: controller.formattedToDate.value,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateInputColored({required String dateText}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 14.h,
        vertical: 9.v,
      ),
      decoration: AppDecoration.outlineBlack90014.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder10,
      ),
      child: Row(
        children: [
          CustomImageView(
            svgPath: ImageConstant.imgCalendarPrimary,
            height: 13.v,
            width: 12.h,
            margin: EdgeInsets.symmetric(vertical: 3.v),
          ),
          Padding(
            padding: EdgeInsets.only(
              left: 10.h,
              top: 2.v,
            ),
            child: Text(
              dateText,
              style: CustomTextStyles.bodySmallOpenSansGray700.copyWith(
                color: appTheme.gray700,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CustomPaymentListItem extends StatelessWidget {
  final Payment payment;
  final ProductController controller = Get.put(ProductController());

  CustomPaymentListItem({
    required this.payment,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        print("Payment tapped");
        SharedPrefManager.instance
            .setString(ConstantValues.customerCode, "${payment.code}");
        Get.toNamed(AppRoutes.viewPaymentScreen);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 9.h,
          vertical: 6.v,
        ),
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 26.adaptSize,
              height: 26.adaptSize,
              margin: EdgeInsets.only(
                top: 1.v,
                // bottom: 29.v,
              ),
              decoration: BoxDecoration(
                color: Colors.amber,
                borderRadius: BorderRadius.circular(13),
              ),
              child: Center(
                child: Text(
                  '${controller.fetchCustomerName(payment.cmCode!)}'
                      .substring(0, 1)
                      .toUpperCase(),
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
            SizedBox(width: 12),
            Row(
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(right: 4.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                              "${controller.fetchCustomerName(payment.cmCode!)}",
                              style:
                                  CustomTextStyles.labelLargeOpenSansff0b2e40),
                        ],
                      ),
                    ),
                    Row(
                      children: [
                        Text(
                          "${controller.fetchPaymentTypeName(payment.type!)}",
                          style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                        ),
                      ],
                    ),
                    // 0-pending, 1-conform, 2-rejected, 3-partial approved
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: "Amount : ",
                            style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                          ),
                          TextSpan(
                            text: '₹${payment.amount.toString()}',
                            style: CustomTextStyles.labelLargeOpenSansff8e8e8e,
                          ),
                        ],
                      ),
                      textAlign: TextAlign.left,
                    ),
                    Text(
                      payment.status == 0
                          ? "Pending"
                          : payment.status == 1
                              ? "Comfirm"
                              : payment.status == 2
                                  ? "Rejected"
                                  : "Partial Approved",
                      style: CustomTextStyles.labelLargeOpenSansBluegray400
                          .copyWith(
                              color: payment.status == 0
                                  ? Colors.orange
                                  : payment.status == 1
                                      ? Colors.green
                                      : payment.status == 2
                                          ? Colors.red
                                          : Colors.blue),
                    ),
                  ],
                ),
              ],
            ),
            Spacer(),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  margin: EdgeInsets.only(
                    top: 4.v,
                  ),
                  child: Text(
                    controller.formatDate(payment.updatedAt ?? ""),
                    // controller.formatDate(expense.date),
                    style: CustomTextStyles.bodySmallOpenSansGray500,
                  ),
                ),
                SizedBox(height: 8),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Visibility(
                      visible: payment.syncStatus == 0 ? true : false,
                      child: CustomImageView(
                        svgPath: ImageConstant.imgNotSynced,
                        color: Colors.red,
                        height: 15.adaptSize,
                        width: 15.adaptSize,
                        margin: EdgeInsets.only(bottom: 2.v),
                      ),
                    ),
                    SizedBox(width: 4),
                    CustomImageView(
                      svgPath: ImageConstant.imgArrowRight,
                      // color: Colors.black,
                      height: 15.adaptSize,
                      width: 15.adaptSize,
                      margin: EdgeInsets.only(bottom: 2.v),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
