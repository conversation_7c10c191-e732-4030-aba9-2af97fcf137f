// ignore_for_file: invalid_use_of_protected_member, must_be_immutable

import 'package:flutter/material.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/filter_option.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/product_screen/controller/product_controller.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/sfmDatabase/models/call_model.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

class HistoryListWidget extends StatelessWidget {
  final ProductController controller = Get.put(ProductController());

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 8.v),
        CustomSearchBarHistory(),
        SizedBox(height: 4.v),
        _buildOrderHistoryList(),
        SizedBox(height: 10.v),
        _buildVIEWCART(),
      ],
    );
  }

  Widget _buildOrderHistoryList() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.h, vertical: 0),
        child: Obx(
          () {
            if (controller.isLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            } else if (controller.filteredCallList.isEmpty) {
              return Center(
                child: Text(
                  'No orders found',
                  style: TextStyle(fontSize: 16),
                ),
              );
            } else {
              return ListView.builder(
                physics: ClampingScrollPhysics(),
                itemCount: controller.filteredCallList.length,
                itemBuilder: (context, index) => CustomOrderListItem(
                  order: controller.filteredCallList[index],
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildVIEWCART() {
    return Obx(
      () => Container(
        margin: EdgeInsets.only(
          left: 0.h,
          right: 0.h,
          bottom: 0.v,
        ),
        decoration: AppDecoration.outlineBlack9007.copyWith(
          borderRadius: BorderRadiusStyle.customBorderTL10,
        ),
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                height: 50.v,
                width: double.maxFinite,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(10.h),
                  ),
                ),
              ),
            ),
            Align(
              alignment: Alignment.center,
              child: Container(
                width: double.maxFinite,
                padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                decoration: AppDecoration.outlineBlack9007,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Grand Total",
                          style: CustomTextStyles.titleSmallOnErrorContainerBold
                              .copyWith(
                            color: theme.colorScheme.onErrorContainer
                                .withValues(alpha: 1),
                          ),
                        ),
                        Text(
                          '₹${controller.grandTotalAmount.value.toStringAsFixed(2)}',
                          style: CustomTextStyles.titleSmallOnErrorContainerBold
                              .copyWith(
                            color: theme.colorScheme.onErrorContainer
                                .withValues(alpha: 1),
                          ),
                        ),
                      ],
                    ),
                    Spacer(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Total Orders",
                          style: CustomTextStyles.titleSmallOnErrorContainerBold
                              .copyWith(
                            color: theme.colorScheme.onErrorContainer
                                .withValues(alpha: 1),
                          ),
                        ),
                        Text(
                          '${controller.filteredCallList.length}',
                          style: CustomTextStyles.titleSmallOnErrorContainerBold
                              .copyWith(
                            color: theme.colorScheme.onErrorContainer
                                .withValues(alpha: 1),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomOrderListItem extends StatelessWidget {
  final CallData order;
  final ProductController controller = Get.put(ProductController());

  CustomOrderListItem({
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          if (order.callOrderType == 1) {
            print("order is productive");
            SharedPrefManager.instance
                .setString(ConstantValues.callCode, order.callCode);
            Get.toNamed(AppRoutes.orderHistoryDetailScreen);
          } else {
            print("order is non-productive");
          }
        },
        child: Padding(
          padding: const EdgeInsets.only(top: 4, bottom: 4),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: 7.h,
              vertical: 3.v,
            ),
            decoration: AppDecoration.outlineGray.copyWith(
              borderRadius: BorderRadiusStyle.roundedBorder10,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(left: 4.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                "${controller.getCustomerNameFromPartyCode(order.callClientCode ?? "")}",
                                textAlign: TextAlign.start,
                                style: CustomTextStyles
                                    .titleMediumBluegray900Bold_1,
                                maxLines: 10,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.only(bottom: 3.v),
                              child: Text(
                                controller
                                    .formatDate(order.callCreatedAt ?? ""),
                                style:
                                    CustomTextStyles.bodySmallOpenSansGray500,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 2.v),
                        Text(
                          order.callOrderType == 1
                              ? "Productive"
                              : "Non-Productive",
                          style: CustomTextStyles.titleSmallBluegray400,
                        ),
                        SizedBox(height: 4.v),
                        SizedBox(
                          width: 274.h,
                          child: Row(
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    "Total Qty",
                                    style: CustomTextStyles
                                        .labelLargeOpenSansBluegray40013,
                                  ),
                                  Text(
                                    "${order.callTotalQTY ?? 0}",
                                    style: CustomTextStyles
                                        .titleSmallBluegray900Bold,
                                  ),
                                ],
                              ),
                              Padding(
                                padding: EdgeInsets.only(left: 23.h),
                                child: SizedBox(
                                  height: 39.v,
                                  child: VerticalDivider(
                                    width: 1.h,
                                    thickness: 1.v,
                                    color: appTheme.black900
                                        .withValues(alpha: 0.2),
                                    indent: 3.h,
                                    endIndent: 6.h,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(left: 27.h),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      "Grand Total",
                                      style: CustomTextStyles
                                          .labelLargeOpenSansBluegray40013,
                                    ),
                                    Text(
                                      "₹ ${order.callGrandTotal!.toStringAsFixed(2)}",
                                      style: CustomTextStyles
                                          .titleSmallBluegray900Bold,
                                    ),
                                  ],
                                ),
                              ),
                              Spacer(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Row(
                  children: [
                    Visibility(
                      visible: order.callSyncStatus == 0 ? true : false,
                      child: CustomImageView(
                        svgPath: ImageConstant.imgNotSynced,
                        color: Colors.red,
                        height: 15.adaptSize,
                        width: 15.adaptSize,
                        margin: EdgeInsets.only(bottom: 2.v),
                      ),
                    ),
                    SizedBox(width: 6),
                    Visibility(
                      visible: order.callOrderType == 1 ? true : false,
                      child: CustomImageView(
                        svgPath: ImageConstant.imgArrowRight,
                        height: 15.adaptSize,
                        width: 15.adaptSize,
                        margin: EdgeInsets.only(bottom: 2.v),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
  }
}

class CustomSearchBarHistory extends StatelessWidget {
  ProductController controller = Get.put(ProductController());

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 12, right: 12, top: 4, bottom: 4),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.5),
              spreadRadius: 2,
              blurRadius: 7,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                style: CustomTextStyles.bodyMediumBluegray700,
                onChanged: (value) {
                  print('Search value: $value');
                },
                decoration: InputDecoration(
                  hintText: 'Search Orders',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 10.0),
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(10.0),
                  bottomRight: Radius.circular(10.0),
                ),
              ),
              child: IconButton(
                icon: Container(
                  child: CustomImageView(
                    svgPath: ImageConstant.imgFilterIcon,
                    height: 20.adaptSize,
                    width: 20.adaptSize,
                  ),
                ),
                onPressed: () {
                  print('Filter button pressed');
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return CustomDialogFilter();
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomDialogFilter extends StatelessWidget {
  CustomDialogFilter({
    Key? key,
  }) : super(key: key);

  ProductController controller = Get.put(ProductController());

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      insetPadding: EdgeInsets.all(12),
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Days",
              style: CustomTextStyles.titleLargeGray900,
            ),
            SizedBox(height: 8.v),
            filterOrdersByDays(),
            SizedBox(height: 16.v),
            Obx(
              () => Visibility(
                visible: controller.currentOrderDays.contains(OrderDays.date)
                    ? true
                    : false,
                child: _buildDate(context),
              ),
            ),
            SizedBox(height: 16.v),
            Container(
              width: MediaQuery.of(context).size.width * 0.75,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomElevatedButton(
                    width: MediaQuery.of(context).size.width * 0.35,
                    text: "lbl_cancel".tr,
                    buttonStyle: CustomButtonStyles.fillOnErrorContainer,
                    buttonTextStyle: CustomTextStyles.titleSmallPoppinsOnError,
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  CustomElevatedButton(
                    width: MediaQuery.of(context).size.width * 0.35,
                    text: "lbl_apply".tr,
                    margin: EdgeInsets.only(left: 16.h),
                    buttonStyle: CustomButtonStyles.fillPrimaryTL8,
                    buttonTextStyle:
                        CustomTextStyles.titleSmallPoppinsOnErrorContainer,
                    onPressed: () {
                      print(
                          "controller.filterFromDate.value: ${controller.filterFromDate.value}");
                      print(
                          "controller.filterToDate.value: ${controller.filterToDate.value}");
                      print(
                          "controller.currentOrderCustType.value: ${controller.currentOrderCustType.value}");
                      print(
                          "controller.currentOrderDays.value: ${controller.currentOrderDays.value}");
                      controller.filterOrders(
                          controller.filterFromDate.value,
                          controller.filterToDate.value,
                          controller.currentOrderCustType.value,
                          controller.currentOrderDays.value);
                      Get.back();
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDate(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "lbl_date".tr,
          style: CustomTextStyles.titleLargeGray900,
        ),
        SizedBox(height: 9.v),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                  builder: (context, child) {
                    return Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: ColorScheme.light(
                          primary: theme.colorScheme.primary,
                        ),
                      ),
                      child: child!,
                    );
                  },
                ).then((value) {
                  if (value != null) {
                    print("fromdate value: $value");
                    String dateValue = value.toString();
                    controller.formattedFromDate.value =
                        controller.formatDateTime(dateValue);
                    print(controller.formattedFromDate.value);
                    controller.filterFromDate.value =
                        controller.formatFilterDateTime(dateValue);
                    print(controller.formattedFromDate.value);
                    controller.fromDate.value = value;
                  }
                });
              },
              child: Obx(
                () => Container(
                  width: 115.h,
                  decoration: AppDecoration.outlineBlack90013,
                  child: Row(
                    children: [
                      _buildDateInputColored(
                        dateText: controller.formattedFromDate.value,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 8.h,
                right: 8.h,
              ),
              child: Text(
                "to",
                style: CustomTextStyles.bodyMediumOpenSansBlack900,
              ),
            ),
            GestureDetector(
              onTap: () {
                showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                  builder: (context, child) {
                    return Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: ColorScheme.light(
                          primary: theme.colorScheme.primary,
                        ),
                      ),
                      child: child!,
                    );
                  },
                ).then((value) {
                  if (value != null) {
                    print("todate value: $value");
                    String dateValue = value.toString();
                    controller.formattedToDate.value =
                        controller.formatDateTime(dateValue);
                    print(controller.formattedToDate.value);
                    controller.filterToDate.value =
                        controller.formatFilterDateTime(dateValue);
                    print(controller.filterToDate.value);
                    controller.toDate.value = value;
                  }
                });
              },
              child: Obx(
                () => Container(
                  width: 115.h,
                  decoration: AppDecoration.outlineBlack90013,
                  child: Row(
                    children: [
                      _buildDateInputColored(
                        dateText: controller.formattedToDate.value,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateInputColored({required String dateText}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 14.h,
        vertical: 9.v,
      ),
      decoration: AppDecoration.outlineBlack90014.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder10,
      ),
      child: Row(
        children: [
          CustomImageView(
            svgPath: ImageConstant.imgCalendarPrimary,
            height: 13.v,
            width: 12.h,
            margin: EdgeInsets.symmetric(vertical: 3.v),
          ),
          Padding(
            padding: EdgeInsets.only(
              left: 10.h,
              top: 2.v,
            ),
            child: Text(
              dateText,
              style: CustomTextStyles.bodySmallOpenSansGray700.copyWith(
                color: appTheme.gray700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget filterOrdersByDays() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  FilterOption(
                    text: "All",
                    isSelected:
                        controller.currentOrderDays.contains(OrderDays.all)
                            ? true
                            : false,
                    onTap: () {
                      controller.currentOrderDays.value = {OrderDays.all};
                    },
                  ),
                  SizedBox(width: 8),
                  FilterOption(
                    text: "Today",
                    isSelected:
                        controller.currentOrderDays.contains(OrderDays.today)
                            ? true
                            : false,
                    onTap: () {
                      controller.currentOrderDays.value = {OrderDays.today};
                    },
                  ),
                  SizedBox(width: 8),
                  FilterOption(
                    text: "Last 7 Days",
                    isSelected: controller.currentOrderDays
                            .contains(OrderDays.last7days)
                        ? true
                        : false,
                    onTap: () {
                      controller.currentOrderDays.value = {OrderDays.last7days};
                    },
                  ),
                  SizedBox(width: 8),
                  FilterOption(
                    text: "Last 30 Days",
                    isSelected: controller.currentOrderDays
                            .contains(OrderDays.last30days)
                        ? true
                        : false,
                    onTap: () {
                      controller.currentOrderDays.value = {
                        OrderDays.last30days
                      };
                    },
                  ),
                ],
              ),
              SizedBox(height: 8),
              FilterOption(
                text: "Date",
                isSelected: controller.currentOrderDays.contains(OrderDays.date)
                    ? true
                    : false,
                onTap: () {
                  controller.currentOrderDays.value = {OrderDays.date};
                  if (controller.currentOrderDays.contains(OrderDays.date)) {
                    print("Value is true");
                  } else {
                    print("Value is false");
                  }
                  print(
                      "controller.currentOrderDays.value: ${controller.currentOrderDays.value}");
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
