import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/presentation/product_screen/controller/product_controller.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/presentation/product_screen/models/product_model.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

class ProductListWidget extends StatelessWidget {
  final ProductController controller = Get.put(ProductController());

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CustomSearchBar(),
        SizedBox(height: 10.v),
        Obx(
          () => Visibility(
            visible: controller.filteredProductList.isNotEmpty ? true : false,
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    left: 16.h,
                    right: 16.h,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(bottom: 4.v),
                        child: Text(
                          "lbl_products_list".tr,
                          style: CustomTextStyles.titleSmallLatoPrimary,
                        ),
                      ),
                      Spacer(),
                      GestureDetector(
                        onTap: () {
                          controller.isOrderProductive.value = false;

                          if (controller.isOldVersion.value == 1) {
                            showToastMessage(
                                "Please use latest app version to \nplace an order");
                          } else if (controller.checkinType.value == 3) {
                            showToastMessage(
                                "You can't place an order \nbecause you are on other work today");
                          } else if (controller.checkinType.value == 4) {
                            showToastMessage(
                                "You can't place an order \nbecause you are absent today");
                          } else {
                            Get.toNamed(AppRoutes.noOrderScreen);
                          }
                        },
                        child: Padding(
                          padding: EdgeInsets.only(bottom: 4.v),
                          child: Text(
                            "lbl_no_order".tr,
                            style: CustomTextStyles.titleSmallLatoPrimary,
                          ),
                        ),
                      ),
                      CustomImageView(
                        svgPath: ImageConstant.imgIcon,
                        height: 10.v,
                        width: 5.h,
                        margin: EdgeInsets.only(
                          left: 6.h,
                          // top: 7.v,
                          bottom: 7.v,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(height: 8.v),
        _buildProductList(),
        SizedBox(height: 10.v),
        SizedBox(
          height: 70,
          child: _addOrderBottomView(),
        ),
      ],
    );
  }

  Widget _buildProductList() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.h),
        child: Obx(
          () {
            if (controller.isLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            } else if (controller.filteredProductList.isEmpty) {
              return Center(
                child: Text(
                  'No products found',
                  style: TextStyle(fontSize: 16),
                ),
              );
            } else {
              return ListView.builder(
                physics: ClampingScrollPhysics(),
                itemCount: controller.filteredProductList.length,
                itemBuilder: (context, index) => CustomProductListItem(
                  product: controller.filteredProductList[index],
                  index: index,
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Widget _addOrderBottomView() {
    return Obx(
      () => Align(
        alignment: Alignment.center,
        child: Container(
          width: double.maxFinite,
          margin: EdgeInsets.only(
            top: 2.v,
            bottom: 0.v,
          ),
          padding: EdgeInsets.only(
            left: 16.h,
            right: 12.h,
            top: 10.v,
            bottom: 8.v,
          ),
          decoration: AppDecoration.outlineBlack9007.copyWith(
            borderRadius: BorderRadiusStyle.customBorderTL10,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "lbl_total_products".tr,
                    style: CustomTextStyles.labelLargeOpenSansOnErrorContainer,
                  ),
                  SizedBox(height: 1.v),
                  Text(
                    controller.totalQty.value.toString(),
                    style: CustomTextStyles.titleSmallOnErrorContainerBold,
                  ),
                ],
              ),
              Spacer(),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  FocusManager.instance.primaryFocus?.unfocus();
                  controller.isOrderProductive.value = true;
                  FocusManager.instance.primaryFocus?.unfocus();
                  if (controller.cartList.isEmpty) {
                    showToastMessage("Please add products to continue");
                  } else {
                    Get.toNamed(AppRoutes.showOrderScreen);
                  }
                },
                child: Container(
                  height: 50.0,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25.0),
                  ),
                  padding: EdgeInsets.only(
                      left: 15.h, right: 8.h, bottom: 8.v, top: 8.v),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Icon(
                        Icons.shopping_cart,
                        color: theme.colorScheme.primary,
                      ),
                      Text(
                        "lbl_add_order".tr,
                        style: CustomTextStyles.titleSmallPrimaryBold,
                      ),
                      CustomImageView(
                        svgPath: ImageConstant.imgGroupGray50,
                        height: 4.adaptSize,
                        width: 4.adaptSize,
                        margin: EdgeInsets.only(left: 3.h),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomProductListItem extends StatelessWidget {
  final AddProduct product;
  final int index;
  final ProductController controller = Get.put(ProductController());

  CustomProductListItem({
    required this.product,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 4, bottom: 4),
      child: Obx(
        () => Container(
          decoration: AppDecoration.outlineGray.copyWith(
            borderRadius: BorderRadiusStyle.roundedBorder20,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      // height: 120.v,
                      width: 80.h,
                      margin: EdgeInsets.only(top: 10.v),
                      child: Stack(
                        alignment: Alignment.topCenter,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey.withValues(alpha: 0.25),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(20),
                              child: CachedNetworkImage(
                                imageUrl: product.productImage != null &&
                                        product.productImage!.isNotEmpty
                                    ? "${ApiClient.imgProductBaseURL}${product.productImage}"
                                    : "",
                                height: 100.v,
                                width: 90.h,
                                alignment: Alignment.center,
                                placeholder: (context, url) =>
                                    Image.asset(ImageConstant.imgSplash),
                                errorWidget: (context, url, error) =>
                                    Image.asset(ImageConstant.imgSplash),
                              ),
                            ),
                          ),
                          CustomElevatedButton(
                            height: 28.v,
                            width: 90.h,
                            text: product.grandTotal! > 0
                                ? "Total:${product.grandTotal!.value.toStringAsFixed(2)}"
                                : "Total:0",
                            buttonStyle: CustomButtonStyles.outlineBlackTL20,
                            buttonTextStyle:
                                CustomTextStyles.labelLargeLatoOnErrorContainer,
                            alignment: Alignment.topCenter,
                          ),
                        ],
                      ),
                    ),
                    Flexible(
                      flex: 0,
                      child: Padding(
                        padding: EdgeInsets.only(left: 5.h, top: 5),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "${product.productName}",
                              style: CustomTextStyles.titleSmallBluegray90015_1,
                            ),
                            SizedBox(height: 2.v),
                            Text(
                              "${controller.fetchTypeName(product.pmPtId!)}",
                              style: CustomTextStyles.titleSmallBluegray90015_1,
                            ),
                            SizedBox(height: 2.v),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "lbl_unit_size".tr,
                                          style: CustomTextStyles
                                              .bodyMediumGray6000214_1,
                                        ),
                                        SizedBox(height: 5),
                                        Text(
                                          "MRP",
                                          style: CustomTextStyles
                                              .bodyMediumGray6000214_1,
                                        ),
                                        SizedBox(height: 5),
                                        if (product.innerCaseSize != 0)
                                          Text(
                                            "lbl_innercase".tr,
                                            style: CustomTextStyles
                                                .bodyMediumGray6000214_1,
                                          ),
                                      ],
                                    ),
                                    SizedBox(width: 3),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          ":",
                                          style: CustomTextStyles
                                              .bodyMediumGray6000214_1,
                                        ),
                                        SizedBox(height: 5),
                                        Text(
                                          ":",
                                          style: CustomTextStyles
                                              .bodyMediumGray6000214_1,
                                        ),
                                        SizedBox(height: 5),
                                        if (product.innerCaseSize != 0)
                                          Text(
                                            ":",
                                            style: CustomTextStyles
                                                .bodyMediumGray6000214_1,
                                          ),
                                      ],
                                    ),
                                    SizedBox(width: 3),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "${product.unitSize} ${product.uomType}",
                                          style: CustomTextStyles
                                              .bodyMediumGray6000214_1,
                                        ),
                                        SizedBox(height: 5),
                                        Text(
                                          "${product.productMrp}",
                                          style: CustomTextStyles
                                              .bodyMediumGray6000214_1,
                                        ),
                                        SizedBox(height: 5),
                                        if (product.innerCaseSize != 0)
                                          Text(
                                            product.innerCaseSize == null
                                                ? '0'
                                                : '${product.innerCaseSize}',
                                            style: CustomTextStyles
                                                .bodyMediumGray6000214_1,
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                                SizedBox(width: 3),
                                Padding(
                                  padding: EdgeInsets.only(top: 4),
                                  child: Container(
                                    width: 1,
                                    height: 50,
                                    color: Colors.grey,
                                  ),
                                ),
                                SizedBox(width: 3),
                                Row(
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Barcode",
                                          style: CustomTextStyles
                                              .bodyMediumGray6000214_1,
                                        ),
                                        SizedBox(height: 5),
                                        // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer
                                        Text(
                                          controller.selectedCustType == 2
                                              ? "PTS"
                                              : controller.selectedCustType ==
                                                          3 ||
                                                      controller
                                                              .selectedCustType ==
                                                          4
                                                  ? "Price"
                                                  : "PTR",
                                          style: CustomTextStyles
                                              .bodyMediumGray6000214_1,
                                        ),
                                        SizedBox(height: 5),
                                        if (product.outerCaseSize != 0)
                                          Text(
                                            "lbl_outercase".tr,
                                            style: CustomTextStyles
                                                .bodyMediumGray6000214_1,
                                          ),
                                      ],
                                    ),
                                    SizedBox(width: 3),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          ":",
                                          style: CustomTextStyles
                                              .bodyMediumGray6000214_1,
                                        ),
                                        SizedBox(height: 5),
                                        Text(
                                          ":",
                                          style: CustomTextStyles
                                              .bodyMediumGray6000214_1,
                                        ),
                                        SizedBox(height: 5),
                                        if (product.outerCaseSize != 0)
                                          Text(
                                            ":",
                                            style: CustomTextStyles
                                                .bodyMediumGray6000214_1,
                                          ),
                                      ],
                                    ),
                                    SizedBox(width: 3),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          product.barcode == null ||
                                                  product.barcode == ""
                                              ? '0'
                                              : '${product.barcode}',
                                          style: CustomTextStyles
                                              .bodyMediumGray6000214_1,
                                        ),
                                        SizedBox(height: 3),
                                        GestureDetector(
                                          onTap: () {
                                            if (controller.isOldVersion.value ==
                                                1) {
                                              showToastMessage(
                                                  "Please use latest app version to \nplace an order");
                                            } else if (controller.checkinType.value ==
                                                3) {
                                              showToastMessage(
                                                  "You can't place an order \nbecause you are on other work today");
                                            } else if (controller
                                                    .checkinType.value ==
                                                4) {
                                              showToastMessage(
                                                  "You can't place an order \nbecause you are absent today");
                                            }
                                          },
                                          child: CustomProductTextField(
                                            controller: controller
                                                .ptsValueControllers[index],
                                            borderRadius:
                                                BorderRadius.circular(0),
                                            hintText: "0",
                                            height: 20,
                                            width: 60,
                                            isEnabled:
                                                !(controller.checkinType == 3 ||
                                                    controller.checkinType ==
                                                        4),
                                            onChanged: (newValue) {
                                              print(
                                                  'New value pts data: $newValue');
                                              double intValue =
                                                  double.tryParse(newValue) ??
                                                      0;
                                              print(
                                                  "intValue pts data: ${intValue}");

                                              if (controller.selectedCustType ==
                                                  2) {
                                                product.productPtss!.value =
                                                    intValue;
                                                controller.addToCart(product);
                                                // }
                                              } else if (controller
                                                      .selectedCustType ==
                                                  3) {
                                                product.productPtdistributor!
                                                    .value = intValue;
                                                controller.addToCart(product);
                                              } else if (controller
                                                      .selectedCustType ==
                                                  5) {
                                                product.productPtretailer!
                                                    .value = intValue;
                                                controller.addToCart(product);
                                                // }
                                              } else if (controller
                                                      .selectedCustType ==
                                                  4) {
                                                product.productPtdealer!.value =
                                                    intValue;
                                                controller.addToCart(product);
                                              }
                                            },
                                            enableInteractiveSelection: false,
                                          ),
                                        ),
                                        SizedBox(height: 3),
                                        if (product.outerCaseSize != 0)
                                          Text(
                                            product.outerCaseSize == null
                                                ? '0'
                                                : '${product.outerCaseSize}',
                                            style: CustomTextStyles
                                                .bodyMediumGray6000214_1,
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 4.v),
              Container(
                padding: EdgeInsets.symmetric(vertical: 4.v),
                decoration: AppDecoration.fillPrimaryContainer.copyWith(
                  borderRadius: BorderRadiusStyle.roundedBorder20,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(bottom: 0.v),
                      child: Column(
                        children: [
                          Obx(
                            () => Container(
                              width: MediaQuery.of(context).size.width * 0.85,
                              margin: EdgeInsets.only(
                                left: 8.h,
                                right: 8.h,
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Column(
                                    children: [
                                      Text(
                                        "lbl_pcs".tr,
                                        style: CustomTextStyles
                                            .labelLargeOpenSansBluegray900Bold,
                                      ),
                                      SizedBox(height: 2.v),
                                      GestureDetector(
                                        onTap: () {
                                          if (controller.isOldVersion.value ==
                                              1) {
                                            showToastMessage(
                                                "Please use latest app version to \nplace an order");
                                          } else if (controller.checkinType.value ==
                                              3) {
                                            showToastMessage(
                                                "You can't place an order \nbecause you are on other work today");
                                          } else if (controller
                                                  .checkinType.value ==
                                              4) {
                                            showToastMessage(
                                                "You can't place an order \nbecause you are absent today");
                                          }
                                        },
                                        child: CustomProductTextField(
                                          controller: controller
                                              .pcsValueControllers[index],
                                          borderRadius:
                                              BorderRadius.circular(0),
                                          hintText: "0",
                                          height: 22,
                                          width: 40,
                                          isEnabled:
                                              !(controller.checkinType == 3 ||
                                                  controller.checkinType == 4),
                                          enableInteractiveSelection: false,
                                          onChanged: (newValue) {
                                            print('Pcs value: $newValue');
                                            product.pcs!.value =
                                                int.tryParse(newValue) ?? 0;
                                            controller.addToCart(product);
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(width: 8),
                                  if (product.innerCaseSize != 0)
                                    Column(
                                      children: [
                                        Text(
                                          "lbl_bunch".tr,
                                          style: CustomTextStyles
                                              .labelLargeOpenSansBluegray900Bold,
                                        ),
                                        SizedBox(height: 2.v),
                                        GestureDetector(
                                          onTap: () {
                                            if (controller.isOldVersion.value ==
                                                1) {
                                              showToastMessage(
                                                  "Please use latest app version to \nplace an order");
                                            } else if (controller.checkinType.value ==
                                                3) {
                                              showToastMessage(
                                                  "You can't place an order \nbecause you are on other work today");
                                            } else if (controller
                                                    .checkinType.value ==
                                                4) {
                                              showToastMessage(
                                                  "You can't place an order \nbecause you are absent today");
                                            }
                                          },
                                          child: CustomProductTextField(
                                            controller: controller
                                                .bunchValueControllers[index],
                                            enableInteractiveSelection: false,
                                            borderRadius:
                                                BorderRadius.circular(0),
                                            hintText: "0",
                                            height: 22,
                                            width: 40,
                                            isEnabled: !((controller
                                                            .checkinType ==
                                                        3 ||
                                                    controller.checkinType ==
                                                        4) ||
                                                product.innerCaseSize == 0),
                                            onChanged: (newValue) {
                                              print(
                                                  'Bunch value product screen: $newValue');
                                              product.bunch!.value =
                                                  int.tryParse(newValue) ?? 0;
                                              controller.addToCart(product);
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  SizedBox(width: 8),
                                  if (product.outerCaseSize != 0)
                                    Column(
                                      children: [
                                        Text(
                                          "lbl_box".tr,
                                          style: CustomTextStyles
                                              .labelLargeOpenSansBluegray900Bold,
                                        ),
                                        SizedBox(height: 2.v),
                                        GestureDetector(
                                          onTap: () {
                                            if (controller.isOldVersion.value ==
                                                1) {
                                              showToastMessage(
                                                  "Please use latest app version to \nplace an order");
                                            } else if (controller.checkinType.value ==
                                                3) {
                                              showToastMessage(
                                                  "You can't place an order \nbecause you are on other work today");
                                            } else if (controller
                                                    .checkinType.value ==
                                                4) {
                                              showToastMessage(
                                                  "You can't place an order \nbecause you are absent today");
                                            }
                                          },
                                          child: CustomProductTextField(
                                            controller: controller
                                                .boxValueControllers[index],
                                            enableInteractiveSelection: false,
                                            borderRadius:
                                                BorderRadius.circular(0),
                                            hintText: "0",
                                            height: 22,
                                            width: 40,
                                            isEnabled: !((controller
                                                            .checkinType ==
                                                        3 ||
                                                    controller.checkinType ==
                                                        4) ||
                                                product.outerCaseSize == 0),
                                            onChanged: (newValue) {
                                              print('Box value: $newValue');

                                              product.box!.value =
                                                  int.tryParse(newValue) ?? 0;
                                              controller.addToCart(product);
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  SizedBox(width: 8),
                                  CustomElevatedButton(
                                    height: 35.v,
                                    width: 90.h,
                                    text: product.grandTotal! > 0
                                        ? "Qty : ${product.quantity!.value}"
                                        : "Qty : 0",
                                    buttonStyle:
                                        CustomButtonStyles.outlineBlueQty,
                                    buttonTextStyle: CustomTextStyles
                                        .labelLargeLatoOnErrorContainer,
                                    alignment: Alignment.topCenter,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: 2.v),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomSearchBar extends StatelessWidget {
  final ProductController controller = Get.put(ProductController());

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 12),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.5),
              spreadRadius: 2,
              blurRadius: 7,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Icon(
                Icons.search,
                color: Colors.grey,
              ),
            ),
            Expanded(
              child: TextField(
                controller: controller.searchController,
                style: CustomTextStyles.bodyMediumBluegray700,
                decoration: InputDecoration(
                  hintText: 'Search',
                  hintStyle: TextStyle(color: Colors.grey),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 10.0),
                ),
                onChanged: (value) {
                  print('Search value: $value');
                  controller.updateSearchText(value);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomProductTextField extends StatelessWidget {
  final TextEditingController controller;
  final bool autoFocus;
  final bool enableInteractiveSelection;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final void Function(String)? onChanged;
  final bool isEnabled;

  CustomProductTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(0.0)),
    this.onChanged,
    this.autoFocus = false,
    this.enableInteractiveSelection = true,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: theme.colorScheme.primary),
      ),
      child: TextFormField(
        maxLines: 1,
        controller: controller,
        style: CustomTextStyles.titleSmallBluegray900,
        textAlign: TextAlign.center,
        textAlignVertical: TextAlignVertical.center,
        keyboardType: TextInputType.numberWithOptions(
          signed: true,
          decimal: true,
        ),
        textInputAction: TextInputAction.done,
        cursorHeight: 14,
        onChanged: onChanged,
        autofocus: autoFocus,
        onEditingComplete: () {
          print("onEditingComplete called");
          FocusManager.instance.primaryFocus?.unfocus();
        },
        enableInteractiveSelection: enableInteractiveSelection,
        decoration: InputDecoration(
          isDense: true,
          hintText: hintText,
          hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
          contentPadding: EdgeInsets.fromLTRB(2.0, 1.0, 2.0, 2.0),
          border: InputBorder.none,
        ),
        enabled: isEnabled,
      ),
    );
  }
}

class CustomProductQTYTextField extends StatelessWidget {
  final TextEditingController controller;
  final bool autoFocus;
  final bool enableInteractiveSelection;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final void Function(String)? onChanged;
  final String? initialValue;
  final bool isEnabled;

  CustomProductQTYTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(0.0)),
    this.onChanged,
    this.initialValue,
    this.autoFocus = false,
    this.enableInteractiveSelection = true,
    this.isEnabled = true,
  }) {
    controller.text = initialValue ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextFormField(
        style: CustomTextStyles.titleSmallBluegray900,
        textAlign: TextAlign.center,
        textAlignVertical: TextAlignVertical.center,
        keyboardType:
            TextInputType.numberWithOptions(signed: true, decimal: true),
        textInputAction: TextInputAction.done,
        cursorHeight: 14,
        onChanged: onChanged,
        autofocus: autoFocus,
        onEditingComplete: () {
          FocusManager.instance.primaryFocus?.unfocus();
        },
        onTap: () {
          FocusScope.of(context).requestFocus();
        },
        enableInteractiveSelection: enableInteractiveSelection,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
          contentPadding: EdgeInsets.only(bottom: 11),
          border: InputBorder.none,
        ),
        enabled: isEnabled,
      ),
    );
  }
}
