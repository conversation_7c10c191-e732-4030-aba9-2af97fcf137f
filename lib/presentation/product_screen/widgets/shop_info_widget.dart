import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/product_screen/controller/product_controller.dart';
import 'package:sfm_new/core/app_export.dart';

class ShopInfoWidget extends StatelessWidget {
  final ProductController controller = Get.put(ProductController());

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 8.v),
        shopDetails(context),
      ],
    );
  }

  Widget shopDetails(BuildContext context) {
    return Obx(
      () => SizedBox(
        height: MediaQuery.of(context).size.height * 0.8,
        child: Container(
          margin: EdgeInsets.only(left: 12, right: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.0),
            border: Border.all(
              color: Colors.grey.shade300,
              width: 1.0,
            ),
          ),
          child: Column(
            children: [
              SizedBox(height: 12.v),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Align(
                      alignment: Alignment.center,
                      child: Container(
                        width: 35.adaptSize,
                        height: 35.adaptSize,
                        decoration: AppDecoration.outlineYellow.copyWith(
                          borderRadius: BorderRadiusStyle.roundedBorder17,
                        ),
                        child: Align(
                          alignment: Alignment.center,
                          child: Text(
                            '${controller.shopInfoList[0].cmName}'
                                .substring(0, 1)
                                .toUpperCase(),
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 6.v),
                    Padding(
                      padding: const EdgeInsets.only(left: 12, right: 12),
                      child: Align(
                        alignment: Alignment.center,
                        child: Text(
                          "${controller.shopInfoList[0].cmName}",
                          style: CustomTextStyles.titleMediumBluegray900Bold,
                        ),
                      ),
                    ),
                    SizedBox(height: 12.v),
                    Padding(
                      padding: EdgeInsets.only(right: 12.h, left: 12.h),
                      child: Container(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (controller.shopInfoList[0].cmType != 5)
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Code",
                                          style: CustomTextStyles
                                              .titleSmallPoppinsGray60002
                                              .copyWith(
                                            color: appTheme.gray60002,
                                          ),
                                        ),
                                        SizedBox(height: 0.v),
                                        Text(
                                          "${controller.shopInfoList[0].cmCode}",
                                          style: CustomTextStyles
                                              .titleSmallBluegray900,
                                        ),
                                        SizedBox(height: 8),
                                      ],
                                    ),
                                  if (controller.shopInfoList[0].cmType == 5 &&
                                      controller.selectedPartyName.value != "")
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Distributor",
                                          style: CustomTextStyles
                                              .titleSmallPoppinsGray60002
                                              .copyWith(
                                            color: appTheme.gray60002,
                                          ),
                                        ),
                                        SizedBox(height: 0.v),
                                        Text(
                                          "${controller.selectedPartyName.value}",
                                          style: CustomTextStyles
                                              .titleSmallBluegray900,
                                        ),
                                        SizedBox(height: 8),
                                      ],
                                    ),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Contact Person",
                                        style: CustomTextStyles
                                            .titleSmallPoppinsGray60002
                                            .copyWith(
                                          color: appTheme.gray60002,
                                        ),
                                      ),
                                      SizedBox(height: 0.v),
                                      Text(
                                        "${controller.shopInfoList[0].cmContactPerson}",
                                        style: CustomTextStyles
                                            .titleSmallBluegray900,
                                      ),
                                      SizedBox(height: 8),
                                    ],
                                  ),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Phone No.",
                                        style: CustomTextStyles
                                            .titleSmallPoppinsGray60002
                                            .copyWith(
                                          color: appTheme.gray60002,
                                        ),
                                      ),
                                      SizedBox(height: 0.v),
                                      Text(
                                        "${controller.shopInfoList[0].cmMobile}",
                                        style: CustomTextStyles
                                            .titleSmallBluegray900,
                                      ),
                                      SizedBox(height: 8),
                                    ],
                                  ),
                                  if (controller
                                      .shopInfoList[0].cmGST!.isNotEmpty)
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "GST No.",
                                          style: CustomTextStyles
                                              .titleSmallPoppinsGray60002
                                              .copyWith(
                                            color: appTheme.gray60002,
                                          ),
                                        ),
                                        SizedBox(height: 0.v),
                                        Text(
                                          "${controller.shopInfoList[0].cmGST}",
                                          style: CustomTextStyles
                                              .titleSmallBluegray900,
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                            SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (controller.shopInfoList[0].cmType == 5)
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Beat",
                                          style: CustomTextStyles
                                              .titleSmallPoppinsGray60002
                                              .copyWith(
                                            color: appTheme.gray60002,
                                          ),
                                        ),
                                        SizedBox(height: 0.v),
                                        Text(
                                          controller.getBeatNameFromBeatCode(
                                                  controller.shopInfoList[0]
                                                          .cmBeatRelationCode ??
                                                      "") ??
                                              "",
                                          style: CustomTextStyles
                                              .titleSmallBluegray900,
                                        ),
                                      ],
                                    ),
                                  SizedBox(height: 8),
                                  if (controller
                                      .shopInfoList[0].cmEmail!.isNotEmpty)
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(height: 8),
                                        Text(
                                          "Email",
                                          style: CustomTextStyles
                                              .titleSmallPoppinsGray60002
                                              .copyWith(
                                            color: appTheme.gray60002,
                                          ),
                                        ),
                                        SizedBox(height: 0.v),
                                        Text(
                                          "${controller.shopInfoList[0].cmEmail}",
                                          style: CustomTextStyles
                                              .titleSmallBluegray900,
                                        ),
                                        SizedBox(height: 8),
                                      ],
                                    ),
                                  if (controller
                                      .shopInfoList[0].cmDOB!.isNotEmpty)
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(height: 8),
                                        Text(
                                          "DOB",
                                          style: CustomTextStyles
                                              .titleSmallPoppinsGray60002
                                              .copyWith(
                                            color: appTheme.gray60002,
                                          ),
                                        ),
                                        SizedBox(height: 0.v),
                                        Text(
                                          controller.formatDate(
                                              "${controller.shopInfoList[0].cmDOB}"),
                                          style: CustomTextStyles
                                              .titleSmallBluegray900,
                                        ),
                                        SizedBox(height: 8),
                                      ],
                                    ),
                                  if (controller.shopInfoList[0].cmAnniversary!
                                      .isNotEmpty)
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Anniversary",
                                          style: CustomTextStyles
                                              .titleSmallPoppinsGray60002
                                              .copyWith(
                                            color: appTheme.gray60002,
                                          ),
                                        ),
                                        SizedBox(height: 0.v),
                                        Text(
                                          controller.formatDate(
                                              "${controller.shopInfoList[0].cmAnniversary ?? ""}"),
                                          style: CustomTextStyles
                                              .titleSmallBluegray900,
                                        ),
                                      ],
                                    ),
                                  SizedBox(height: 8),
                                  if (controller.shopInfoList[0]
                                          .cmOutstandingAmount !=
                                      0.0)
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Balance",
                                          style: CustomTextStyles
                                              .titleSmallPoppinsGray60002
                                              .copyWith(
                                            color: appTheme.gray60002,
                                          ),
                                        ),
                                        SizedBox(height: 0.v),
                                        Text(
                                          "₹${controller.shopInfoList[0].cmOutstandingAmount ?? 0}",
                                          style: CustomTextStyles
                                              .titleSmallBluegray900,
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    if (controller.shopInfoList[0].cmAddress != '')
                      Padding(
                        padding: const EdgeInsets.only(left: 12, right: 12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              "Address",
                              style: CustomTextStyles.titleSmallPoppinsGray60002
                                  .copyWith(
                                color: appTheme.gray60002,
                              ),
                            ),
                            SizedBox(height: 0.v),
                            Text(
                              "${controller.shopInfoList[0].cmAddress}",
                              style: CustomTextStyles.titleSmallBluegray900,
                            ),
                          ],
                        ),
                      ),
                    Spacer(),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          if (controller.shopInfoList[0].cmType == 5) ...[
                            Expanded(
                              child: SizedBox(
                                height: 40.0,
                                child: ElevatedButton(
                                  onPressed: () {
                                    print("button shop images tapped");
                                    if (controller.shopInfoList[0]
                                                .cmImageRelation !=
                                            "" &&
                                        controller.shopInfoList[0]
                                                .cmImageRelation !=
                                            'null' &&
                                        controller.shopInfoList[0]
                                                .cmImageRelation !=
                                            null) {
                                      List<String> stringArray = controller
                                          .shopInfoList[0].cmImageRelation!
                                          .split(',');

                                      int imgLength = controller.shopInfoList[0]
                                          .cmImageRelation!.length;
                                      print("imgLength: ${imgLength}");

                                      if (imgLength < 125) {
                                        openImageDialog(stringArray, 0, 1);
                                      } else {
                                        openImageDialog(stringArray, 0, 2);
                                      }
                                    } else {
                                      showToastMessage("No shop images found");
                                    }
                                  },
                                  child: Text(
                                    'View Shop Images',
                                    style: CustomTextStyles
                                        .titleSmallBluegray900logout,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 10),
                            Expanded(
                              child: SizedBox(
                                height: 40.0,
                                child: ElevatedButton(
                                  onPressed: () {
                                    SharedPrefManager.instance.setBool(
                                        ConstantValues.isFromProductShopInfo,
                                        true);
                                    SharedPrefManager.instance.setString(
                                        ConstantValues.selectedRetailerCode,
                                        controller.shopInfoList[0].cmCode ??
                                            "");
                                    Get.toNamed(AppRoutes.addRetailerScreen);
                                  },
                                  child: Text(
                                    'Edit Shop Details',
                                    style: CustomTextStyles
                                        .titleSmallBluegray900logout,
                                  ),
                                ),
                              ),
                            ),
                          ] else
                            Expanded(
                              child: SizedBox(
                                height: 40.0,
                                child: ElevatedButton(
                                  onPressed: () {
                                    print("button shop images tapped");
                                    if (controller.shopInfoList[0]
                                                .cmImageRelation !=
                                            "" &&
                                        controller.shopInfoList[0]
                                                .cmImageRelation !=
                                            'null' &&
                                        controller.shopInfoList[0]
                                                .cmImageRelation !=
                                            null) {
                                      List<String> stringArray = controller
                                          .shopInfoList[0].cmImageRelation!
                                          .split(',');

                                      int imgLength = controller.shopInfoList[0]
                                          .cmImageRelation!.length;
                                      print("imgLength: ${imgLength}");

                                      if (imgLength < 125) {
                                        openImageDialog(stringArray, 0, 1);
                                      } else {
                                        openImageDialog(stringArray, 0, 2);
                                      }
                                    } else {
                                      showToastMessage("No shop images found");
                                    }
                                  },
                                  child: Text(
                                    'View Shop Images',
                                    style: CustomTextStyles
                                        .titleSmallBluegray900logout,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

void openImageDialog(List<String> imageList, int initialIndex, int type) {
  int currentIndex = initialIndex;

  Get.dialog(
    AlertDialog(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      title: Text(
        "Shop Images",
        style: CustomTextStyles.titleMediumPrimary.copyWith(
          color: theme.colorScheme.primary,
        ),
      ),
      content: SizedBox(
        height: 300,
        child: Stack(
          alignment: Alignment.center,
          children: [
            type == 1
                ? InAppWebView(
                    initialUrlRequest: URLRequest(
                        url: WebUri(ApiClient.imgRetailerBaseURL +
                            imageList[currentIndex])),
                    onLoadStart: (controller, url) => print("Loading started"),
                    onLoadStop: (controller, url) {
                      print("Loading completed");
                    },
                  )
                : Image.memory(
                    base64Decode(imageList[currentIndex]),
                    fit: BoxFit.cover, 
                  ),
            if (currentIndex > 0)
              Positioned(
                left: 0,
                child: IconButton(
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    if (currentIndex > 0) {
                      currentIndex--;
                      Get.back();
                      openImageDialog(imageList, currentIndex, type);
                    }
                  },
                ),
              ),
            if (currentIndex <
                imageList.length - 1) 
              Positioned(
                right: 0,
                child: IconButton(
                  icon: Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    if (currentIndex < imageList.length - 1) {
                      currentIndex++;
                      Get.back();
                      openImageDialog(imageList, currentIndex, type);
                    }
                  },
                ),
              ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: Text(
            'Close',
            style: CustomTextStyles.bodyMediumPoppinsBluegray900.copyWith(
              color: theme.colorScheme.primary,
            ),
          ),
        ),
      ],
    ),
  );
}
