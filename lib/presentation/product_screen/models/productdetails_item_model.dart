import '../../../core/app_export.dart';

/// This class is used in the [productdetails_item_widget] screen.
class ProductdetailsItemModel {
  ProductdetailsItemModel({
    this.productName,
    this.productName1,
    this.sizeText,
    this.barcodeText,
    this.mrpText,
    this.ptsText,
    this.bunchText,
    this.boxText,
    this.pcsText,
    this.bunchText1,
    this.boxText1,
    this.kgLtrText,
    this.id,
  }) {
    productName = productName ?? Rx("Amee saffron 1 GM");
    productName1 = productName1 ?? Rx("Amee saffron 1 GM");
    sizeText = sizeText ?? Rx("Size : 0.001");
    barcodeText = barcodeText ?? Rx("Barcode : 0.0");
    mrpText = mrpText ?? Rx("MRP : 0.001");
    ptsText = ptsText ?? Rx("PTS : ");
    bunchText = bunchText ?? Rx("Bunch : 0.001");
    boxText = boxText ?? Rx("Box : 0.001");
    pcsText = pcsText ?? Rx("Pcs.");
    bunchText1 = bunchText1 ?? Rx("Bunch");
    boxText1 = boxText1 ?? Rx("Box");
    kgLtrText = kgLtrText ?? Rx("KG/Ltr : 0.001");
    id = id ?? Rx("");
  }

  Rx<String>? productName;
  Rx<String>? productName1;
  Rx<String>? sizeText;
  Rx<String>? barcodeText;
  Rx<String>? mrpText;
  Rx<String>? ptsText;
  Rx<String>? bunchText;
  Rx<String>? boxText;
  Rx<String>? pcsText;
  Rx<String>? bunchText1;
  Rx<String>? boxText1;
  Rx<String>? kgLtrText;
  Rx<String>? id;
}
