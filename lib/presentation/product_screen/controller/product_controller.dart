import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/product_screen/models/product_model.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/presentation/add_leave_screen/models/add_leave_model.dart';
import 'package:sfm_new/presentation/order_history_screen/models/order_history_model.dart';
import 'package:sfm_new/presentation/super_stockist_screen/controller/super_stockist_controller.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/beat_model.dart';
import 'package:sfm_new/sfmDatabase/models/call_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:sfm_new/sfmDatabase/models/payment_collection_model.dart';
import 'package:sfm_new/sfmDatabase/models/payment_type_model.dart';
import 'package:sfm_new/sfmDatabase/models/price_group_model.dart';
import 'package:sfm_new/sfmDatabase/models/product_brand_model.dart';
import 'package:sfm_new/sfmDatabase/models/product_type_model.dart';
import 'package:sfm_new/sfmDatabase/models/uom_model.dart';
import 'package:uuid/uuid.dart';
import 'package:dio/dio.dart' as dio_package;

enum OrderCustType { all, ss, distributor, dealer, retailer }

enum OrderDays { all, today, last7days, last30days, date }

enum PaymentFilterType { all, cash, cheque, netbanking }

enum PaymentStatus { all, pending, approved, rejected }

class ProductController extends GetxController {
  TextEditingController searchController = TextEditingController();
  TextEditingController amountController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();
  TextEditingController ptsController = TextEditingController();
  TextEditingController pcsController = TextEditingController();

  TextEditingController bunchController = TextEditingController();
  TextEditingController boxController = TextEditingController();

  Rx<AddLeaveModel> addLeaveModelObj = AddLeaveModel().obs;
  Rx<OrderHistoryModel> orderHistoryModelObj = OrderHistoryModel().obs;
  SelectionPopupModel? selectedDropDownValue;
  var selectedOption = "Cash".obs;

  RxList<ProductBrand> productBrandList = <ProductBrand>[].obs;
  RxList<ProductType> productTypeList = <ProductType>[].obs;
  RxList<PriceGroup> priceGroupList = <PriceGroup>[].obs;

  RxList<UOM> uomList = <UOM>[].obs;

  RxList<AddProduct> productList = <AddProduct>[].obs;
  RxList<AddProduct> filteredProductList = <AddProduct>[].obs;
  RxList<AddProduct> cartList = <AddProduct>[].obs;

  final custTypeController = Get.find<SuperStockistController>();

  static ProductController get instance => Get.find();

  RxBool isOrderProductive = true.obs;

  var isLoading = true.obs;
  final searchText = ''.obs;

  final Rx<int?> currentProductId =
      Rx<int?>(null); 

  RxDouble totalPcsAmount = 0.0.obs;
  RxDouble totalBunchAmount = 0.0.obs;
  RxDouble totalBoxAmount = 0.0.obs;

  RxDouble totalAmount = 0.0.obs;

  RxInt pcsQty = 0.obs;
  RxInt bunchQty = 0.obs;
  RxInt boxQty = 0.obs;

  RxInt totalQty = 0.obs;

  RxInt checkinType = 0.obs;
  RxInt isOldVersion = 0.obs;

  int selectedCustType = 0;
  RxString selectedClientCode = "".obs;

  RxList<CallData> callList = <CallData>[].obs;
  RxList<CallData> filteredCallList = <CallData>[].obs;
  RxList<Customer> customerList = <Customer>[].obs;

  RxList<Beat> beatList = <Beat>[].obs;

  RxList<Customer> shopInfoList = <Customer>[].obs;

  RxSet<OrderCustType> currentOrderCustType =
      <OrderCustType>{OrderCustType.all}.obs;

  RxSet<OrderDays> currentOrderDays = <OrderDays>{OrderDays.all}.obs;

  RxSet<PaymentFilterType> currentPaymentTypes =
      <PaymentFilterType>{PaymentFilterType.all}.obs;
  RxSet<PaymentStatus> currentPaymentStatuses =
      <PaymentStatus>{PaymentStatus.all}.obs;

  RxDouble grandTotalAmount = 0.0.obs;

  late Rx<DateTime> fromDate;
  late Rx<DateTime> toDate;

  RxString formattedFromDate = ''.obs;
  RxString formattedToDate = ''.obs;

  RxString filterFromDate = ''.obs;
  RxString filterToDate = ''.obs;

  RxList<Payment> paymentList = <Payment>[].obs;
  RxList<PaymentType> paymentTypeList = <PaymentType>[].obs;
  RxList<Payment> filteredPaymentList = <Payment>[].obs;

  List<TextEditingController> ptsValueControllers = [];
  List<TextEditingController> pcsValueControllers = [];
  List<TextEditingController> bunchValueControllers = [];
  List<TextEditingController> boxValueControllers = [];

  final Connectivity _connectivity = Connectivity();

  RxString employeeToken = "".obs;
  bool isPaymentAPISuccess = false;

  bool isCustomerAPISuccess = false;

  RxInt intDefaultPcs = 0.obs;
  RxInt intDefaultBunch = 0.obs;
  RxInt intDefaultBox = 0.obs;

  RxString selectedPartyName = ''.obs;

  @override
  void onClose() {
    super.onClose();
    amountController.dispose();
    descriptionController.dispose();
    searchController.dispose();

    pcsController.dispose();
    bunchController.dispose();
    boxController.dispose();
  }

  @override
  void onInit() async {
    super.onInit();

    selectedClientCode.value = await SharedPrefManager.instance
            .getString(ConstantValues.selectedCustCode) ??
        '';
    print("selectedClientCode: ${selectedClientCode}");

    selectedCustType = await SharedPrefManager.instance
            .getInt(ConstantValues.selectedCustType) ??
        0;

    print("selectedCustType: ${selectedCustType}");

    cartList.clear();
    productList.clear();
    filteredProductList.clear();

    fetchedToken();

    await fetchProductType();
    await fetchUOMType();
    await fetchProducts();

    await fetchCheckInType();

    fromDate = DateTime.now().obs;
    toDate = (DateTime.now().add(Duration(days: 1))).obs;

    formattedFromDate.value = formatDateTime(fromDate.toString());
    formattedToDate.value = formatDateTime(toDate.toString());

    await fetchCustomers();
    await fetchCalls();
    await fetchBeats();
    await fetchPaymentType();
    await fetchPayments();
    await fetchAmounts();

    await checkInternetConnectivity();

    await checkInternetConnectivityCustomer();

    String formattedDateTime = DateFormat("HH:mm:ss").format(DateTime.now());
    print(formattedDateTime);

    SharedPrefManager.instance
        .setString(ConstantValues.callStartTime, formattedDateTime);
  }

  @override
  void onReady() {
    super.onReady();
    // This function will be called every time you come back to the screen
    // fetchData();
  }

  String formatDateTime(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('dd MMM');
    return formatter.format(dateTime);
  }

  String formatFilterDateTime(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(dateTime);
  }

  Future<void> fetchCheckInType() async {
    checkinType.value =
        (await SharedPrefManager.instance.getInt(ConstantValues.checkInType))!;
    print("checkinType.value: ${checkinType.value}");

    isOldVersion.value =
        (await SharedPrefManager.instance.getInt(ConstantValues.isUsingOldVersion))!;
    print("isOldVersion.value: ${isOldVersion.value}");
  }

  Future<bool> checkInternetConnectivity() async {
    var connectivityResult = await _connectivity.checkConnectivity();
    if (connectivityResult != ConnectivityResult.none) {
      fetchedToken();
      syncPaymentsWithServer();
    }
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> syncPaymentsWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedPayments = await db.query(
        '${TableValues.tablePaymentCollection}',
        where: '${TableValues.pcSyncStatus} = ?',
        whereArgs: [0]);

    if (unsyncedPayments.isNotEmpty) {
      print('Syncing payments with the server...');
      print("unsyncedPayments: ${unsyncedPayments}");

      for (final payment in unsyncedPayments) {
        try {
          await addPaymentAPI(payment);

          if (isPaymentAPISuccess == true) {
            await db.update('${TableValues.tablePaymentCollection}',
                {'${TableValues.pcSyncStatus}': 1},
                where: '${TableValues.pcCode} = ?',
                whereArgs: [payment['${TableValues.pcCode}']]);

            print(
                'Payment with ID ${payment['${TableValues.pcCode}']} synced successfully');
          } else {
            print(
                'Error syncing payment with ID ${payment['${TableValues.pcCode}']}');

            await ApiLogger.logError(
              apiName: "Add payment sync",
              apiRequestData: payment,
              apiResponse:
                  'Error syncing payment with ID ${payment['${TableValues.pcCode}']}',
            );
          }
        } catch (error) {
          print(
              'Error syncing Payment with ID ${payment['${TableValues.pcCode}']}: $error');

          await ApiLogger.logError(
            apiName: "Add payment sync with error",
            apiRequestData: payment,
            apiResponse:
                'Error syncing payment with ID ${payment['${TableValues.pcCode}']}: $error',
          );
        }
      }
      fetchPayments();
    } else {
      print('No payments to sync.');
    }
  }

  Future<void> addPaymentAPI(Map<String, dynamic> payment) async {
    try {
      Map<String, dynamic> data = {};

      print(payment['pc_type']);

      if (payment['pc_type'] == 1) {
        data = {
          'pc_code': "${payment['pc_code']}",
          'cm_code': payment['pc_cm_code'],
          'pc_type': payment['pc_type'],
          'pc_amount': "${payment['pc_amount']}",
          'pc_note': "${payment['pc_note']}",
          'emp_code': payment['pc_emp_code'],
          'pc_condition_id': payment['pc_condition_id'],
        };
      } else if (payment['pc_type'] == 2) {
        data = {
          'pc_code': "${payment['pc_code']}",
          'cm_code': "${payment['pc_cm_code']}",
          'pc_type': "${payment['pc_type']}",
          'pc_amount': "${payment['pc_amount']}",
          'pc_note': "${payment['pc_note']}",
          'emp_code': "${payment['pc_emp_code']}",
          'pc_condition_id': "${payment['pc_condition_id']}",
          'pc_bank_id': "${payment['pc_bank_id']}",
          'pc_cheque_date': "${payment['pc_cheque_date']}",
          'pc_cheque_no': "${payment['pc_cheque_no']}",
          'pc_account_no': "${payment['pc_account_no']}",
          'pc_cheque_photo': "${payment['pc_cheque_photo']}",
          'pc_status': "${payment['pc_status']}",
          'created_at': "${payment['created_at']}",
          'updated_at': "${payment['updated_at']}",
        };
      } else if (payment['pc_type'] == 3) {
        data = {
          'pc_code': "${payment['pc_code']}",
          'cm_code': "${payment['pc_cm_code']}",
          'pc_type': "${payment['pc_type']}",
          'pc_amount': "${payment['pc_amount']}",
          'pc_note': "${payment['pc_note']}",
          'emp_code': "${payment['pc_emp_code']}",
          'pc_condition_id': "${payment['pc_condition_id']}",
          'pc_bank_id': "${payment['pc_bank_id']}",
          'pc_cheque_date': "${payment['pc_cheque_date']}",
          'pc_cheque_no': "${payment['pc_cheque_no']}",
          'pc_account_no': "${payment['pc_account_no']}",
          'pc_cheque_photo': "${payment['pc_cheque_photo']}",
          'pc_status': "${payment['pc_status']}",
          'created_at': "${payment['created_at']}",
          'updated_at': "${payment['updated_at']}",
        };
      }

      print("data: ${data}");

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };
      print("employeeToken: ${employeeToken.value}");

      final response = await ApiClient.dioClient
          .post(ApiClient.getUrl(ApiClient.postAddPaymentEndpoint),
              data: data, options: Options(headers: headers))
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      if (response.statusCode == 200) {
        print('Request successful');
        print('Response data: ${response.data}');
        isPaymentAPISuccess = true;
      } else {
        print('Unexpected status code: ${response.statusCode}');
        isPaymentAPISuccess = false;
      }
      print('Response status: ${response.statusCode}');
      print('Response data: ${response.data}');
    } on DioException catch (e) {
      isPaymentAPISuccess = false;

      String errorMessage;

      if (e.response != null) {
        errorMessage =
            'Error ${e.response!.statusCode}: ${e.response!.statusMessage}';
        print(errorMessage);
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        }
      } else {
        errorMessage = 'Network error: ${e.message}';
        print(errorMessage);
      }

      await ApiLogger.logError(
        apiName: ApiClient.postAddPaymentEndpoint,
        apiRequestData: payment,
        apiResponse: errorMessage,
      );
    } catch (e) {
      isPaymentAPISuccess = false;
      // EasyLoading.dismiss();
      print('Unexpected error: $e');
    }
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  Future<void> fetchProducts() async {
    isLoading(true); 
    print("fetchProducts called");
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableProduct}");

    // Populate the product list
    productList.value = List.generate(maps.length, (index) {
      var productMap = maps[index];
      print("productMap: ${productMap}");

      var productTypeId = productMap['pm_pt_id'];
      var uomTypeId = productMap['uom'];

      // Fetch the product type name
      String? productTypeName = fetchTypeName(productTypeId);

      String? uomTypeName = fetchUOMTypeName(uomTypeId);
      print("uomTypeName: ${uomTypeName}");

      return AddProduct(
        orderCode: productMap['orderCode'],
        productCode: productMap['product_code'],
        productName: productMap['product_name'],
        productType: productTypeName,
        productBrand: productMap['product_brand'] ?? 0,
        uom: productMap['uom'] ?? 0,
        uomType: uomTypeName,
        unitSize: (productMap['unit_size'] ?? 0).toDouble(),
        innerCaseSize: productMap['inner_case_size'] ?? 0,
        outerCaseSize: productMap['outer_case_size'] ?? 0,
        productMrp: (productMap['product_mrp'] ?? 0).toDouble(),
        productGst: (productMap['product_gst'] ?? 0).toDouble(),
        productHsnSacCode: productMap['product_hsn_sac_code'] ?? '',
        productImage: productMap['product_image'] ?? '',
        productStatus: productMap['product_status'] ?? 0,
        barcode: productMap['barcode'] ?? '',
        marketType: productMap['market_type'] ?? 0,
        productPtss: RxDouble((productMap['product_ptss'] ?? 0).toDouble()),
        productPtdistributor:
            RxDouble((productMap['product_ptdistributor'] ?? 0).toDouble()),
        productPtdealer:
            RxDouble((productMap['product_ptdealer'] ?? 0).toDouble()),
        productPtretailer:
            RxDouble((productMap['product_ptretailer'] ?? 0).toDouble()),
        pmPtId: productMap['pm_pt_id'] ?? 0,
        quantity: RxInt(productMap['quantity'] ?? 0),
        pcs: RxInt(productMap['pcs'] ?? 0),
        box: RxInt(productMap['box'] ?? 0),
        bunch: RxInt(productMap['bunch'] ?? 0),
        mrp: (productMap['mrp'] ?? 0).toDouble(),
        pts: (productMap['pts'] ?? 0).toDouble(),
        rateBasic: (productMap['rate_basic'] ?? 0).toDouble(),
        totalBasicRate: (productMap['total_basic_rate'] ?? 0).toDouble(),
        gst: productMap['gst'] ?? 0,
        gstAmount: (productMap['gst_amount'] ?? 0).toDouble(),
        grandTotal: RxDouble((productMap['grand_total'] ?? 0).toDouble()),
      );
    });

    filteredProductList.value = productList.toList()
      ..sort((a, b) => a.productType!.compareTo(b.productType!));

    print("productList123: ${productList}");
    print("filteredProductList: ${filteredProductList}");

    updateTextEditingControllers();

    print("pcsValueControllers: ${pcsValueControllers}");
  }

  Future<void> fetchFilteredProducts() async {
    filteredProductList.clear();
    filteredProductList.value = productList
        .where((product) =>
            product.productName!
                .toLowerCase()
                .contains(searchText.value.toLowerCase()) ||
            product.barcode!
                .toLowerCase()
                .contains(searchText.value.toLowerCase()) ||
            '${fetchTypeName(product.pmPtId!)}'
                .toLowerCase()
                .contains(searchText.value.toLowerCase()))
        .toList()
      ..sort((a, b) => a.productType!.compareTo(b.productType!));
    ;

    print("filteredProductList: ${filteredProductList}");

    print("pcsValueControllers: ${pcsValueControllers}");

    updateTextEditingControllers();

    print("filteredProductList: ${filteredProductList}");
  }

  void updateTextEditingControllers() {
    ptsValueControllers.clear();
    pcsValueControllers.clear();
    bunchValueControllers.clear();
    boxValueControllers.clear();

    for (var product in filteredProductList) {
      final ptsvalue = selectedCustType == 2
          ? "${product.productPtss!.value}"
          : selectedCustType == 3
              ? "${product.productPtdistributor!.value}"
              : selectedCustType == 4
                  ? "${product.productPtdealer!.value}"
                  : "${product.productPtretailer!.value}";

      final pcsValue = product.pcs?.value == 0 ? '' : '${product.pcs?.value}';

      final bunchValue =
          product.bunch?.value == 0 ? '' : '${product.bunch?.value}';

      final boxValue = product.box?.value == 0 ? '' : '${product.box?.value}';

      ptsValueControllers.add(TextEditingController(text: ptsvalue));
      pcsValueControllers.add(TextEditingController(text: pcsValue));
      bunchValueControllers.add(TextEditingController(text: bunchValue));
      boxValueControllers.add(TextEditingController(text: boxValue));
    }
  }

  void updateSearchText(String newText) {
    searchText.value = newText;
    fetchFilteredProducts();
  }

  Future<void> fetchProductType() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableProductType}");
    productTypeList.value = List.generate(maps.length, (index) {
      return ProductType.fromMap(maps[index]);
    });
    isLoading(false);
  }

  Future<void> fetchUOMType() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableUOM}");
    uomList.value = List.generate(maps.length, (index) {
      return UOM.fromMap(maps[index]);
    });
    isLoading(false);
  }

  String? fetchTypeName(int productTypeId) {
    try {
      ProductType? type = productTypeList.firstWhere(
        (type) => type.type_id == productTypeId,
      );
      return type.type_name;
    } catch (e) {
      if (kDebugMode) {
        print('Error in fetchTypeName: $e');
      }
      return null;
    }
  }

  String? fetchUOMTypeName(int productTypeId) {
    try {
      UOM? type = uomList.firstWhere(
        (type) => type.uom_id == productTypeId,
      );
      return type.uom_name;
    } catch (e) {
      if (kDebugMode) {
        print('Error in fetchTypeName: $e');
      }
      return null;
    }
  }

  String? fetchPaymentTypeName(int pcTypeId) {
    try {
      PaymentType? type = paymentTypeList.firstWhere(
        (type) => type.ptID == pcTypeId,
      );

      return type.ptName;
    } catch (e) {
      if (kDebugMode) {
        print('Error in fetchTypeName: $e');
      }
      return null;
    }
  }

  String? fetchCustomerName(String pcCMCode) {
    print("pcCMCode: ${pcCMCode}");
    try {
      Customer? name = customerList.firstWhere(
        (type) => type.cmCode == pcCMCode,
      );
      print("type: ${name.cmName}");
      return name.cmName;
    } catch (e) {
      if (kDebugMode) {
        print('Error in fetchCustomerName: $e');
      }
      return null;
    }
  }

  void selectOption(String option) {
    selectedOption.value = option;
    print("object: ${selectedOption.value}");
  }

  void addToCart(AddProduct product) {
    // Calculate total based on pcs, box, and bunch

    print("product.pcs: ${product.pcs}");
    print("product.box: ${product.box}");
    print("product.bunch: ${product.bunch}");
    print("product.outerCaseSize: ${product.outerCaseSize}");

    double custTypeValue = 0.0;

    if (selectedCustType == 2) {
      custTypeValue = product.productPtss!.value;
    } else if (selectedCustType == 3) {
      custTypeValue = product.productPtdistributor!.value;
    } else if (selectedCustType == 4) {
      custTypeValue = product.productPtdealer!.value;
    } else if (selectedCustType == 5) {
      custTypeValue = product.productPtretailer!.value;
    }

    double total = 0.0;

    total = (product.pcs!.value > 0) ? product.pcs!.value * custTypeValue : 0;
    print("total1: ${total}");

    total += (product.box! > 0 && product.outerCaseSize! > 0)
        ? ((product.box!.value * custTypeValue) * product.outerCaseSize!)
        : 0;
    print("total2: ${total}");

    total += (product.bunch! > 0 && product.innerCaseSize! > 0)
        ? ((product.bunch! * custTypeValue) * product.innerCaseSize!)
        : 0;
    print("total3: ${total}");

    int productQty = (product.pcs!.value > 0) ? product.pcs!.value * 1 : 0;
    print("productQty1: ${productQty}");

    productQty += (product.box!.value > 0 && product.outerCaseSize! > 0)
        ? product.box!.value * product.outerCaseSize!
        : 0;
    print("productQty2: ${productQty}");

    productQty += (product.bunch!.value > 0 && product.innerCaseSize! > 0)
        ? product.bunch!.value * product.innerCaseSize!
        : 0;
    print("productQty3: ${productQty}");

    int totalQuantity = 0;
    double totalAmt = 0.0;

    // Check if any of bunch, pcs, or box values is greater than 0
    if (product.bunch! > 0 || product.pcs! > 0 || product.box! > 0) {
      // Check if the product already exists in cart

      var index = cartList
          .indexWhere((item) => item.productCode == product.productCode);
      if (index != -1) {
        double totalValue = double.parse(total.toStringAsFixed(2));
        print("totalValue inside if : ${totalValue}");
        if (totalValue > 0) {
          print("Inside IF");
          print("product.pcs!.value: ${product.pcs!.value}");
          print("product.box!.value: ${product.box!.value}");
          print("product.bunch!.value: ${product.bunch!.value}");
          print("product.outerCaseSize: ${product.outerCaseSize!}");
          print("product.innerCaseSize: ${product.innerCaseSize!}");
          cartList[index].quantity!.value = productQty;
          cartList[index].pcs!.value = product.pcs!.value;

          if (product.outerCaseSize! > 0) {
            cartList[index].box!.value = product.box!.value;
          } else {
            cartList[index].box!.value = 0;
          }

          if (product.innerCaseSize! > 0) {
            cartList[index].bunch!.value = product.bunch!.value;
          } else {
            cartList[index].bunch!.value = 0;
          }

          cartList[index].productGst = product.productGst;
          cartList[index].grandTotal!.value =
              double.parse(total.toStringAsFixed(2));
          cartList[index].productType = "${fetchTypeName(product.pmPtId!)}";
          print("productType Inside IF: ${cartList[index].productType}");
        } else {
          cartList[index].grandTotal!.value = 0.0;
          cartList[index].quantity!.value = 0;
          cartList.remove(product);
        }
      } else {
        print("Inside else");
        product.grandTotal!.value = double.parse(total.toStringAsFixed(2));
        product.quantity!.value = productQty;
        product.productType = "${fetchTypeName(product.pmPtId!)}";

        if (product.outerCaseSize! == 0) {
          product.box!.value = 0;
        }

        if (product.innerCaseSize! == 0) {
          product.bunch!.value = 0;
        }

        print("productType Inside else: ${product.productType}");
        print("productType: ${product}");
        if (custTypeValue > 0 && product.grandTotal!.value > 0) {
          cartList.add(product);
        }
      }
    } else {
      var index = cartList
          .indexWhere((item) => item.productCode == product.productCode);
      if (index != -1) {
        cartList[index].grandTotal!.value = 0;
      }
      // product.grandTotal
      removeFromCart(product);
    }

    for (var cartItem in cartList) {
      totalAmt += cartItem.grandTotal!.value;
      print("item cartList: ${cartItem}");
      totalQuantity += cartItem.quantity!.value;

      if (totalAmt > 0) {
        for (var productItem in productList) {
          if (cartItem.productCode == productItem.productCode) {
            productItem.pcs = cartItem.pcs;

            if (cartItem.outerCaseSize! > 0) {
              productItem.box = cartItem.box;
            }

            if (cartItem.innerCaseSize! > 0) {
              productItem.bunch = cartItem.bunch;
            }

            print("productItem456: ${productItem}");
          }
        }
      } else {}
    }

    totalQty.value = totalQuantity;
    print("totalQty: ${totalQty}");

    // totalAmt = totalQuantity;
    print("totalAmt: ${totalAmt.toStringAsFixed(2)}");

    totalAmount.value = totalAmt.toDouble();
    print("totalQty: ${totalQty}");
    print("cartList length: ${cartList.length}");
    print("cartList: ${cartList}");

    update();
  }

  void removeFromCart(AddProduct product) {
    cartList.removeWhere((item) => item.productCode == product.productCode);
    print("cartlist removeFromCart: ${cartList}");
  }

  String generateUUID() {
    var uuid = Uuid();
    return uuid.v4();
  }

  //******************************************************************************************************/

  Future<void> fetchCalls() async {
    grandTotalAmount.value = 0.0;
    isLoading(true);
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps = await db.query(
      "${TableValues.tableCall}",
      where: "${TableValues.callClientCode} = ?",
      whereArgs: [
        selectedClientCode.value
      ],
    );
    callList.value = List.generate(maps.length, (index) {
      return CallData.fromMap(maps[index]);
    });
    filteredCallList.value = List.generate(maps.length, (index) {
      return CallData.fromMap(maps[index]);
    });

    print("callList.length: ${callList.length}");
    print("callList: ${callList}");
    isLoading(false);

    for (var item in filteredCallList) {
      grandTotalAmount.value += item.callGrandTotal ?? 0.0;
    }
    print("grandTotalAmount: ${grandTotalAmount}");
  }

  Future<void> fetchPayments() async {
    isLoading.value = true;
    final db = await DatabaseProvider.database;
    print("fetched data from DB: ${db}");

    final List<Map<String, dynamic>> maps = await db.query(
      "${TableValues.tablePaymentCollection}",
      where: "${TableValues.pcCMCode} = ?",
      whereArgs: [
        selectedClientCode.value
      ], 
    );
    paymentList.value = List.generate(maps.length, (index) {
      return Payment.fromMap(maps[index]);
    });
    filteredPaymentList.value = List.generate(maps.length, (index) {
      return Payment.fromMap(maps[index]);
    });
    print("paymentList: ${paymentList}");
    await fetchAmounts();
  }

  Future<void> fetchPaymentType() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tablePaymentType}");
    paymentTypeList.value = List.generate(maps.length, (index) {
      return PaymentType.fromMap(maps[index]);
    });
  }

  Future<void> fetchBeats() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableBeats}");
    beatList.value = List.generate(maps.length, (index) {
      return Beat.fromMap(maps[index]);
    });
    print("beatList: ${beatList}");
  }

  Future<void> fetchCustomers() async {
    customerList.clear();
    shopInfoList.clear();
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCustomer}");
    customerList.value = List.generate(maps.length, (index) {
      return Customer.fromMap(maps[index]);
    });
    print("customerList: ${customerList}");
    print("customerList.length: ${customerList.length}");
    print("selectedClientCode: ${selectedClientCode}");
    shopInfoList.value = customerList
        .where((customer) => customer.cmCode == selectedClientCode.value)
        .toList();
    print("shopInfoList: ${shopInfoList[0].cmDOB}");
    selectedPartyName.value =
        "${getCustomerNameFromPartyCode(shopInfoList[0].cmRelationCode ?? "")}";
  }

  Future<void> fetchAmounts() async {
    totalAmount.value = 0.0;

    for (final payment in paymentList) {
      totalAmount.value += payment.amount!;
    }
    print('totalAmount.value : ${totalAmount.value}');
    isLoading.value = false;
  }

  // callClientCode

  String? getBeatNameFromBeatCode(String beatCode) {
    print("partyCode: ${beatCode}");
    try {
      final customerType = beatList.firstWhere(
        (ccType) => ccType.beat_code == beatCode,
      );
      print("customerType.beat_name: ${customerType.beat_name}");
      return customerType.beat_name;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerCodeFromTypeName: $e');
      }
      return null;
    }
  }

  String? getCustomerNameFromPartyCode(String partyCode) {
    print("partyCode123: ${partyCode}");
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmCode == partyCode,
      );
      print("customerType.cmName: ${customerType.cmName}");
      selectedPartyName.value = "${customerType.cmName}";
      print("selectedPartyName.value: ${selectedPartyName.value}");
      return customerType.cmName;
    } catch (e) {
      selectedPartyName.value = "";
      if (kDebugMode) {
        print('Error in getCustomerCodeFromTypeName: $e');
      }
      return "";
    }
  }

  int? getCustomerTypeFromPartyCode(String partyCode, int custType) {
    print("partyCode: ${partyCode}");
    print("custType: ${custType}");
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmCode == partyCode && ccType.cmType == custType,
      );
      print("customerType.cmType: ${customerType.cmType}");
      return customerType.cmType;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerCodeFromTypeName: $e');
      }
      return null;
    }
  }

  String formatDate(String dateString) {
    if (dateString.isNotEmpty && dateString != 'null') {
      DateTime dateTime = DateTime.parse(dateString);
      String formattedDate = DateFormat('dd MMM yyyy').format(dateTime);
      return formattedDate;
    } else {
      return "";
    }
  }

  void filterOrders(
      String fromDateStr,
      String toDateStr,
      Set<OrderCustType> currentOrderCustTypes,
      Set<OrderDays> currentOrderDays) {
    filteredCallList.clear();
    grandTotalAmount.value = 0.0;

    DateTime fromDate = DateTime.tryParse(fromDateStr) ?? DateTime.now();
    DateTime toDate = DateTime.tryParse(toDateStr) ?? DateTime.now();
    bool isWithinDateRange = false;
    bool mactchesOrderDays = false;

    List<CallData> filteredCalls = callList.where((call) {
      print("call inside: ${call}");
      if (!fromDateStr.isEmpty || !toDateStr.isEmpty) {
        DateTime callFromDate = DateTime.parse(call.callCreatedAt ?? "");
        print('callFromDate: ${callFromDate}');
        DateTime callToDate = DateTime.parse(call.callCreatedAt ?? "");
        print('callToDate: ${callToDate}');

        isWithinDateRange =
            callFromDate.isBefore(toDate) && callToDate.isAfter(fromDate);
      }

      bool matchesCallType = currentOrderCustTypes
              .contains(OrderCustType.all) ||
          (currentOrderCustTypes.contains(OrderCustType.ss) &&
              getCustomerTypeFromPartyCode(call.callClientCode ?? "", 2) ==
                  2) ||
          (currentOrderCustTypes.contains(OrderCustType.distributor) &&
              getCustomerTypeFromPartyCode(call.callClientCode ?? "", 3) ==
                  3) ||
          (currentOrderCustTypes.contains(OrderCustType.dealer) &&
              getCustomerTypeFromPartyCode(call.callClientCode ?? "", 4) ==
                  4) ||
          (currentOrderCustTypes.contains(OrderCustType.retailer) &&
              getCustomerTypeFromPartyCode(call.callClientCode ?? "", 5) == 5);

      if (!currentOrderDays.isEmpty) {
        DateTime fromDate = DateTime.tryParse(fromDateStr) ?? DateTime.now();
        DateTime toDate = DateTime.tryParse(toDateStr) ?? DateTime.now();
        print("fromDate before: ${fromDate}");

        if (currentOrderDays.contains(OrderDays.all)) {
          fromDate = fromDate.subtract(Duration(days: 60));
          fromDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
        } else if (currentOrderDays.contains(OrderDays.today)) {
          fromDate = DateTime.now();
          fromDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
        } else if (currentOrderDays.contains(OrderDays.last7days)) {
          fromDate = fromDate.subtract(Duration(days: 7));
          fromDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
        } else if (currentOrderDays.contains(OrderDays.last30days)) {
          fromDate = fromDate.subtract(Duration(days: 30));
          fromDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
        }

        print("fromDate after: ${fromDate}");

        DateTime callFromDate = DateTime.parse(call.callCreatedAt ?? "");
        print('currentOrderDaysFromDate: ${callFromDate}');
        DateTime callToDate = DateTime.parse(call.callCreatedAt ?? "");
        print('currentOrderDaysToDate: ${callToDate}');

        mactchesOrderDays =
            callFromDate.isBefore(toDate) && callToDate.isAfter(fromDate);
      }

      print("isWithinDateRange: $isWithinDateRange");
      print("matchesCallType: $matchesCallType");
      print("mactchesOrderDays: $mactchesOrderDays");

      if (isWithinDateRange || (matchesCallType && mactchesOrderDays)) {
        filteredCallList.add(call);
      }

      return matchesCallType;
    }).toList();

    for (var item in filteredCallList) {
      grandTotalAmount.value += item.callGrandTotal ?? 0.0;
    }
    print("grandTotalAmount: ${grandTotalAmount}");
    print("callList: ${callList}");
    print("filteredCalls ${filteredCalls}");
    print("filteredCalls.length ${filteredCalls.length}");
  }

  void filterPaymentsByDateRange(
      String fromDateStr,
      String toDateStr,
      Set<PaymentFilterType> currentPaymentType,
      Set<PaymentStatus> paymentStatuses) {
    filteredPaymentList.clear();

    DateTime fromDate = DateTime.tryParse(fromDateStr) ?? DateTime.now();
    DateTime toDate = DateTime.tryParse(toDateStr) ?? DateTime.now();
    print("fromDate fetched : ${fromDate}");
    print("toDate fetched : ${toDate}");
    bool isWithinDateRange = false;

    // 0-pending, 1-conform, 2-rejected, 3-partial approved

    List<Payment> filteredPayments = paymentList.where((payment) {
      if (!fromDateStr.isEmpty || !toDateStr.isEmpty) {
        DateTime paymentFromDate = DateTime.parse(payment.createdAt ?? "");
        print('paymentFromDate: ${paymentFromDate}');
        DateTime paymentToDate = DateTime.parse(payment.createdAt ?? "");
        print('paymentToDate: ${paymentToDate}');

        fromDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
        print("fromDate123: ${fromDate}");
        toDate = DateTime(toDate.year, toDate.month, toDate.day);
        print("toDate123: ${toDate}");

        isWithinDateRange =
            paymentFromDate.isBefore(toDate) && paymentToDate.isAfter(fromDate);
      }

      bool matchesPaymentType =
          currentPaymentType.contains(PaymentFilterType.all) ||
              (currentPaymentType.contains(PaymentFilterType.cash) &&
                  payment.type == 1) ||
              (currentPaymentType.contains(PaymentFilterType.cheque) &&
                  payment.type == 2) ||
              (currentPaymentType.contains(PaymentFilterType.netbanking) &&
                  payment.type == 3);

      bool matchesPaymentStatus = paymentStatuses.contains(PaymentStatus.all) ||
          (paymentStatuses.contains(PaymentStatus.pending) &&
              payment.status == 0) ||
          (paymentStatuses.contains(PaymentStatus.approved) &&
              payment.status == 1) ||
          (paymentStatuses.contains(PaymentStatus.rejected) &&
              payment.status == 2);

      // Debugging information
      print("Payment Code: ${payment.code}");
      print("isWithinDateRange: $isWithinDateRange");
      print("matchesPaymentType: $matchesPaymentType");
      print("matchesPaymentStatus: $matchesPaymentStatus");

      if (isWithinDateRange || (matchesPaymentType && matchesPaymentStatus)) {
        filteredPaymentList.add(payment);
      }
      return matchesPaymentType && matchesPaymentStatus;
    }).toList();
    print("filteredPaymentList: ${filteredPaymentList}");
    print("filteredPaymentList.length ${filteredPayments.length}");
  }

  Future<bool> checkInternetConnectivityCustomer() async {
    var connectivityResult = await _connectivity.checkConnectivity();
    if (connectivityResult != ConnectivityResult.none) {
      syncCustomersWithServer();
    }
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> syncCustomersWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedCustomers = await db.query(
        '${TableValues.tableCustomer}',
        where: '${TableValues.customerSyncStatus} = ?',
        whereArgs: [0]);

    if (unsyncedCustomers.isNotEmpty) {
      print('Syncing customers with the server...');
      print("unsyncedCustomers: ${unsyncedCustomers}");

      for (final customer in unsyncedCustomers) {
        try {
          print("Customer: ${customer}");
          await updateCustomerAPI(customer);

          if (isCustomerAPISuccess == true) {
            await db.update('${TableValues.tableCustomer}',
                {'${TableValues.customerSyncStatus}': 1},
                where: '${TableValues.customerCode} = ?',
                whereArgs: [customer['${TableValues.customerCode}']]);
            print(
                'Customer with ID ${customer['${TableValues.customerCode}']} synced successfully');
          } else {
            print(
                'Error syncing Customer with ID ${customer['${TableValues.customerCode}']}');
          }
        } catch (error) {
          print(
              'Error syncing Customer with ID ${customer['${TableValues.customerCode}']}: $error');
        }
      }

      fetchCustomers();
    } else {
      print('No customers to sync.');
    }
  }

  Future<void> updateCustomerAPI(Map<String, dynamic> customer) async {
    try {
      Map<String, dynamic> data = {};

      String strImage = "${customer['cm_image_relation']}";
      List<String> strImagesList = strImage.split(",");
      print("strImagesList: ${strImagesList}");

      data = {
        "cm_code": "${customer['cm_code']}",
        "cm_name": "${customer['cm_name']}",
        "cm_mobile": customer['cm_mobile'],
        "cm_mobile2": customer['cm_mobile2'],
        "cm_email": "${customer['cm_email']}",
        "cm_address": "${customer['cm_address']}",
        "cm_pincode": customer['cm_pincode'],
        "cm_gst": "${customer['cm_gst']}",
        "cm_pan": "${customer['cm_pan']}",
        "latitude": "${customer['cm_lat']}",
        "longitude": "${customer['cm_long']}",
        "cm_town_id": customer['cm_town_id'],
        "cm_outstanding_amount": customer['cm_outstanding_amount'],
        "cm_contact_person": "${customer['cm_contact_person']}",
        "cm_area": "${customer['cm_area']}",
        "cm_relation_code": "${customer['cm_relation_code']}",
        "cm_type": customer['cm_type'],
        "beat_code": "${customer['cm_beat_relation']}",
        "birth_date": "${customer['cm_dob']}",
        "anni_date": "${customer['cm_anniversary']}",
        "category_id": customer['cm_category_relation'],
        "grade_id": customer['cm_grade_relation'],
        "shop_images": strImagesList,
      };

      print("data: ${data}");

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };

      // dio_package.Dio dioClient = dio_package.Dio();
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.postUpdateCustomerEndpoint),
        data: data,
        options: dio_package.Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.data);
      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            isCustomerAPISuccess = true;
            // EasyLoading.dismiss();
          }
        } else {
          isCustomerAPISuccess = false;
          print('Response data is null');
        }
      } catch (e) {
        isCustomerAPISuccess = false;
        print('Error parsing response data: $e');
      }
    } on dio_package.DioException catch (e) {
      isCustomerAPISuccess = false;
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        print(e.response!.data["message"] ?? "No data found");
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
    } finally {}
  }
}
