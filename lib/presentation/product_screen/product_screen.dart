// ignore_for_file: unnecessary_null_comparison, unused_local_variable, invalid_use_of_protected_member, unused_import

import 'package:sfm_new/presentation/product_screen/widgets/history_list_widget.dart';
import 'package:sfm_new/presentation/product_screen/widgets/payment_list_widget.dart';
import 'package:sfm_new/presentation/product_screen/widgets/product_list_widget.dart';
import 'package:sfm_new/presentation/product_screen/widgets/shop_info_widget.dart';

import 'controller/product_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';

import 'package:dynamic_tabbar/dynamic_tabbar.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';

// ignore_for_file: must_be_immutable
class ProductScreen extends GetWidget<ProductController> {
  bool isScrollable = false;
  bool showNextIcon = true;
  bool showBackIcon = true;

  Widget? leading;
  Widget? trailing;
  List<TabData> tabs = [];

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: Column(
          children: [
            Expanded(
              child: DynamicTabBarWidget(
                dynamicTabs: tabs = [
                  TabData(
                    index: 1,
                    title: Tab(
                      child: Text("lbl_order".tr,
                          style: CustomTextStyles.titleSmallBluegray900),
                    ),
                    content: Center(
                      child: ProductListWidget(),
                    ),
                  ),
                  TabData(
                    index: 2,
                    title: Tab(
                      child: Text('History',
                          style: CustomTextStyles.titleSmallBluegray900),
                    ),
                    content: HistoryListWidget(),
                  ),
                  TabData(
                    index: 3,
                    title: Tab(
                      child: Text('lbl_payment'.tr,
                          style: CustomTextStyles.titleSmallBluegray900),
                    ),
                    content: PaymentListWidget(),
                  ),
                  TabData(
                    index: 4,
                    title: Tab(
                      child: Text('lbl_info'.tr,
                          style: CustomTextStyles.titleSmallBluegray900),
                    ),
                    content: ShopInfoWidget(),
                  ),
                ],
                isScrollable: isScrollable,
                onTabControllerUpdated: (controller) {
                  print("onTabControllerUpdated");
                },
                onTabChanged: (index) {
                  print("Tab changed: $index");
                },
                onAddTabMoveTo: MoveToTab.last,
                labelPadding: EdgeInsets.only(left: 10.h, right: 10.h),
                showBackIcon: showBackIcon,
                showNextIcon: showNextIcon,
                leading: leading,
                trailing: trailing,
              ),
            ),
            // ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          // final beatList = Get.find<BeatController>();
          // beatList.checkInternetConnectivity();
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "Order",
      ),
      styleType: Style.bgShadow,
    );
  }
}
