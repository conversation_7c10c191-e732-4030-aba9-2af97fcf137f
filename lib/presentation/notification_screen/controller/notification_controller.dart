import 'dart:async';

import 'package:dio/dio.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/sfmDatabase/models/notification_model.dart';

class NotificationController extends GetxController {
  late String formattedDateTime;
  RxBool isEditing = false.obs;

  RxList<NotificationData> notificationList = <NotificationData>[].obs;
  RxBool isLoading = true.obs;
  final Dio dio = Dio();
  RxString token = ''.obs;

  @override
  void onInit() async {
    super.onInit();
    // Initialize formattedDateTime when the controller is initialized
    formattedDateTime = formatDate(DateTime.now().add(Duration(days: 1)));
    await fetchedToken();
    await getNotification();
  }

  // Function to format date
  String formatDate(DateTime date) {
    return DateFormat('dd MMM yyyy hh:mm a').format(date);
  }

  Future<void> fetchedToken() async {
    token.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
    print("token: ${token.value}");
  }

  String convertTimestamp(String timestamp) {
    // Parse the timestamp string to DateTime object
    DateTime dateTime = DateTime.parse(timestamp);

    // Format the DateTime object to the desired format
    String formattedDate = DateFormat('dd MMM yyyy hh:mm a').format(dateTime);

    return formattedDate;
  }

  Future<void> getNotification() async {
    try {
      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getNotificationEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);
            Map<String, dynamic> responseData = response.data;
            List<dynamic> notificationsJson = responseData['notification'];
            notificationList.assignAll(notificationsJson
                .map((json) => NotificationData.fromJson(json))
                .toList());
            print("notificationList: ${notificationList}");
          }
        } else {
          print('Response data is null');
        }
      } catch (e) {
        print('Error parsing response data: $e');
      }
    } catch (e) {
      print(e);
    } finally {
      isLoading(false);
    }
  }
}
