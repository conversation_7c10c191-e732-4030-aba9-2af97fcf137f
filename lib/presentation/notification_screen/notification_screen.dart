// ignore_for_file:

import 'package:sfm_new/sfmDatabase/models/notification_model.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'controller/notification_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

// ignore_for_file: must_be_immutable
class NotificationScreen extends GetWidget<NotificationController> {
  const NotificationScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(context),
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(horizontal: 12.h, vertical: 10.v),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 10.v),
              Expanded(
                child: Obx(
                  () {
                    if (controller.isLoading.value) {
                      return Center(
                        child: CircularProgressIndicator(),
                      );
                    } else if (controller.notificationList.isEmpty) {
                      return Center(
                        child: Text(
                          'No notifications found',
                          style: TextStyle(fontSize: 16),
                        ),
                      );
                    } else {
                      return ListView.builder(
                        physics: ClampingScrollPhysics(),
                        itemCount: controller.notificationList.length,
                        itemBuilder: (context, index) =>
                            CustomNotificationListItem(
                          notification: controller.notificationList[index],
                        ),
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: Obx(() {
          return _buildBottomButtons(context);
        }),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(left: 8.h, top: 18.v, bottom: 18.v),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "Notifications",
      ),
      actions: [],
      styleType: Style.bgShadow,
    );
  }

  Widget _buildBottomButtons(BuildContext context) {
    return Visibility(
      visible: controller.isEditing.value,
      child: Padding(
        padding: EdgeInsets.only(
          left: 12.h,
          right: 12.h,
          bottom: 16.v,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomElevatedButton(
              width: MediaQuery.of(context).size.width * 0.42,
              text: "msg_mark_all_as_read".tr,
              buttonStyle: CustomButtonStyles.outlineGray,
              buttonTextStyle: CustomTextStyles.bodyMediumNotoSansTamilBlack900,
            ),
            CustomElevatedButton(
              width: MediaQuery.of(context).size.width * 0.42,
              text: "lbl_clear_all".tr,
              margin: EdgeInsets.only(left: 10.h),
              buttonStyle: CustomButtonStyles.outlineLightBlue,
              buttonTextStyle:
                  CustomTextStyles.bodyMediumNotoSansTamilOnErrorContainer,
            ),
          ],
        ),
      ),
    );
  }
}

class CustomNotificationListItem extends StatelessWidget {
  final NotificationData notification;
  final NotificationController controller = Get.put(NotificationController());

  CustomNotificationListItem({
    required this.notification,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // Get.toNamed(AppRoutes.notificationThreeScreen);
      },
      child: Container(
        child: Padding(
          padding: EdgeInsets.only(
            // left: 1.h,
            // right: 20.h,
            bottom: 10.h,
          ),
          child: Container(
            decoration: AppDecoration.outlineGray.copyWith(
              borderRadius: BorderRadiusStyle.roundedBorder10,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomImageView(
                  svgPath: ImageConstant.imgPromotion2,
                  height: 25.adaptSize,
                  width: 25.adaptSize,
                  margin: EdgeInsets.only(
                    top: 10.v,
                    left: 6.v,
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(left: 4.h, top: 4, bottom: 8.v),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Padding(
                              padding: EdgeInsets.only(left: 4.h),
                              child: Text(
                                notification.enTitle,
                                style: CustomTextStyles
                                    .titleMediumErrorContainer17,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 2.v),
                        Padding(
                          padding: const EdgeInsets.only(left: 4),
                          child: SizedBox(
                            width:
                                MediaQuery.of(Get.context!).size.width * 0.85,
                            child: Text(notification.enMessage,
                                maxLines: 2,
                                style: CustomTextStyles.bodyMediumGray6000213),
                          ),
                        ),
                        SizedBox(height: 4.v),
                        Padding(
                          padding: EdgeInsets.only(left: 4.h),
                          child: Text(
                            controller.convertTimestamp(notification.createdAt),
                            style: CustomTextStyles.labelLargeOpenSansGray800,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
