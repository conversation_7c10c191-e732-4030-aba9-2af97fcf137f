import 'controller/app_navigation_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';

// ignore_for_file: must_be_immutable
class AppNavigationScreen extends GetWidget<AppNavigationController> {
  const AppNavigationScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Color(0XFFFFFFFF),
        body: SizedBox(
          width: double.maxFinite,
          child: Column(
            children: [
              _buildAppNavigation(),
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Color(0XFFFFFFFF),
                    ),
                    child: Column(
                      children: [
                        _buildScreenTitle(
                          screenTitle: "Home".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Report".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Report Three".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Report Two".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Sync data Six".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Sync data Two".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Report One".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Place Payment Two".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Sync data Seven".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Login".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Super stockist".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Distributors".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Retailer - Tab Container".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Shop Details - Tab Container".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Cheque Payment One - Tab Container".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Add Order".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Order History".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Order Details".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Show Order".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Place Payment".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "No Order".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Complain".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "View Complain".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Add Complain".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Leave".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Add Leave".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Expense".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "View Expense Details".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Add Expense".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Login One".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Attendance".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Home One".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Graph".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Sync data Five".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Sync data Four".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Place Payment One".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "menu One".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Sync data Three".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Sync data".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Sync data One".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "menu".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Payment filter".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Complain filter".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Leave filter".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Expense Filter".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Payment filter One".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Cheque payment".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "RTGS/NEFT/IMPS Payment".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Cash Payment One".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Inventory".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "menu Two".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Retailer shop list".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Retailer Shop Details".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Sync data Eight".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Report Four".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "Notification".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "notificaton Two".tr,
                        ),
                        _buildScreenTitle(
                          screenTitle: "notification Three".tr,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppNavigation() {
    return Container(
      decoration: BoxDecoration(
        color: Color(0XFFFFFFFF),
      ),
      child: Column(
        children: [
          SizedBox(height: 10.v),
          Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.h),
              child: Text(
                "App Navigation".tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color(0XFF000000),
                  fontSize: 20.fSize,
                  fontFamily: 'Roboto',
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
          SizedBox(height: 10.v),
          Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: EdgeInsets.only(left: 20.h),
              child: Text(
                "Check your app's UI from the below demo screens of your app."
                    .tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color(0XFF888888),
                  fontSize: 16.fSize,
                  fontFamily: 'Roboto',
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
          SizedBox(height: 5.v),
          Divider(
            height: 1.v,
            thickness: 1.v,
            color: Color(0XFF000000),
          ),
        ],
      ),
    );
  }

  Widget _buildScreenTitle({required String screenTitle}) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0XFFFFFFFF),
      ),
      child: Column(
        children: [
          SizedBox(height: 10.v),
          Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.h),
              child: Text(
                screenTitle,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color(0XFF000000),
                  fontSize: 20.fSize,
                  fontFamily: 'Roboto',
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
          SizedBox(height: 10.v),
          SizedBox(height: 5.v),
          Divider(
            height: 1.v,
            thickness: 1.v,
            color: Color(0XFF888888),
          ),
        ],
      ),
    );
  }

  /// Common click event
  void onTapScreenTitle(String routeName) {
    Get.toNamed(routeName);
  }
}
