import '../expense_filter_screen/widgets/junior12_item_widget.dart';
import 'controller/expense_filter_controller.dart';
import 'models/junior12_item_model.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

// ignore_for_file: must_be_immutable
class ExpenseFilterScreen extends GetWidget<ExpenseFilterController> {
  const ExpenseFilterScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(
            horizontal: 4.h,
            vertical: 6.v,
          ),
          child: Column(
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: EdgeInsets.only(left: 9.h),
                  child: Text(
                    "lbl_filters".tr,
                    style: theme.textTheme.titleLarge,
                  ),
                ),
              ),
              <PERSON><PERSON><PERSON><PERSON>(height: 21.v),
              _buildJunior(),
              <PERSON><PERSON><PERSON><PERSON>(height: 23.v),
              _buildDate1(),
              <PERSON><PERSON><PERSON><PERSON>(height: 5.v),
            ],
          ),
        ),
        bottomNavigationBar: _buildCancel(),
      ),
    );
  }

  Widget _buildJunior() {
    return Obx(
      () => Wrap(
        runSpacing: 18.v,
        spacing: 18.h,
        children: List<Widget>.generate(
          controller.expenseFilterModelObj.value.junior12ItemList.value.length,
          (index) {
            Junior12ItemModel model = controller
                .expenseFilterModelObj.value.junior12ItemList.value[index];

            return Junior12ItemWidget(
              model,
            );
          },
        ),
      ),
    );
  }

  Widget _buildDate() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "lbl_date".tr,
          textAlign: TextAlign.center,
          style: CustomTextStyles.titleSmallBlack900,
        ),
        SizedBox(height: 9.v),
        Row(
          children: [
            Container(
              width: 154.h,
              decoration: AppDecoration.outlineBlack90013,
              child: Row(
                children: [
                  _buildDateInputColored(
                    augCounter: "lbl_11_aug".tr,
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 17.h,
                top: 11.v,
                bottom: 7.v,
              ),
              child: Text(
                "to",
                style: CustomTextStyles.bodyMediumOpenSansBlack900,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDate1() {
    return Padding(
      padding: EdgeInsets.only(left: 9.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildDate(),
          Padding(
            padding: EdgeInsets.only(top: 30.v),
            child: _buildDateInputColored(
              augCounter: "lbl_18_aug".tr,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCancel() {
    return Padding(
      padding: EdgeInsets.only(
        left: 24.h,
        right: 24.h,
        bottom: 9.v,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomElevatedButton(
            height: 48.v,
            width: 155.h,
            text: "lbl_cancel".tr,
            buttonStyle: CustomButtonStyles.fillOnErrorContainer,
            buttonTextStyle: CustomTextStyles.titleSmallPoppinsOnError,
          ),
          CustomElevatedButton(
            height: 48.v,
            width: 156.h,
            text: "lbl_apply".tr,
            margin: EdgeInsets.only(left: 16.h),
            buttonStyle: CustomButtonStyles.fillPrimaryTL8,
            buttonTextStyle: CustomTextStyles.titleSmallPoppinsOnErrorContainer,
          ),
        ],
      ),
    );
  }

  Widget _buildDateInputColored({required String augCounter}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 14.h,
        vertical: 9.v,
      ),
      decoration: AppDecoration.outlineBlack90014.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder10,
      ),
      child: Row(
        children: [
          CustomImageView(
            svgPath: ImageConstant.imgCalendarPrimary,
            height: 14.v,
            width: 12.h,
            margin: EdgeInsets.symmetric(vertical: 3.v),
          ),
          Padding(
            padding: EdgeInsets.only(
              left: 10.h,
              top: 3.v,
            ),
            child: Text(
              augCounter,
              style: CustomTextStyles.bodySmallOpenSansGray700.copyWith(
                color: appTheme.gray700,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
