import '../models/junior12_item_model.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';

// ignore: must_be_immutable
class Junior12ItemWidget extends StatelessWidget {
  Junior12ItemWidget(
    this.junior12ItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  Junior12ItemModel junior12ItemModelObj;

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Theme(
        data: ThemeData(
          canvasColor: Colors.transparent,
        ),
        child: RawChip(
          padding: EdgeInsets.symmetric(
            horizontal: 16.h,
            vertical: 6.v,
          ),
          showCheckmark: false,
          labelPadding: EdgeInsets.zero,
          label: Text(
            junior12ItemModelObj.junior!.value,
            style: TextStyle(
              color: (junior12ItemModelObj.isSelected?.value ?? false)
                  ? theme.colorScheme.onErrorContainer.withValues(alpha: 1)
                  : appTheme.gray60003,
              fontSize: 14.fSize,
              fontFamily: 'OpenSans',
              fontWeight: FontWeight.w800,
            ),
          ),
          selected: (junior12ItemModelObj.isSelected?.value ?? false),
          backgroundColor: Colors.transparent,
          selectedColor: theme.colorScheme.primary,
          shape: (junior12ItemModelObj.isSelected?.value ?? false)
              ? RoundedRectangleBorder(
                  side: BorderSide.none,
                  borderRadius: BorderRadius.circular(
                    6.h,
                  ),
                )
              : RoundedRectangleBorder(
                  side: BorderSide(
                    color: appTheme.gray300,
                    width: 1.h,
                  ),
                  borderRadius: BorderRadius.circular(
                    6.h,
                  ),
                ),
          onSelected: (value) {
            junior12ItemModelObj.isSelected!.value = value;
          },
        ),
      ),
    );
  }
}
