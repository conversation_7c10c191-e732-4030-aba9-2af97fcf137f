// ignore_for_file: invalid_use_of_protected_member

import 'dart:async';
import 'dart:convert';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/beat_model.dart';
import 'package:sfm_new/sfmDatabase/models/config_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';

enum CustomerType { retailer, distributor, ss, dealer }

class RoutePlanListController extends GetxController
    with GetSingleTickerProviderStateMixin {
  TextEditingController searchController = TextEditingController();

  RxList<Customer> customerList = <Customer>[].obs;
  RxList<Customer> retailerList = <Customer>[].obs;
  RxList<Beat> beatList = <Beat>[].obs;
  RxList<Beat> filteredBeatList = <Beat>[].obs;

  Rx<CustomerType> currentCustomerType = CustomerType.retailer.obs;

  var isLoading = true.obs;
  final searchText = ''.obs;

  late TabController tabviewController =
      Get.put(TabController(vsync: this, length: 3));

  RxList<Config> customerTypeList = <Config>[].obs;
  var customerTypes = <String, dynamic>{}.obs;

  RxList<Config> configList = <Config>[].obs;
  RxString addDealerStatus = ''.obs;
  RxString addDistributorStatus = ''.obs;
  RxInt tabIndexValue = 0.obs;

  bool isCustomerAPISuccess = false;
  RxString employeeToken = "".obs;

  RxInt addRoutePlan = 0.obs;
  RxInt addDistributor = 0.obs;
  RxInt addDealer = 0.obs;

  @override
  void onInit() async {
    super.onInit();

    // EasyLoading.show(status: 'Loading...');

    EasyLoading.dismiss();

    await fetchedToken();
    await fetchConfig();

    final List<ConnectivityResult> connectivityResult =
        await (Connectivity().checkConnectivity());

    if (connectivityResult.contains(ConnectivityResult.none)) {
      showToastMessage("Please check your internet connection");
      isLoading.value = false;
    } else {
      await getRoutePlanListAPI();
    }
  }

  @override
  void onClose() {
    super.onClose();
    searchController.dispose();
  }

  Future<void> getRoutePlanListAPI() async {
    isLoading.value = true;
    try {
      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getRoutePlanListEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> beatRoute =
                List<Map<String, dynamic>>.from(response.data['beat_route']);
            print(beatRoute);

            // Create a new list containing only the desired fields
            beatList.value = beatRoute.map((beat) {
              String beatCode = '';
              String beatName = '';
              int beatStatus = 0;
              String bdmDate = '';
              int bdmNumberwise = 0;
              int bdmType = 0;
              String bdmDay = '';
              int bdmPeriod = 0;
              int beatSyncStatus = 1;

              // Process values
              if (beat['beat'] != null && beat['beat']['beat_code'] != null) {
                try {
                  beatCode = convertValue(beat['beat']['beat_code'], String);
                } catch (e) {
                  print("Error converting beat_code: $e");
                }
              }

              if (beat['beat'] != null && beat['beat']['beat_name'] != null) {
                try {
                  beatName = convertValue(beat['beat']['beat_name'], String);
                } catch (e) {
                  print("Error converting beat_name: $e");
                }
              }

              if (beat['beat'] != null && beat['bdm_status'] != null) {
                try {
                  beatStatus = convertValue(beat['bdm_status'], int);
                } catch (e) {
                  print("Error converting bdm_status: $e");
                }
              }

              if (beat['bdm_date'] != null) {
                try {
                  bdmDate = convertValue(beat['bdm_date'], String);
                } catch (e) {
                  print("Error converting bdm_date: $e");
                }
              }

              if (beat['bdm_numberwise'] != null) {
                try {
                  bdmNumberwise = convertValue(beat['bdm_numberwise'], int);
                } catch (e) {
                  print("Error converting bdm_numberwise: $e");
                }
              }

              if (beat['bdm_type'] != null) {
                try {
                  bdmType = convertValue(beat['bdm_type'], int);
                } catch (e) {
                  print("Error converting bdm_type: $e");
                }
              }

              if (beat['bdm_day'] != null) {
                try {
                  bdmDay = convertValue(beat['bdm_day'], String);
                } catch (e) {
                  print("Error converting bdm_day: $e");
                }
              }

              if (beat['bdm_period'] != null) {
                try {
                  bdmPeriod = convertValue(beat['bdm_period'], int);
                } catch (e) {
                  print("Error converting bdm_period: $e");
                }
              }

              return Beat(
                beat_code: beatCode,
                beat_name: beatName,
                beat_status: beatStatus,
                bdm_date: bdmDate,
                bdm_numberwise: bdmNumberwise,
                bdm_type: bdmType,
                bdm_day: bdmDay,
                bdm_period: bdmPeriod,
                beat_sync_status: beatSyncStatus,
              );
            }).toList();

            print("beatList: ${beatList}");
            filteredBeatList.value = List.from(beatList.value);
          }
        } else {
          print('Response data is null');
        }
      } catch (e) {
        print('Error parsing response data: $e');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: ApiClient.getRoutePlanListEndpoint,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: ApiClient.getRoutePlanListEndpoint,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer

  Future<void> fetchConfig() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableConfig}");
    print(maps);
    customerTypeList.value = List.generate(maps.length, (index) {
      return Config.fromJson(maps[index]);
    });
    print("customerTypeList: ${customerTypeList}");

    if (customerTypeList.isNotEmpty) {
      final customerTypeConfig = customerTypeList.firstWhere(
        (config) => config.config_key == 'customer_types',
      );
      print("customerTypeConfig: ${customerTypeConfig}");
      customerTypes.value = json.decode(customerTypeConfig.config_value!);

      final addRoutePlanConfig = customerTypeList.firstWhere(
        (config) => config.config_key == 'add_route_plan',
      );
      print("addRoutePlanConfig: ${addRoutePlanConfig}");
      addRoutePlan.value = json.decode(addRoutePlanConfig.config_value!);

      final addDistributorConfig = customerTypeList.firstWhere(
        (config) => config.config_key == 'add_route_plan',
      );
      print("addDistributorConfig: ${addDistributorConfig}");
      addDistributor.value = json.decode(addDistributorConfig.config_value!);

      final addDealerConfig = customerTypeList.firstWhere(
        (config) => config.config_key == 'add_route_plan',
      );
      print("addDealerConfig: ${addDealerConfig}");
      addDealer.value = json.decode(addDealerConfig.config_value!);
    }
  }

  bool isCustomerTypeActive(String type) {
    bool isStatus = false;
    print("customerTypes: ${customerTypes}");
    print("type123: ${type}");
    if (customerTypes.isNotEmpty) {
      if (customerTypes[type]['status'] == 1) {
        isStatus = true;
      } else {
        isStatus = false;
      }
    }

    return isStatus;
  }

  void updateSearchText(String newText) {
    searchText.value = newText;
    fetchFilteredBeats(); 
  }

  Future<void> fetchFilteredBeats() async {
    print("beatList search: ${beatList.length}");
    filteredBeatList.value = beatList
        .where((beat) => beat.beat_name!
            .toLowerCase()
            .contains(searchText.value.toLowerCase()))
        .toList();

    print("filteredLeaveList: ${filteredBeatList}");
  }
}

dynamic convertValue(dynamic value, [Type targetType = String]) {
  try {
    if (targetType == int) {
      String stringValue = value is String ? value : value.toString();
      return int.tryParse(stringValue) ?? 0;
    } else if (targetType == double) {
      String stringValue = value is String ? value : value.toString();
      return double.tryParse(stringValue) ?? 0.0;
    } else {
      return value;
    }
  } catch (e) {
    print("Error converting value to $targetType: $e");
    return targetType == int
        ? 0
        : targetType == double
            ? 0.0
            : "";
  }
}
