import 'package:intl/intl.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/route_plan_list_screen/controller/route_plan_list_controller.dart';
import 'package:sfm_new/sfmDatabase/models/beat_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:sfm_new/widgets/custom_icon_button.dart';

import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';

// ignore_for_file: must_be_immutable
class RoutePlanListScreen extends GetWidget<RoutePlanListController> {
  bool isScrollable = false;
  bool showNextIcon = true;
  bool showBackIcon = true;

  Widget? leading;
  Widget? trailing;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(),
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(horizontal: 4.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 8.v),
              CustomSearchBar(customerType: CustomerType.retailer),
              SizedBox(height: 12.v),
              _buildRoutePlanList(),
              SizedBox(height: 12.v),
            ],
          ),
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    // dist = 3 , dealer = 4, retailer = 5

    return CustomIconButton(
      height: 70.adaptSize,
      width: 70.adaptSize,
      padding: EdgeInsets.all(19.h),
      decoration: IconButtonStyleHelper.outlineOnErrorContainer,
      alignment: Alignment.bottomRight,
      child: CustomImageView(
        imagePath: ImageConstant.imgGroup29,
      ),
      onTap: () {
        Get.toNamed(AppRoutes.addRoutePlanScreen);
      },
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(text: "Route Plan"),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildRoutePlanList() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.h),
        child: Obx(
          () {
            if (controller.isLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            } else if (controller.filteredBeatList.isEmpty) {
              return Center(
                child: Text(
                  'No beat found',
                  style: TextStyle(fontSize: 16),
                ),
              );
            } else {
              return ListView.separated(
                physics: BouncingScrollPhysics(),
                shrinkWrap: true,
                separatorBuilder: (
                  context,
                  index,
                ) {
                  return SizedBox(
                    height: 10.v,
                  );
                },
                itemCount: controller.filteredBeatList.length,
                itemBuilder: (context, index) => BeatListItem(
                  beat: controller.filteredBeatList[index],
                ),
              );
            }
          },
        ),
      ),
    );
  }
}

class CustomDealerListItem extends StatelessWidget {
  final Customer customer;
  final RoutePlanListController controller = Get.put(RoutePlanListController());

  CustomDealerListItem({
    required this.customer,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        SharedPrefManager.instance
            .setString(ConstantValues.selectedCustCode, customer.cmCode!);
        SharedPrefManager.instance.setString(
            ConstantValues.selectedCustRelationCode, customer.cmRelationCode!);
        SharedPrefManager.instance
            .setString(ConstantValues.selectedCustName, customer.cmName!);
        SharedPrefManager.instance.setInt(ConstantValues.selectedCustType, 4);
        SharedPrefManager.instance.setBool(ConstantValues.isFromBeat, false);

        Get.toNamed(AppRoutes.productScreen);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 9.h,
          vertical: 15.v,
        ),
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 4.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [],
              ),
            ),
            SizedBox(height: 4.v),
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: customer.cmName,
                    style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                  ),
                ],
              ),
              textAlign: TextAlign.left,
            ),
          ],
        ),
      ),
    );
  }
}

class CustomCustomerListItem extends StatelessWidget {
  final Customer customer;
  final RoutePlanListController controller = Get.put(RoutePlanListController());

  CustomCustomerListItem({
    required this.customer,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        SharedPrefManager.instance
            .setString(ConstantValues.selectedCustCode, customer.cmCode!);

        SharedPrefManager.instance.setString(
            ConstantValues.selectedCustRelationCode, customer.cmRelationCode!);

        SharedPrefManager.instance
            .setString(ConstantValues.selectedCustName, customer.cmName!);

        SharedPrefManager.instance.setInt(ConstantValues.selectedCustType, 3);

        SharedPrefManager.instance.setBool(ConstantValues.isFromBeat, false);

        SharedPrefManager.instance.setInt(ConstantValues.syncDays, 7);

        Get.toNamed(AppRoutes.productScreen);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 9.h,
          vertical: 15.v,
        ),
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 4.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [],
              ),
            ),
            SizedBox(height: 4.v),
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: customer.cmName,
                    style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                  ),
                ],
              ),
              textAlign: TextAlign.left,
            ),
          ],
        ),
      ),
    );
  }
}

//0 = pending, 1 = approved, 2 = reject
class BeatListItem extends StatelessWidget {
  final Beat beat;
  final RoutePlanListController controller = Get.put(RoutePlanListController());

  BeatListItem({
    required this.beat,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 9.h,
          vertical: 15.v,
        ),
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Row(
          children: [
            Flexible(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(right: 4.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [],
                    ),
                  ),
                  Text(
                    "${beat.beat_name?.toUpperCase() ?? ''}",
                    style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                    softWrap: true,
                    overflow: TextOverflow.visible,
                  ),
                  SizedBox(width: 4),
                  Text(
                    beat.bdm_type == 1
                        ? "Route Plan Type : Fixed"
                        : beat.bdm_type == 2
                            ? "Route Plan Type : Daywise"
                            : "Route Plan Type : Datewise",
                    style: CustomTextStyles.labelLargeOpenSansBluegray400,
                  ),
                  SizedBox(width: 4),
                  Visibility(
                    visible: beat.bdm_type == 2,
                    child: Text(
                      "Day : ${beat.bdm_day}",
                      style: CustomTextStyles.labelLargeOpenSansBluegray400,
                    ),
                  ),
                  SizedBox(width: 4),
                  Visibility(
                    visible: beat.bdm_type == 3,
                    child: Text(
                      "Date : ${formatDate('${beat.bdm_date}')}",
                      style: CustomTextStyles.labelLargeOpenSansBluegray400,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 4),
            Text(
              beat.beat_status == 0
                  ? "Pending"
                  : beat.beat_status == 1
                      ? "Approved"
                      : "Rejected",
              style: CustomTextStyles.labelLargeOpenSansBluegray400.copyWith(
                color: beat.beat_status == 0
                    ? Colors.orange
                    : beat.beat_status == 1
                        ? Colors.green
                        : Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String formatDate(String date) {
    if (date.isEmpty) {
      return '';
    }

    try {
      DateTime parsedDate = DateTime.parse(date);
      return DateFormat("dd MMM y").format(parsedDate);
    } catch (e) {
      return '';
    }
  }
}

class CustomSearchBar extends StatelessWidget {
  final CustomerType customerType;

  CustomSearchBar({
    required this.customerType,
  });

  final RoutePlanListController controller = Get.put(RoutePlanListController());

  @override
  Widget build(BuildContext context) {
    controller.currentCustomerType.value = customerType;
    print("customerType: ${controller.currentCustomerType.value}");
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 12),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.5),
              spreadRadius: 2,
              blurRadius: 7,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Icon(
                Icons.search,
                color: Colors.grey,
              ),
            ),
            Expanded(
              child: TextField(
                controller: controller.searchController,
                style: CustomTextStyles.bodyMediumBluegray700,
                decoration: InputDecoration(
                  hintText: 'Search',
                  hintStyle: TextStyle(color: Colors.grey),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 10.0),
                ),
                onChanged: (value) {
                  print('Search value: $value');
                  controller.updateSearchText(value);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
