

import 'dart:convert';

class ApiResponse {
  final String token;
  final bool isCheckedIn;
  final Employee employee;
  final List<TeamMember> teamMembers;

  ApiResponse({
    required this.token,
    required this.isCheckedIn,
    required this.employee,
    required this.teamMembers,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json) {
    return ApiResponse(
      token: json['token'],
      isCheckedIn: json['isCheckedIn'],
      employee: Employee.from<PERSON><PERSON>(json['employee']),
      teamMembers: List<TeamMember>.from(
        json['employee']['team_members']
            .map((teamMemberJson) => TeamMember.fromJson(teamMemberJson)),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'isCheckedIn': isCheckedIn,
      'employee': employee.toJson(),
      'teamMembers': List<dynamic>.from(teamMembers.map((e) => e.toJson())),
    };
  }

  @override
  String toString() {
    return json.encode(toJson());
  }
}

class Employee {
  final String? empCode;
  final String? empName;
  final String? empMobile;
  final String? empEmail;
  final String? reportingEmpCode;
  final int? designationId;
  final int? marketType;
  final int? empStatus;
  final int? empPinStatus;
  final String? deviceInfo;
  final String? expHq;
  final String? expExhq;
  final String? expNighthold;
  final bool? checkIn;
  // final TeamMember? teamMembers;

  Employee({
    this.empCode,
    this.empName,
    this.empMobile,
    this.empEmail,
    this.reportingEmpCode,
    this.designationId,
    this.marketType,
    this.empStatus,
    this.empPinStatus,
    this.deviceInfo,
    this.expHq,
    this.expExhq,
    this.expNighthold,
    this.checkIn,
    // this.teamMembers,
  });

  factory Employee.fromJson(Map<String, dynamic> json) {
    return Employee(
      empCode: json['emp_code'],
      empName: json['emp_name'],
      empMobile: json['emp_mobile'],
      empEmail: json['emp_email'],
      reportingEmpCode: json['reporting_emp_code'],
      designationId: json['designation_id'],
      marketType: json['market_type'],
      empStatus: json['emp_status'],
      empPinStatus: json['emp_pin_status'],
      deviceInfo: json['device_info'],
      expHq: json['exp_hq'],
      expExhq: json['exp_exhq'],
      expNighthold: json['exp_nighthold'],
      checkIn: json['checkIn'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'emp_code': empCode,
      'emp_name': empName,
      'emp_mobile': empMobile,
      'emp_email': empEmail,
      'reporting_emp_code': reportingEmpCode,
      'designation_id': designationId,
      'market_type': marketType,
      'emp_status': empStatus,
      'emp_pin_status': empPinStatus,
      'device_info': deviceInfo,
      'exp_hq': expHq,
      'exp_exhq': expExhq,
      'exp_nighthold': expNighthold,
      'checkIn': checkIn,
    };
  }

  @override
  String toString() {
    return json.encode(toJson());
  }
}

class TeamMember {
  final String? empCode;
  final String? empName;
  final String? empMobile;
  final String? empEmail;
  final String? reportingEmpCode;
  final int? designationId;
  final int? marketType;
  final int? empStatus;
  final int? empPinStatus;
  final String? deviceInfo;
  final String? expHq;
  final String? expExhq;
  final String? expNighthold;

  TeamMember({
    this.empCode,
    this.empName,
    this.empMobile,
    this.empEmail,
    this.reportingEmpCode,
    this.designationId,
    this.marketType,
    this.empStatus,
    this.empPinStatus,
    this.deviceInfo,
    this.expHq,
    this.expExhq,
    this.expNighthold,
  });

  factory TeamMember.fromJson(Map<String, dynamic> json) {
    return TeamMember(
      empCode: json['emp_code'],
      empName: json['emp_name'],
      empMobile: json['emp_mobile'],
      empEmail: json['emp_email'],
      reportingEmpCode: json['reporting_emp_code'],
      designationId: json['designation_id'],
      marketType: json['market_type'],
      empStatus: json['emp_status'],
      empPinStatus: json['emp_pin_status'],
      deviceInfo: json['device_info'],
      expHq: json['exp_hq'],
      expExhq: json['exp_exhq'],
      expNighthold: json['exp_nighthold'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'emp_code': empCode,
      'emp_name': empName,
      'emp_mobile': empMobile,
      'emp_email': empEmail,
      'reporting_emp_code': reportingEmpCode,
      'designation_id': designationId,
      'market_type': marketType,
      'emp_status': empStatus,
      'emp_pin_status': empPinStatus,
      'device_info': deviceInfo,
      'exp_hq': expHq,
      'exp_exhq': expExhq,
      'exp_nighthold': expNighthold,
    };
  }

  @override
  String toString() {
    return json.encode(toJson());
  }
}
