
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';

import 'controller/attendance_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:animated_custom_dropdown/custom_dropdown.dart';

// ignore_for_file: must_be_immutable
class AttendanceScreen extends GetWidget<AttendanceController> {
  const AttendanceScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: SafeArea(
        child: Scaffold(
          body: Obx(
            () => Container(
              width: double.maxFinite,
              padding: EdgeInsets.symmetric(
                horizontal: 13.h,
                vertical: 26.v,
              ),
              child: Column(
                children: [
                  SizedBox(height: 20.v),
                  Text(
                    "Good Morning",
                    style: theme.textTheme.headlineLarge,
                  ),
                  Text(
                    "${controller.employeeName.value}",
                    style: CustomTextStyles.titleLargeBluegray90001Regular,
                  ),
                  SizedBox(height: 63.v),
                  _buildSoloCall(),
                  SizedBox(height: 20.v),
                  _buildJointCall(context),
                  SizedBox(height: 20.v),
                  _buildAbsent(context),
                  SizedBox(height: 20.v),
                  _buildOtherWork(context),
                  Spacer(),
                  SizedBox(height: 15.v),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSoloCall() {
    return GestureDetector(
      onTap: () async {
        controller.getLocation();
        print("Solo call tapped");
        if (controller.lat.value == 0.0) {
          showToastMessage("Please allow location to proceed");
        } else {
          controller.checkinType.value = "1";
          if (controller.accuracy.value == 0.0 ||
              controller.accuracy.value > controller.requiredAccuracy.value ||
              controller.lat.value == 0.0 ||
              controller.long.value == 0.0) {
            await controller.getLocation();
            showToastMessage(
                "Wait while getting your location \n If error persist then set location to high accuracy");
            controller.getLocation();
          } else {
            SharedPrefManager.instance.setInt(ConstantValues.checkInType, 1);
            controller.checkin();
          }
        }
      },
      child: Container(
        margin: EdgeInsets.only(
          left: 8.h,
          right: 8.h,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 10.h,
          vertical: 2.v,
        ),
        decoration: AppDecoration.outlineBlack9009.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder6,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomImageView(
              imagePath: ImageConstant.imgSolo,
              height: 65.v,
              width: 65.h,
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 12.h,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Solo Call",
                    style: CustomTextStyles.titleLargeRegular,
                  ),
                  SizedBox(height: 2.v),
                  Text(
                    "You can add solo call",
                    style: CustomTextStyles.bodyMediumBluegray200,
                  ),
                ],
              ),
            ),
            Spacer(),
            CustomImageView(
              imagePath: ImageConstant.imgRightArrow7,
              height: 15.adaptSize,
              width: 15.adaptSize,
              margin: EdgeInsets.only(
                top: 27.v,
                right: 4.h,
                bottom: 24.v,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJointCall(BuildContext context) {
    return GestureDetector(
      onTap: () {
        controller.getLocation();
        print("Joint call tapped");
        controller.checkinType.value = "2";
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return CustomDialogJointCall();
          },
        );
        // controller.navigateToHomeScreen();
      },
      child: Container(
        margin: EdgeInsets.only(
          left: 8.h,
          right: 8.h,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 10.h,
          vertical: 2.v,
        ),
        decoration: AppDecoration.outlineBlack9009.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder6,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomImageView(
              imagePath: ImageConstant.imgJoin,
              height: 65.v,
              width: 65.h,
            ),
            Padding(
              padding: const EdgeInsets.only(left: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Joint Call",
                    style: CustomTextStyles.titleLargeRegular,
                  ),
                  SizedBox(height: 2.v),
                  Text(
                    "You can add joint call",
                    style: CustomTextStyles.bodyMediumBluegray200,
                  ),
                ],
              ),
            ),
            Spacer(),
            CustomImageView(
              imagePath: ImageConstant.imgRightArrow7,
              height: 15.adaptSize,
              width: 15.adaptSize,
              margin: EdgeInsets.only(
                top: 27.v,
                right: 4.h,
                bottom: 24.v,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAbsent(BuildContext context) {
    return GestureDetector(
      onTap: () {
        controller.getLocation();
        print("Absent tapped");
        controller.checkinType.value = "4";
        controller.leaveController.text = "";
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return CustomDialogAbsent();
          },
        );
        // controller.navigateToHomeScreen();
      },
      child: Container(
        margin: EdgeInsets.only(
          left: 8.h,
          right: 8.h,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 10.h,
          vertical: 2.v,
        ),
        decoration: AppDecoration.outlineBlack9009.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder6,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomImageView(
              imagePath: ImageConstant.imgAbsent,
              height: 65.v,
              width: 65.h,
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 12.h,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Absent",
                    style: CustomTextStyles.titleLargeRegular,
                  ),
                  SizedBox(height: 2.v),
                  Text(
                    "You can add absent",
                    style: CustomTextStyles.bodyMediumBluegray200,
                  ),
                ],
              ),
            ),
            Spacer(),
            CustomImageView(
              imagePath: ImageConstant.imgRightArrow7,
              height: 15.adaptSize,
              width: 15.adaptSize,
              margin: EdgeInsets.only(
                top: 27.v,
                right: 4.h,
                bottom: 24.v,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOtherWork(BuildContext context) {
    return GestureDetector(
      onTap: () {
        controller.getLocation();
        print("Other work tapped");
        controller.checkinType.value = "3";
        controller.leaveController.text = "";
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return CustomDialogOtherWork();
          },
        );
      },
      child: Container(
        margin: EdgeInsets.only(
          left: 8.h,
          right: 8.h,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 10.h,
          vertical: 2.v,
        ),
        decoration: AppDecoration.outlineBlack9009.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder6,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomImageView(
              imagePath: ImageConstant.imgAbsent,
              height: 65.v,
              width: 65.h,
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 12.h,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Other Work",
                    style: CustomTextStyles.titleLargeRegular,
                  ),
                  SizedBox(height: 2.v),
                  Text(
                    "You can add other work",
                    style: CustomTextStyles.bodyMediumBluegray200,
                  ),
                ],
              ),
            ),
            Spacer(),
            CustomImageView(
              imagePath: ImageConstant.imgRightArrow7,
              height: 15.adaptSize,
              width: 15.adaptSize,
              margin: EdgeInsets.only(
                top: 27.v,
                right: 4.h,
                bottom: 24.v,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomDialogAbsent extends StatelessWidget {
  const CustomDialogAbsent({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    AttendanceController controller = Get.put(AttendanceController());

    return Dialog(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: SingleChildScrollView(
        child: Container(
          // width: MediaQuery.of(context).size.width * 0.9,
          padding: EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Absent',
                style: CustomTextStyles.titleSmallBluegray900Bold,
              ),
              SizedBox(height: 8.0),
              Text(
                'Why you are not working today?',
                style: CustomTextStyles.titleSmallBluegray900,
              ),
              SizedBox(height: 16.0),
              CustomTextField(
                controller: controller.leaveController,
                hintText: "Please enter reason",
                height: 100,
              ),
              SizedBox(height: 10.0),
              Container(
                width: 250,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: 110.0,
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: ButtonStyle(
                          side: WidgetStateProperty.all(
                            BorderSide(
                                color: theme.colorScheme.primary,
                                width: 1.0), 
                          ),
                          shape: WidgetStateProperty.all(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  10.0), 
                            ),
                          ),
                          foregroundColor: WidgetStateProperty.all(
                              theme.colorScheme.primary), 
                        ),
                        child: Text(
                          'Cancel',
                          style: CustomTextStyles.titleSmallBluegray900,
                        ),
                      ),
                    ),
                    SizedBox(width: 16.0),
                    SizedBox(
                      width: 110.0,
                      height: 40.0,
                      child: ElevatedButton(
                        onPressed: () async {
                          if (controller.leaveController.text.isEmpty) {
                            showToastMessage("Please enter reason");
                            return;
                          }
                          print("call absent api");
                          controller.checkinType.value = "4";
                          if (controller.accuracy.value == 0.0 ||
                              controller.accuracy.value >
                                  controller.requiredAccuracy.value ||
                              controller.lat.value == 0.0 ||
                              controller.long.value == 0.0) {
                            await controller.getLocation();
                            showToastMessage(
                                "Wait while getting your location \n If error persist then set location to high accuracy");
                            controller.getLocation();
                          } else {
                            SharedPrefManager.instance
                                .setInt(ConstantValues.checkInType, 4);
                            controller.checkin();
                          }
                        },
                        child: Text(
                          'Submit',
                          style: CustomTextStyles.titleSmallBluegray900logout,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomDialogJointCall extends StatelessWidget {
  const CustomDialogJointCall({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    AttendanceController controller = Get.put(AttendanceController());

    return Dialog(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Joint Call',
              style: CustomTextStyles.titleSmallBluegray900Bold,
            ),
            SizedBox(height: 8.0),
            Text(
              'With whom you are working today?',
              style: CustomTextStyles.titleSmallBluegray900,
            ),
            SizedBox(height: 16.0),
            CustomDropdown.search(
              decoration: CustomDropdownDecoration(
                searchFieldDecoration: SearchFieldDecoration(
                    textStyle: CustomTextStyles.titleSmallBluegray900),
                noResultFoundStyle: CustomTextStyles.titleSmallBluegray900,
                listItemStyle: CustomTextStyles.titleSmallBluegray900,
                headerStyle: CustomTextStyles.titleSmallBluegray900,
                expandedFillColor: Colors.white,
                expandedBorder: Border.all(color: Colors.grey[300]!),
                expandedBorderRadius: BorderRadius.circular(12),
                closedBorder: Border.all(color: Colors.grey[300]!),
                closedBorderRadius: BorderRadius.circular(12),
              ),
              hintText: 'Select employee',
              searchHintText: 'Select employee',
              items: controller.teamMembers.map((cm) => cm.empName!).toList(),
              excludeSelected: false,
              initialItem: controller.teamMembers.isNotEmpty
                  ? controller.teamMembers[0].empName
                  : null,
              onChanged: (value) {
                print('changing value to: $value');
                controller.joinEmpCode.value = controller.teamMembers
                    .firstWhere((element) => element.empName == value)
                    .empCode!;
              },
            ),
            SizedBox(height: 10.0),
            Container(
              width: 250,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                    width: 110.0,
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ButtonStyle(
                        side: WidgetStateProperty.all(
                          BorderSide(
                              color: theme.colorScheme.primary,
                              width: 1.0),
                        ),
                        shape: WidgetStateProperty.all(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                                10.0), 
                          ),
                        ),
                        foregroundColor: WidgetStateProperty.all(
                            theme.colorScheme.primary), 
                      ),
                      child: Text(
                        'Cancel',
                        style: CustomTextStyles.titleSmallBluegray900,
                      ),
                    ),
                  ),
                  SizedBox(width: 16.0),
                  SizedBox(
                    width: 110.0,
                    height: 40.0,
                    child: ElevatedButton(
                      onPressed: () async {
                        print("call joint call api");
                        controller.checkinType.value = "2";
                        if (controller.accuracy.value == 0.0 ||
                            controller.accuracy.value >
                                controller.requiredAccuracy.value ||
                            controller.lat.value == 0.0 ||
                            controller.long.value == 0.0) {
                          await controller.getLocation();
                          showToastMessage(
                              "Wait while getting your location \n If error persist then set location to high accuracy");
                          controller.getLocation();
                        } else {
                          SharedPrefManager.instance
                              .setInt(ConstantValues.checkInType, 2);
                          controller.checkin();
                        }
                      },
                      child: Text(
                        'Submit',
                        style: CustomTextStyles.titleSmallBluegray900logout,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomDialogOtherWork extends StatelessWidget {
  const CustomDialogOtherWork({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    AttendanceController controller = Get.put(AttendanceController());

    return Dialog(
      backgroundColor: Colors.white,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Other Work',
                style: CustomTextStyles.titleSmallBluegray900Bold,
              ),
              SizedBox(height: 8.0),
              Text(
                'Please describe your other work',
                style: CustomTextStyles.titleSmallBluegray900,
              ),
              SizedBox(height: 16.0),
              CustomTextField(
                controller: controller.leaveController,
                hintText: "Please enter description",
                height: 100,
              ),
              SizedBox(height: 10.0),
              Container(
                width: 250,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: 110.0,
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: ButtonStyle(
                          side: WidgetStateProperty.all(
                            BorderSide(
                                color: theme.colorScheme.primary,
                                width: 1.0),
                          ),
                          shape: WidgetStateProperty.all(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  10.0), 
                            ),
                          ),
                          foregroundColor: WidgetStateProperty.all(
                              theme.colorScheme.primary),
                        ),
                        child: Text(
                          'Cancel',
                          style: CustomTextStyles.titleSmallBluegray900,
                        ),
                      ),
                    ),
                    SizedBox(width: 16.0),
                    SizedBox(
                      width: 110.0,
                      height: 40.0,
                      child: ElevatedButton(
                        onPressed: () async {
                          if (controller.leaveController.text.isEmpty) {
                            showToastMessage("Please enter description");
                            return;
                          }

                          print("call other work api");
                          controller.checkinType.value = "3";
                          if (controller.accuracy.value == 0.0 ||
                              controller.accuracy.value >
                                  controller.requiredAccuracy.value ||
                              controller.lat.value == 0.0 ||
                              controller.long.value == 0.0) {
                            await controller.getLocation();
                            showToastMessage(
                                "Wait while getting your location \n If error persist then set location to high accuracy");
                            controller.getLocation();
                          } else {
                            SharedPrefManager.instance
                                .setInt(ConstantValues.checkInType, 3);
                            controller.checkin();
                          }
                        },
                        child: Text(
                          'Submit',
                          style: CustomTextStyles.titleSmallBluegray900logout,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;

  CustomTextField({
    required this.controller,
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(10.0)),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextFormField(
        style: CustomTextStyles.titleSmallBluegray900,
        controller: controller,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
          contentPadding: EdgeInsets.symmetric(horizontal: 15.0),
          border: InputBorder.none,
        ),
      ),
    );
  }
}
