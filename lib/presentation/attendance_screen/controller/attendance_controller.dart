import 'dart:async';
import 'dart:convert';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/login_screen/models/login_model.dart';

import 'package:geolocator/geolocator.dart';
import 'package:dio/dio.dart';

import 'package:sfm_new/data/apiClient/api_client.dart';

class AttendanceController extends GetxController {
  TextEditingController leaveController = TextEditingController();

  Rx<UserInfo?> userInfo = Rx<UserInfo?>(null);

  RxList<TeamMember> teamMembers = <TeamMember>[].obs;

  RxString locationMessage = ''.obs;
  RxString checkinType = ''.obs;
  RxString fetchedToken = ''.obs;

  final RxBool isLoading = false.obs;

  final Dio dio = Dio();

  RxDouble lat = 0.0.obs;
  RxDouble long = 0.0.obs;
  RxDouble accuracy = 0.0.obs;
  RxDouble requiredAccuracy = 0.0.obs;

  RxString joinEmpCode = ''.obs;

  RxString employeeName = ''.obs;

  @override
  void onInit() async {
    super.onInit();

    checkinType.value = "1";

    requiredAccuracy.value = await SharedPrefManager.instance
            .getDouble(ConstantValues.requiredAccuracy) ??
        500;

    await getLocation();

    fetchTokenData();
    await fetchEmployeeData();
    fetchTeamMembers();
  }

  void onClose() {
    super.onClose();
    leaveController.dispose();
  }

  void navigateToHomeScreen() {
    EasyLoading.show(status: 'Loading...');
    Get.offNamed(
      AppRoutes.homeScreen,
    );
  }

  void navigateSyncScreen() {
    Get.offNamed(
      AppRoutes.syncDataScreen,
    );
  }

  Future<String?> fetchEmployeeData() async {
    // final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.employee) ??
            "";
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];

      final String? empName = employeeData['emp_name'];
      employeeName.value = empName!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> getLocation() async {
    Position position;
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();

    if (!serviceEnabled) {
      locationMessage.value = "Location services are disabled";
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        locationMessage.value = 'Location permissions are denied.';
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      locationMessage.value =
          'Location permissions are permanently denied, we cannot request permissions.';
      return;
    }

    if (permission == LocationPermission.unableToDetermine) {}

    position = await Geolocator.getCurrentPosition();
    locationMessage.value =
        'Latitude: ${position.latitude}, Longitude: ${position.longitude}';

    lat.value = position.latitude;
    long.value = position.longitude;
    print("position.accuracy: ${position.accuracy}");
    accuracy.value = position.accuracy;
  }

  Future<void> fetchTokenData() async {
    fetchedToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  Future<void> checkin() async {
    try {
      isLoading.value = true;

      print("checkinType: ${checkinType.value}");
      print("lat: ${lat.value}");
      print("long: ${long.value}");
      print("JoinEmpCode: ${joinEmpCode.value}");
      print("leaveController: ${leaveController.text}");

      Map<String, dynamic> data = {};

      if (checkinType.value == '1') {
        data = {
          'checkin_type': '${checkinType.value}',
          'latitude': '${lat.value}',
          'longitude': '${long.value}',
        };
      } else if (checkinType.value == '2') {
        data = {
          'checkin_type': '${checkinType.value}',
          'latitude': '${lat.value}',
          'longitude': '${long.value}',
          'join_emp_code': '${joinEmpCode.value}',
        };
      } else if (checkinType.value == '3' || checkinType.value == '4') {
        data = {
          'checkin_type': '${checkinType.value}',
          'latitude': '${lat.value}',
          'longitude': '${long.value}',
          'remarks': '${leaveController.text}',
        };
      }

      print("data: ${data}");

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${fetchedToken.value}',
      };
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.checkinEndpoint),
        data: data,
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.data);
      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            SharedPrefManager.instance.setBool(
                ConstantValues.isCheckedIn, response.data['isCheckedIn']);

            EasyLoading.showSuccess('Checked in successfully');
            Get.toNamed(AppRoutes.syncDataScreen);
          }
        } else {
          print('Response data is null');
        }
      } catch (e) {
        print('Error parsing response data: $e');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');

        await ApiLogger.logError(
          apiName: 'checkin',
          apiRequestData: null,
          apiResponse: 'DioException: ${e.response}',
        );
      }
    } finally {
      EasyLoading.dismiss();
    }
  }

  Future<List<TeamMember>> fetchTeamMembers() async {
    final String? teamMembersJson = await SharedPrefManager.instance
            .getString(ConstantValues.teamMembers) ??
        "";
    // prefs.getString('team_members');

    if (teamMembersJson != null) {
      List<dynamic> teamMembersData = json.decode(teamMembersJson);
      teamMembers.value = teamMembersData
          .map((memberJson) => TeamMember.fromJson(memberJson))
          .toList();
      print("teamMembers.value: ${teamMembers}");

      if (teamMembers.length > 0) {
        joinEmpCode.value = teamMembers[0].empCode ?? "";
      }

      return teamMembers;
    } else {
      throw Exception('Team members data not found in SharedPreferences');
    }
  }
}
