import 'controller/help_support_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';

// ignore_for_file: must_be_immutable
class HelpSupportScreen extends GetWidget<HelpSupportController> {
  HelpSupportScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              width: double.maxFinite,
              padding: EdgeInsets.symmetric(
                horizontal: 32.h,
                vertical: 15.v,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  SizedBox(height: 40),
                  GestureDetector(
                    onTap: () async {
                      controller.handleTap();
                    },
                    child: CustomImageView(
                        imagePath: ImageConstant.imgAppLogo,
                        fit: BoxFit.contain,
                        height: 75.v,
                        width: 100.h),
                  ),
                  SizedBox(height: 10),
                  Obx(
                    () => Text(
                      'lbl_app_version'.tr +
                          '${controller.appVersion.value}' +
                          ' (${controller.buildVersion.value})',
                      style: CustomTextStyles.bodyMediumBluegray90001,
                    ),
                  ),
                  SizedBox(height: 50),
                  Text(
                    'lbl_for_technical_help'.tr,
                    style: CustomTextStyles.bodyMediumBluegray90001,
                  ),
                  SizedBox(height: 4),
                  Text(
                    'lbl_please_contact_on'.tr,
                    style: CustomTextStyles.bodyMediumBluegray90001,
                  ),
                  SizedBox(height: 8),
                  InkWell(
                    onTap: () => controller.callTechnicalHelp(),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        CustomImageView(
                            svgPath: ImageConstant.imgCall,
                            color: theme.colorScheme.primary,
                            fit: BoxFit.contain,
                            height: 35.v,
                            width: 35.h),
                        SizedBox(width: 4),
                        Text(
                          '${controller.technicalHelpNumber}',
                          style: CustomTextStyles.bodyMediumff004e92,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 20),
                  InkWell(
                    onTap: () => controller.openWhatsApp(),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        CustomImageView(
                            svgPath: ImageConstant.imgWhatsApp,
                            fit: BoxFit.contain,
                            height: 30.v,
                            width: 30.h),
                        SizedBox(width: 4),
                        Text(
                          '${controller.technicalHelpNumber}',
                          style: CustomTextStyles.bodyMediumff004e92,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 20),
                  Text(
                    'lbl_set_location_to_high_accuracy'.tr,
                    style: CustomTextStyles.bodyMediumBluegray90001,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 20),
                  Text(
                    'lbl_keep_map_updated'.tr,
                    style: CustomTextStyles.bodyMediumBluegray90001,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "lbl_help_support".tr,
      ),
      styleType: Style.bgShadow,
    );
  }
}
