// ignore_for_file: unused_local_variable

import 'dart:async';

import 'package:flutter/services.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';

class HelpSupportController extends GetxController {
  // final String appVersion = "1.0.0";
  final String technicalHelpNumber = "+918320677031";
  RxString appVersion = ''.obs;
  RxString buildVersion = ''.obs;
  RxString versionCode = ''.obs;
  final RxString packageName = RxString('');
  final RxBool isLoading = RxBool(false);

  var isLongPress = false.obs;
  Timer? longPressTimer;
  RxInt tapCount = 0.obs;

  @override
  void onInit() async {
    super.onInit();
    fetchAppVersion();
    await getAppInfo();
  }

  @override
  void onClose() {
    super.onClose();
  }

  @override
  void dispose() {
    // Dispose the timer when the controller is disposed
    longPressTimer?.cancel();
    super.dispose();
  }

  void handleTap() async {
    tapCount++;
    if (tapCount.value == 5) {
      print('Image tapped 5 times');
      bool isOpened = await openAppSettings();
      print(isOpened);
      tapCount.value = 0;
    }
  }

  void startLongPressTimer() {
    longPressTimer = Timer(Duration(seconds: 5), () {
      isLongPress.value = true;
    });
  }

  void resetLongPressTimer() {
    longPressTimer?.cancel();
    isLongPress.value = false;
  }

  Future<void> getAppInfo() async {
    isLoading.value = true; 
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      packageName.value = packageInfo.packageName;
      print('packageName: ${packageName.value}');
    } on PlatformException catch (error) {
      print('Error getting package info: $error');
      Get.snackbar(
        'Error',
        'Failed to retrieve app information.',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false; 
    }
  }

  Future<void> openAppInfo() async {
    if (packageName.isEmpty) return;
    final url = 'package:${packageName.value}';
    print("url: ${url}");
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      Get.snackbar(
        'Error',
        'Could not launch app info screen.',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  void callTechnicalHelp() async {
    final Uri url = Uri.parse("tel:$technicalHelpNumber");
    if (!await launchUrl(url)) {
      throw Exception("Could not launch $url");
    }
  }

  void openWhatsApp() async {
    final Uri url = Uri.parse("https://wa.me/$technicalHelpNumber");
    if (!await launchUrl(url)) {
      throw Exception("Could not launch $url");
    }
  }

  Future<void> fetchAppVersion() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      PackageInfo packageInfo1 = await PackageInfo.fromPlatform();
      appVersion.value = packageInfo.version;
      buildVersion.value = packageInfo.buildNumber;
      versionCode.value = packageInfo.version;
    } catch (error) {
      print('Error fetching app version: $error');
    }
  }
}
