// ignore_for_file: unused_local_variable, invalid_use_of_protected_member, unnecessary_null_comparison

import 'dart:convert';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/add_complain_screen/models/add_complain_model.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sfm_new/presentation/complain_screen/controller/complain_controller.dart';

import 'dart:async';
import 'dart:io';

import 'package:image/image.dart' as img;
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/claim_complain_model.dart';
import 'package:sfm_new/sfmDatabase/models/claim_complain_type_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';

class Pair {
  final String text;
  final IconData icon;
  const Pair(this.text, this.icon);

  @override
  String toString() {
    return text;
  }
}

class AddComplainController extends GetxController {
  TextEditingController claimComplainController = TextEditingController();
  TextEditingController claimComplainTypeController = TextEditingController();
  TextEditingController clientTypeController = TextEditingController();
  TextEditingController clientNameController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();

  Rx<AddComplainModel> addComplainModelObj = AddComplainModel().obs;
  SelectionPopupModel? selectedDropDownValue;
  SelectionPopupModel? selectedDropDownValue1;

  final ImagePicker _picker = ImagePicker();

  Rx<File?> imageFile = Rx<File?>(null);
  String storedBase64Image = "";

  RxList<ClaimComplainType> complainTypeList = <ClaimComplainType>[].obs;
  RxList<Customer> customerList = <Customer>[].obs;

  List<String> claimComplainList = ['Claim', 'Complain'];

  RxInt selectedClaimComplain = 0.obs;
  RxInt selectedCCType = 0.obs;
  RxInt selectedClientType = 0.obs;
  RxString selectedClientName = ''.obs;

  RxString employeeCode = ''.obs;
  RxString employeeToken = "".obs;

  final PermissionController permissionController =
      Get.put(PermissionController());

  List<String> clientTypeList = [
    'SS',
    'Distributor',
    'Dealer',
    'Retailer',
  ];

  final Rx<String?> base64Image = Rx(null);

  var isLoading = false.obs;

  // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer

  @override
  void onInit() async {
    super.onInit();

    EasyLoading.show(status: 'Loading...');

    selectedClaimComplain.value = 0;
    selectedCCType.value = 0;
    selectedClientType.value = 0;
    selectedClientName.value = '1';

    await fetchCustomers();
    await fetchClaimComplainType();
    await fetchEmployeeData();
    await fetchedToken();
  }

  @override
  void onClose() {
    super.onClose();
    claimComplainController.dispose();
  }

  Future<void> pickImage(ImageSource source) async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      File compressedImage = await compressImage(File(image.path));
      imageFile.value = compressedImage;

      imageToBase64(imageFile.value!);
    }
  }

  Future<File> compressImage(File imageFile) async {
    int maxSize = 500; 
    int quality = 75; 

    List<int> compressedBytes = await FlutterImageCompress.compressWithList(
      imageFile.readAsBytesSync(),
      minHeight: 1920,
      minWidth: 1080,
      quality: quality,
    );

    return File(imageFile.path)..writeAsBytesSync(compressedBytes);
  }

  Future<void> fetchClaimComplainType() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableClaimComplainType}");
    print(maps);
    complainTypeList.value = List.generate(maps.length, (index) {
      return ClaimComplainType.fromMap(maps[index]);
    });
    print(complainTypeList.value);
  }

  Future<void> fetchCustomers() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCustomer}");
    print(maps);
    customerList.value = List.generate(maps.length, (index) {
      return Customer.fromMap(maps[index]);
    });
    print(customerList.value);
  }

  // Function to return id where selectedType == cct_type_name
  int? getClaimComplainIdFromTypeName(String selectedType) {
    try {
      final claimComplainType = complainTypeList.firstWhere(
        (ccType) => ccType.cct_type_name == selectedType,
      );
      return claimComplainType.cct_id;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getClaimComplainIdFromTypeName: $e');
      }
      return null;
    }
  }

  String? getCustomerCodeFromTypeName(String selectedType) {
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmName == selectedType,
      );
      return customerType.cmCode;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerCodeFromTypeName: $e');
      }
      return null;
    }
  }

  // Function to fetch user data from shared preferences
  Future<Map<String, dynamic>?> getUserData() async {
    String? userDataString =
        await SharedPrefManager.instance.getString(ConstantValues.userData) ??
            "";
    if (userDataString != null) {
      return Map<String, dynamic>.from(
        Map<String, dynamic>.fromIterable(
          userDataString.replaceAll('{', '').replaceAll('}', '').split(','),
          key: (e) => e.split(':')[0].trim(),
          value: (e) => e.split(':')[1].trim(),
        ),
      );
    } else {
      return null;
    }
  }

  String generateUUID() {
    var uuid = Uuid();
    return uuid.v4();
  }

  String generateUUID123() {
    var uuid = Uuid();
    return uuid.v4();
  }

  Future<String?> fetchEmployeeData() async {
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.employee) ??
            "";
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];
      employeeCode.value = empCode!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> fetchedToken() async {
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
    if (employeeDataJson != null) {
      employeeToken.value = employeeDataJson;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> addComplainToDB() async {
    final db = await DatabaseProvider.database;

    String formattedDateTime =
        DateFormat("yyyy-MM-ddTHH:mm:ss.000000'Z'").format(DateTime.now());
    print(formattedDateTime);

    DateTime now = DateTime.now();
    String dateString = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);

    String uuid = generateUUID();

    print("uuid: ${uuid}");
    print("ccTypeID: ${selectedClaimComplain.value}");
    print("ccType: ${selectedCCType.value}");
    print("ccType2: ${selectedClientType.value}");

//

    var complain = ClaimComplain(
      ccCode: uuid,
      ccTypeID: selectedClaimComplain.value,
      ccNote: descriptionController.text,
      ccCMCode: selectedClientName.value,
      ccEmpCode: employeeCode.value,
      ccType: selectedCCType.value,
      ccStatus: 0,
      ccRemarks: '',
      ccCreatedAt: dateString,
      ccUpdatedAt: dateString,
      syncStatus: 0,
      ccImage: storedBase64Image,
    );

    print('ccUrmID: ${selectedClientName.value}');

    print("complain added to db: ${complain}");

    try {
      await db.insert(
        '${TableValues.tableClaimComplain}',
        complain.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      final expense = Get.find<ComplainController>();
      await expense.checkInternetConnectivity();

      showToastMessage("Complain added successfully");

      Get.back();
    } catch (e) {
      print("Error adding complain to database: $e");

      // Log the error to a log file
      await ApiLogger.logError(
        apiName: 'addComplainToDB',
        apiRequestData: complain.toMap(),
        apiResponse: 'Error: $e',
      );

      showToastMessage("Failed to add complain. Please try again.");
    }
  }

  Future<String> imageToBase64(File imageFile) async {

    Uint8List imageBytes = await imageFile.readAsBytes();

    // Decode the image
    img.Image? image = img.decodeImage(imageBytes);

    if (image != null) {
      // Compress the image if its size exceeds 500KB
      if (image.lengthInBytes > 500 * 1024) {
        image = img.copyResize(image,
            width: image.width ~/ 2, height: image.height ~/ 2);
      }

      // Convert the image to base64 format
      String base64Image = base64Encode(img.encodePng(image));
      storedBase64Image = base64Image;
      print("storedBase64Image:- ${storedBase64Image}");
      return base64Image;
    } else {
      throw Exception('Failed to decode the image');
    }
  }

  Future<void> pickImageFromGallery() async {
    final ImagePicker _picker = ImagePicker();
    final XFile? pickedImage =
        await _picker.pickImage(source: ImageSource.gallery);

    if (pickedImage != null) {
      final File selectedImage = File(pickedImage.path);
      // Add your logic to handle the selected image here.
    }
  }

  void showPermissionDeniedDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('Permission Denied'),
        content: Text('Please grant contact permission in app settings.'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: Text('Open Settings'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void showPermissionDeniedDialogGallery() {
    Get.dialog(
      AlertDialog(
        title: Text('Permission Denied'),
        content: Text('Please grant permission in app settings.'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: Text('Open Settings'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  //***************************************************************************/
  Future<void> pickImageNew(ImageSource source) async {
    // Check if appropriate permission is granted

    EasyLoading.show(status: 'Loading...');
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    print("androidInfo.version.sdkInt: ${androidInfo.version.sdkInt}");

    // if (androidInfo.version.sdkInt >= 33) {

    bool isPermissionGranted = source == ImageSource.camera
        ? permissionController.isCameraPermissionGranted.value
        : androidInfo.version.sdkInt >= 33
            ? permissionController.isGalleryPermissionGranted.value
            : permissionController.isStoragePermissionGranted.value;

    if (!isPermissionGranted) {
      // Show permission request dialog if not granted
      await permissionController.checkAndRequestPermissions();

      // Recheck permission
      isPermissionGranted = source == ImageSource.camera
          ? permissionController.isCameraPermissionGranted.value
          : androidInfo.version.sdkInt >= 33
              ? permissionController.isGalleryPermissionGranted.value
              : permissionController.isStoragePermissionGranted.value;

      EasyLoading.dismiss();

      if (!isPermissionGranted) {
        showToastMessage(
            'Permission Denied \n Cannot proceed without required permissions');
        return;
      }
    }

    try {
      final pickedFile = await ImagePicker().pickImage(source: source);
      if (pickedFile != null) {
        imageFile.value = File(pickedFile.path);
        File compressedImage = await compressImage(File(pickedFile.path));
        imageFile.value = compressedImage;
        imageToBase64(imageFile.value!);
      }
      EasyLoading.dismiss();
    } catch (e) {
      showToastMessage('Failed to pick image: ${e.toString()}');
      EasyLoading.dismiss();
    }
  }
}

class PermissionController extends GetxController {
  RxBool isCameraPermissionGranted = false.obs;
  RxBool isGalleryPermissionGranted = false.obs;
  RxBool isStoragePermissionGranted = false.obs;

  @override
  void onInit() {
    super.onInit();
    checkAndRequestPermissions();
  }

  Future<void> checkAndRequestPermissions() async {
    // Check Camera Permission
    PermissionStatus cameraStatus = await Permission.camera.status;
    isCameraPermissionGranted.value = cameraStatus.isGranted;

    // Check Gallery/Storage Permission
    PermissionStatus storageStatus = await Permission.storage.status;
    isGalleryPermissionGranted.value = storageStatus.isGranted;

    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    print("androidInfo.version.sdkInt: ${androidInfo.version.sdkInt}");

    if (androidInfo.version.sdkInt >= 33) {
      // isVideosPermission = await Permission.videos.status.isGranted;
      isGalleryPermissionGranted.value =
          await Permission.photos.status.isGranted;
      // If either permission is not granted, show permission request dialog
      if (!isCameraPermissionGranted.value ||
          !isGalleryPermissionGranted.value) {
        await _showPermissionRequestDialog();
      }
    } else {
      isStoragePermissionGranted.value =
          await Permission.storage.status.isGranted;

      // If either permission is not granted, show permission request dialog
      if (!isCameraPermissionGranted.value ||
          !isStoragePermissionGranted.value) {
        await _showPermissionRequestDialog();
      }
    }
  }

  Future<void> _showPermissionRequestDialog() async {
    bool? result = await Get.dialog<bool>(
      AlertDialog(
        title: Text('Permissions Required'),
        content: Text(
            'This app needs camera and storage permissions to select and take photos.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text('Deny'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text('Allow'),
          ),
        ],
      ),
    );

    if (result == true) {
      // Request permissions
      await _requestPermissions();
    } else {
      // User denied permissions
      _handlePermissionDenied();
    }
  }

  Future<void> _requestPermissions() async {
    // Request Camera Permission
    PermissionStatus cameraStatus = await Permission.camera.request();
    isCameraPermissionGranted.value = cameraStatus.isGranted;

    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

    if (androidInfo.version.sdkInt >= 33) {
      // Request Storage Permission
      PermissionStatus photosStatus = await Permission.photos.request();
      isGalleryPermissionGranted.value = photosStatus.isGranted;

      // Check if both permissions are granted
      if (!isCameraPermissionGranted.value ||
          !isGalleryPermissionGranted.value) {
        _showPermissionDeniedSnackbar();
      }
    } else {
      // Request Storage Permission
      PermissionStatus storageStatus = await Permission.storage.request();
      isGalleryPermissionGranted.value = storageStatus.isGranted;

      // Check if both permissions are granted
      if (!isCameraPermissionGranted.value ||
          !isGalleryPermissionGranted.value) {
        _showPermissionDeniedSnackbar();
      }
    }
  }

  void _handlePermissionDenied() {
    Get.snackbar(
      'Permissions Denied',
      'Some features will be limited without permissions',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange,
      colorText: Colors.white,
    );
  }

  void _showPermissionDeniedSnackbar() {
    Get.snackbar(
      'Permissions Incomplete',
      'Please grant all permissions in app settings',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      mainButton: TextButton(
        onPressed: () => openAppSettings(),
        child: Text('Open Settings'),
      ),
    );
  }
}
