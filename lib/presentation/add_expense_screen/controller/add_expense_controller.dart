// ignore_for_file: unused_local_variable, unnecessary_null_comparison, body_might_complete_normally_nullable

import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:geolocator/geolocator.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/add_expense_screen/models/add_expense_model.dart';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:sfm_new/presentation/expense_screen/controller/expense_controller.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/config_model.dart';
import 'package:sfm_new/sfmDatabase/models/expense_model.dart';
import 'package:sfm_new/sfmDatabase/models/expense_type_model.dart';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';


class AddExpenseController extends GetxController {
  TextEditingController detailsController = TextEditingController();

  TextEditingController amountController = TextEditingController();

  Rx<AddExpenseModel> addExpenseModelObj = AddExpenseModel().obs;

  SelectionPopupModel? selectedDropDownValue;

  final ImagePicker _picker = ImagePicker();
  // Rx<XFile?> imageFile = Rx<XFile?>(null);
  Rx<File?> imageFile = Rx<File?>(null);
  String storedBase64Image = "";
  String fileExtension = "jpg";
  // var selectedFiles = <XFile>[].obs;
  var selectedFile = Rx<XFile?>(null);

  RxList<ExpenseType> expenseTypeList = <ExpenseType>[].obs;

  RxBool isExpenseTypeSelected = false.obs;

  RxInt selectedExpenseType = 0.obs;
  RxString employeeCode = ''.obs;

  RxDouble lat = 0.0.obs;
  RxDouble long = 0.0.obs;
  RxString locationMessage = ''.obs;
  RxList<Config> configList = <Config>[].obs;

  RxString expenseAmountForReqImage = ''.obs;

  RxInt pdfSelected = 0.obs;
  RxString pdfFileName = ''.obs;

  final PermissionController permissionController =
      Get.put(PermissionController());

  // exp_min_amount_req_image

  @override
  void onInit() async {
    super.onInit();

    getLocation();

    await fetchExpenseType();
    await fetchEmployeeData();

    await fetchConfig();
  }

  Future<void> fetchExpenseType() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableExpenseType}");
    expenseTypeList.value = List.generate(maps.length, (index) {
      print("expenseTypeList: ${expenseTypeList}");
      return ExpenseType.fromMap(maps[index]);
    });
  }

  Future<void> fetchConfig() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableConfig}");
    print(maps);
    configList.value = List.generate(maps.length, (index) {
      return Config.fromJson(maps[index]);
    });
    print("configList: ${configList}");

    if (configList.length != 0) {
      final amountReqImg = configList.firstWhere(
        (config) => config.config_key == 'exp_min_amount_req_image',
      );

      if (amountReqImg != null) {
        print('amountReqImg fetched');
        print("amountReqImg: ${amountReqImg.config_value}");
        expenseAmountForReqImage.value = amountReqImg.config_value ?? "";
      }
    }
  }

  @override
  void onClose() {
    super.onClose();
    detailsController.dispose();
    amountController.dispose();
  }

  onSelected(dynamic value) {
    for (var element in addExpenseModelObj.value.dropdownItemList.value) {
      element.isSelected = false;
      if (element.id == value.id) {
        element.isSelected = true;
      }
    }
    addExpenseModelObj.value.dropdownItemList.refresh();
  }

  Future<void> getLocation() async {
    Position position;
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();

    if (!serviceEnabled) {
      locationMessage.value = "Location services are disabled";
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        locationMessage.value = 'Location permissions are denied.';
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      locationMessage.value =
          'Location permissions are permanently denied, we cannot request permissions.';
      return;
    }

    if (permission == LocationPermission.unableToDetermine) {}

    position = await Geolocator.getCurrentPosition();
    locationMessage.value =
        'Latitude: ${position.latitude}, Longitude: ${position.longitude}';

    lat.value = position.latitude;
    long.value = position.longitude;
  }

  String generateUUID() {
    var uuid = Uuid();
    return uuid.v4();
  }

  Future<String?> fetchEmployeeData() async {
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.employee) ??
            "";
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];
      employeeCode.value = empCode!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> addExpenseToDB() async {
    final db = await DatabaseProvider.database;

    String formattedDateTime = DateFormat("yyyy-MM-dd").format(DateTime.now());
    print(formattedDateTime);

    DateTime now = DateTime.now();
    String dateString = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);

    String uuid = generateUUID();

    if (imageFile.value == null && pdfSelected.value == 0) {
      storedBase64Image = "";
    }

    print("storedBase64Image: ${storedBase64Image}");

    print("expenseType: ${selectedExpenseType.value}");
    print("expenseCode: ${uuid}");
    print("expenseDetail: ${detailsController.text}");
    print("amount: ${double.parse(amountController.text)}");
    print("approvedAmount: 0");
    print("billPicture: ${storedBase64Image}");
    print("fileExtension: ${fileExtension}");
    print("longitude: ${long.value}");
    print("latitude: ${lat.value}");
    print("employeeCode: ${employeeCode.value}");
    print("expenseStatus: 0");
    print("date: ${formattedDateTime}");
    print("kilometers: 0");

    var addExpense = Expense(
      expenseType: selectedExpenseType.value,
      expenseCode: uuid, 
      expenseDetail: detailsController.text,
      amount: double.parse(
          amountController.text), 
      approvedAmount: 0,
      billPicture: storedBase64Image,
      fileExtension: fileExtension,
      longitude: long.value,
      latitude: lat.value,
      employeeCode: employeeCode.value,
      expenseStatus: 0,
      date: dateString,
      kilometers: "0",
      syncStatus: 0,
    );

    print("addExpense: ${addExpense}");

    try {
      await db.insert(
        '${TableValues.tableExpense}',
        addExpense.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      final expense = Get.find<ExpenseController>();
      await expense.checkInternetConnectivity();

      showToastMessage("Expense added successfully");

      Get.back();
    } catch (e) {
      print("Error adding expense to database: $e");

      // Log the error to a log file
      await ApiLogger.logError(
        apiName: 'addExpenseToDB',
        apiRequestData: addExpense.toMap(),
        apiResponse: 'Error: $e',
      );

      showToastMessage("Failed to add expense. Please try again.");
    }
  }

  int? getExpenseCodeFromTypeName(String selectedType) {
    try {
      final expenseType = expenseTypeList.firstWhere(
        (ccType) => ccType.expenseName == selectedType,
      );
      return expenseType.id;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getExpenseCodeFromTypeName: $e');
      }
      return null;
    }
  }

  Future<void> pickImage(ImageSource source) async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      File compressedImage = await compressImage(File(image.path));
      imageFile.value = compressedImage;
      imageToBase64(imageFile.value!);
    }
  }

  Future<File> compressImage(File imageFile) async {
    int maxSize = 250; 
    int quality = 40; 

    List<int> compressedBytes = await FlutterImageCompress.compressWithList(
      imageFile.readAsBytesSync(),
      minHeight: 1920, 
      minWidth: 1080,
      quality: quality,
      // maxSize: maxSize * 1024,
    );

    return File(imageFile.path)..writeAsBytesSync(compressedBytes);
  }

//*****************************************************************************************/

  Future<void> pickImageNew(ImageSource source) async {
    // Check if appropriate permission is granted
    EasyLoading.show(status: 'Loading...');
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    print("androidInfo.version.sdkInt: ${androidInfo.version.sdkInt}");

    // if (androidInfo.version.sdkInt >= 33) {

    bool isPermissionGranted = source == ImageSource.camera
        ? permissionController.isCameraPermissionGranted.value
        : androidInfo.version.sdkInt >= 33
            ? permissionController.isGalleryPermissionGranted.value
            : permissionController.isStoragePermissionGranted.value;

    if (!isPermissionGranted) {
      // Show permission request dialog if not granted
      await permissionController.checkAndRequestPermissions();

      // Recheck permission
      isPermissionGranted = source == ImageSource.camera
          ? permissionController.isCameraPermissionGranted.value
          : androidInfo.version.sdkInt >= 33
              ? permissionController.isGalleryPermissionGranted.value
              : permissionController.isStoragePermissionGranted.value;

      EasyLoading.dismiss();

      if (!isPermissionGranted) {
        showToastMessage(
            'Permission Denied \n Cannot proceed without required permissions');
        return;
      }
    }

    try {
      final pickedFile = await ImagePicker().pickImage(source: source);
      if (pickedFile != null) {
        imageFile.value = File(pickedFile.path);
        File compressedImage = await compressImage(File(pickedFile.path));
        imageFile.value = compressedImage;
        imageToBase64(imageFile.value!);
      }
      EasyLoading.dismiss();
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to pick image: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      EasyLoading.dismiss();
    }
  }

  Future<String> imageToBase64Files(XFile file) async {
    try {
      // Read the file as bytes
      Uint8List fileBytes = await file.readAsBytes();
      print("fileBytes: ${fileBytes}");

      // Check if the file is a PDF
      // if (file.path.toLowerCase().endsWith('.pdf')) {
      // Check if the file size exceeds 500KB
      if (fileBytes.length > 400 * 1024) {
        print("fileBytes.length: ${fileBytes.length}");
        // Perform PDF compression
        // For demonstration, let's assume we compress the PDF bytes here
        // Replace this with your actual PDF compression logic
        // compressedFileBytes = yourPDFCompressionFunction(fileBytes);
        // Here, we are just reducing the file size by 50% for demonstration
        fileBytes =
            Uint8List.fromList(fileBytes.map((byte) => byte ~/ 2).toList());
      }
      // }

      // Convert the bytes to base64 format
      String base64Data = base64Encode(fileBytes);

      print("Base64 Data: $base64Data");

      // Update the stored base64 image (for your application logic)
      storedBase64Image = base64Data;

      // Return the base64 data
      return base64Data;
    } catch (e) {
      // Handle any errors that occur during the process
      print("Error converting file to base64: $e");
      // Return an empty string or throw an exception, based on your requirements
      return '';
    }
  }

  Future<String> imageToBase64(File imageFile) async {
    // Read the image file
    // List<int> imageBytes = await imageFile.readAsBytes();

    Uint8List imageBytes = await imageFile.readAsBytes();

    // Decode the image
    img.Image? image = img.decodeImage(imageBytes);

    if (image != null) {
      // Compress the image if its size exceeds 500KB
      if (image.lengthInBytes > 400 * 1024) {
        image = img.copyResize(image,
            width: image.width ~/ 2, height: image.height ~/ 2);
      }

      // Convert the image to base64 format
      String base64Image = base64Encode(img.encodePng(image));
      storedBase64Image = base64Image;
      print("storedBase64Image:- ${storedBase64Image}");
      return base64Image;
    } else {
      throw Exception('Failed to decode the image');
    }
  }

  Future<void> pickImageFromGallery() async {
    final ImagePicker _picker = ImagePicker();
    final XFile? pickedImage =
        await _picker.pickImage(source: ImageSource.gallery);

    if (pickedImage != null) {
      final File selectedImage = File(pickedImage.path);
      // Add your logic to handle the selected image here.
    }
  }

  void showPermissionDeniedDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('Permission Denied'),
        content: Text('Please grant contact permission in app settings.'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: Text('Open Settings'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void showPermissionDeniedDialogGallery() {
    Get.dialog(
      AlertDialog(
        title: Text('Permission Denied'),
        content: Text('Please grant permission in app settings.'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: Text('Open Settings'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

class PermissionController extends GetxController {
  RxBool isCameraPermissionGranted = false.obs;
  RxBool isGalleryPermissionGranted = false.obs;
  RxBool isStoragePermissionGranted = false.obs;

  @override
  void onInit() {
    super.onInit();
    checkAndRequestPermissions();
  }

  Future<void> checkAndRequestPermissions() async {
    // Check Camera Permission
    PermissionStatus cameraStatus = await Permission.camera.status;
    isCameraPermissionGranted.value = cameraStatus.isGranted;

    // Check Gallery/Storage Permission
    PermissionStatus storageStatus = await Permission.storage.status;
    isGalleryPermissionGranted.value = storageStatus.isGranted;

    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    print("androidInfo.version.sdkInt: ${androidInfo.version.sdkInt}");

    if (androidInfo.version.sdkInt >= 33) {
      // isVideosPermission = await Permission.videos.status.isGranted;
      isGalleryPermissionGranted.value =
          await Permission.photos.status.isGranted;
      // If either permission is not granted, show permission request dialog
      if (!isCameraPermissionGranted.value ||
          !isGalleryPermissionGranted.value) {
        await _showPermissionRequestDialog();
      }
    } else {
      isStoragePermissionGranted.value =
          await Permission.storage.status.isGranted;

      // If either permission is not granted, show permission request dialog
      if (!isCameraPermissionGranted.value ||
          !isStoragePermissionGranted.value) {
        await _showPermissionRequestDialog();
      }
    }
  }

  Future<void> _showPermissionRequestDialog() async {
    bool? result = await Get.dialog<bool>(
      AlertDialog(
        title: Text('Permissions Required'),
        content: Text(
            'This app needs camera and storage permissions to select and take photos.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: Text('Deny'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: Text('Allow'),
          ),
        ],
      ),
    );

    if (result == true) {
      // Request permissions
      await _requestPermissions();
    } else {
      // User denied permissions
      _handlePermissionDenied();
    }
  }

  Future<void> _requestPermissions() async {
    // Request Camera Permission
    PermissionStatus cameraStatus = await Permission.camera.request();
    isCameraPermissionGranted.value = cameraStatus.isGranted;

    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

    if (androidInfo.version.sdkInt >= 33) {
      // Request Storage Permission
      PermissionStatus photosStatus = await Permission.photos.request();
      isGalleryPermissionGranted.value = photosStatus.isGranted;

      // Check if both permissions are granted
      if (!isCameraPermissionGranted.value ||
          !isGalleryPermissionGranted.value) {
        _showPermissionDeniedSnackbar();
      }
    } else {
      // Request Storage Permission
      PermissionStatus storageStatus = await Permission.storage.request();
      isGalleryPermissionGranted.value = storageStatus.isGranted;

      // Check if both permissions are granted
      if (!isCameraPermissionGranted.value ||
          !isGalleryPermissionGranted.value) {
        _showPermissionDeniedSnackbar();
      }
    }
  }

  void _handlePermissionDenied() {
    Get.snackbar(
      'Permissions Denied',
      'Some features will be limited without permissions',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.orange,
      colorText: Colors.white,
    );
  }

  void _showPermissionDeniedSnackbar() {
    Get.snackbar(
      'Permissions Incomplete',
      'Please grant all permissions in app settings',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      mainButton: TextButton(
        onPressed: () => openAppSettings(),
        child: Text('Open Settings'),
      ),
    );
  }
}
