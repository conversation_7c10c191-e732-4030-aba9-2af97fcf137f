// ignore_for_file: invalid_use_of_protected_member

import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:sfm_new/sfmDatabase/models/payment_collection_model.dart';
import 'package:sfm_new/sfmDatabase/models/payment_type_model.dart';

enum PaymentFilterType { all, cash, cheque, netbanking }

enum PaymentStatus { all, pending, approved, rejected }

class PaymentController extends GetxController {
  final searchText = ''.obs;

  RxList<Payment> paymentList = <Payment>[].obs;
  RxList<PaymentType> paymentTypeList = <PaymentType>[].obs;
  RxList<Customer> customerList = <Customer>[].obs;

  RxList<Payment> filteredPaymentList = <Payment>[].obs;

  RxSet<PaymentFilterType> currentPaymentTypes =
      <PaymentFilterType>{PaymentFilterType.all}.obs;
  RxSet<PaymentStatus> currentPaymentStatuses =
      <PaymentStatus>{PaymentStatus.all}.obs;

  var selectedOptions = <String>[].obs;

  late Rx<DateTime> fromDate;
  late Rx<DateTime> toDate;

  RxString formattedFromDate = ''.obs;
  RxString formattedToDate = ''.obs;

  RxString filterFromDate = ''.obs;
  RxString filterToDate = ''.obs;

  static PaymentController get instance => Get.find();

  final Connectivity _connectivity = Connectivity();

  RxString employeeToken = "".obs;
  RxDouble totalAmount = 0.0.obs;
  bool isPaymentAPISuccess = false;

  RxBool isLoading = true.obs;

  @override
  void onInit() async {
    super.onInit();

    fromDate = DateTime.now().obs;
    toDate = (DateTime.now().add(Duration(days: 0))).obs;

    formattedFromDate.value = formatDateTime(fromDate.toString());
    formattedToDate.value = formatDateTime(toDate.toString());

    await fetchedToken();
    await fetchPaymentType();
    await fetchCustomers();
    await fetchPayments();
    // await fetchAmounts();

    await checkInternetConnectivity();
  }

  Future<void> fetchPayments() async {
    isLoading.value = true;
    final db = await DatabaseProvider.database;
    print("fetched data from DB: ${db}");
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tablePaymentCollection}");
    paymentList.value = List.generate(maps.length, (index) {
      return Payment.fromMap(maps[index]);
    });

    filteredPaymentList.value = paymentList.value
        .where((payment) => '${fetchCustomerName(payment.cmCode!)}'
            .toLowerCase()
            .contains(searchText.value.toLowerCase()))
        .toList();

    print("filteredPaymentList: ${filteredPaymentList}");

    filteredPaymentList.value
        .sort((a, b) => b.createdAt!.compareTo(a.createdAt!));

    EasyLoading.dismiss();

    await fetchAmounts();
  }

  Future<void> fetchFilteredPayments() async {
    filteredPaymentList.value = paymentList.value
        .where((payment) => '${fetchCustomerName(payment.cmCode!)}'
            .toLowerCase()
            .contains(searchText.value.toLowerCase()))
        .toList();

    print("filteredExpenseList: ${filteredPaymentList}");

    filteredPaymentList.value
        .sort((a, b) => b.createdAt!.compareTo(a.createdAt!));

    await fetchAmounts();
  }

  void updateSearchText(String newText) {
    searchText.value = newText;
    fetchFilteredPayments();
  }

  Future<void> fetchPaymentType() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tablePaymentType}");
    paymentTypeList.value = List.generate(maps.length, (index) {
      return PaymentType.fromMap(maps[index]);
    });
  }

  Future<void> fetchCustomers() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCustomer}");
    customerList.value = List.generate(maps.length, (index) {
      return Customer.fromMap(maps[index]);
    });
  }

  Future<void> fetchAmounts() async {
    totalAmount.value = 0.0;

    for (final payment in filteredPaymentList) {
      totalAmount.value += payment.amount!;
    }
    print('totalAmount.value : ${totalAmount.value}');
    isLoading.value = false;
  }

  Future<bool> checkInternetConnectivity() async {
    var connectivityResult = await _connectivity.checkConnectivity();
    if (connectivityResult != ConnectivityResult.none) {
      syncPaymentsWithServer();
    }
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> syncPaymentsWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedPayments = await db.query(
        '${TableValues.tablePaymentCollection}',
        where: '${TableValues.pcSyncStatus} = ?',
        whereArgs: [0]);

    if (unsyncedPayments.isNotEmpty) {
      print('Syncing payments with the server...');
      print("unsyncedPayments: ${unsyncedPayments}");

      for (final payment in unsyncedPayments) {
        try {
          await addPaymentAPI(payment);

          if (isPaymentAPISuccess == true) {
            await db.update('${TableValues.tablePaymentCollection}',
                {'${TableValues.pcSyncStatus}': 1},
                where: '${TableValues.pcCode} = ?',
                whereArgs: [payment['${TableValues.pcCode}']]);

            print(
                'Payment with ID ${payment['${TableValues.pcCode}']} synced successfully');
          } else {
            print(
                'Error syncing payment with ID ${payment['${TableValues.pcCode}']}');

            await ApiLogger.logError(
              apiName: "Add payment sync",
              apiRequestData: payment,
              apiResponse:
                  'Error syncing payment with ID ${payment['${TableValues.pcCode}']}',
            );
          }
        } catch (error) {
          print(
              'Error syncing Payment with ID ${payment['${TableValues.pcCode}']}: $error');

          await ApiLogger.logError(
            apiName: "Add payment sync with error",
            apiRequestData: payment,
            apiResponse:
                'Error syncing payment with ID ${payment['${TableValues.pcCode}']}: $error',
          );
        }
      }
      fetchPayments();
    } else {
      print('No payments to sync.');
    }
  }

  Future<void> addPaymentAPI(Map<String, dynamic> payment) async {
    try {
      Map<String, dynamic> data = {};

      print(payment['pc_type']);

      if (payment['pc_type'] == 1) {
        data = {
          'pc_code': "${payment['pc_code']}",
          'cm_code': payment['pc_cm_code'],
          'pc_type': payment['pc_type'],
          'pc_amount': "${payment['pc_amount']}",
          'pc_note': "${payment['pc_note']}",
          'emp_code': payment['pc_emp_code'],
          'pc_condition_id': payment['pc_condition_id'],
        };
      } else if (payment['pc_type'] == 2) {
        data = {
          'pc_code': "${payment['pc_code']}",
          'cm_code': "${payment['pc_cm_code']}",
          'pc_type': "${payment['pc_type']}",
          'pc_amount': "${payment['pc_amount']}",
          'pc_note': "${payment['pc_note']}",
          'emp_code': "${payment['pc_emp_code']}",
          'pc_condition_id': "${payment['pc_condition_id']}",
          'pc_bank_id': "${payment['pc_bank_id']}",
          'pc_cheque_date': "${payment['pc_cheque_date']}",
          'pc_cheque_no': "${payment['pc_cheque_no']}",
          'pc_account_no': "${payment['pc_account_no']}",
          'pc_cheque_photo': "${payment['pc_cheque_photo']}",
          'pc_status': "${payment['pc_status']}",
          'created_at': "${payment['created_at']}",
          'updated_at': "${payment['updated_at']}",
        };
      } else if (payment['pc_type'] == 3) {
        data = {
          'pc_code': "${payment['pc_code']}",
          'cm_code': "${payment['pc_cm_code']}",
          'pc_type': "${payment['pc_type']}",
          'pc_amount': "${payment['pc_amount']}",
          'pc_note': "${payment['pc_note']}",
          'emp_code': "${payment['pc_emp_code']}",
          'pc_condition_id': "${payment['pc_condition_id']}",
          'pc_bank_id': "${payment['pc_bank_id']}",
          'pc_cheque_date': "${payment['pc_cheque_date']}",
          'pc_cheque_no': "${payment['pc_cheque_no']}",
          'pc_account_no': "${payment['pc_account_no']}",
          'pc_cheque_photo': "${payment['pc_cheque_photo']}",
          'pc_status': "${payment['pc_status']}",
          'created_at': "${payment['created_at']}",
          'updated_at': "${payment['updated_at']}",
        };
      }

      print("data: ${data}");

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };

      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.postAddPaymentEndpoint),
        data: data,
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      if (response.statusCode == 200) {
        print('Request successful');
        print('Response data: ${response.data}');
        isPaymentAPISuccess = true;
      } else {
        print('Unexpected status code: ${response.statusCode}');
        isPaymentAPISuccess = false;
      }
      print('Response status: ${response.statusCode}');
      print('Response data: ${response.data}');
    } catch (error) {
      String errorMessage = "";

      if (error is DioException) {
        isPaymentAPISuccess = false;

        if (error.response != null) {
          errorMessage =
              'Error ${error.response!.statusCode}: ${error.response!.statusMessage}';
          print(errorMessage);
          if (error.response!.statusCode == 422) {
            print(error.response!.data["message"] ?? "No data found");
          }
        } else {
          errorMessage = 'Network error: ${error.message}';
          print(errorMessage);
        }
      } else {
        isPaymentAPISuccess = false;
        print('Error: $error');
      }

      await ApiLogger.logError(
        apiName: ApiClient.postAddPaymentEndpoint,
        apiRequestData: payment,
        apiResponse: errorMessage,
      );
    }
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  String formatDate(String dateString) {
    if (dateString.isNotEmpty) {
      DateTime dateTime = DateTime.parse(dateString);
      String formattedDate = DateFormat('dd MMM yyyy').format(dateTime);
      return formattedDate;
    } else {
      return "";
    }
  }

  void toggleOption(String option) {
    if (selectedOptions.contains(option)) {
      selectedOptions.remove(option);
    } else {
      selectedOptions.add(option);
    }
  }

  String formatFilterDateTime(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('yyyy-MM-dd HH:mm:ss.SSSZ');
    return formatter.format(dateTime);
  }

  String formatDateTime(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('dd MMM');
    return formatter.format(dateTime);
  }

  Future<void> selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isFromDate ? fromDate.value : toDate.value,
      firstDate: DateTime(2015, 8),
      lastDate: DateTime(2101),
    );
    if (picked != null) {
      if (isFromDate) {
        fromDate.value = picked;
      } else {
        toDate.value = picked;
      }
    }
  }

  String? fetchTypeName(int pcTypeId) {
    try {
      PaymentType? type = paymentTypeList.firstWhere(
        (type) => type.ptID == pcTypeId,
      );

      return type.ptName;
    } catch (e) {
      if (kDebugMode) {
        print('Error in fetchTypeName: $e');
      }
      return null;
    }
  }

  String? fetchCustomerName(String pcCMCode) {
    print("pcCMCode: ${pcCMCode}");
    try {
      Customer? name = customerList.firstWhere(
        (type) => type.cmCode == pcCMCode,
      );
      print("type: ${name.cmName}");
      return name.cmName;
    } catch (e) {
      if (kDebugMode) {
        print('Error in fetchCustomerName: $e');
      }
      return null;
    }
  }

  void filterPaymentsByDateRange(
      String fromDateStr,
      String toDateStr,
      Set<PaymentFilterType> currentPaymentType,
      Set<PaymentStatus> paymentStatuses) {
    filteredPaymentList.clear();

    DateTime fromDate = DateTime.tryParse(fromDateStr) ?? DateTime.now();
    DateTime toDate = DateTime.tryParse(toDateStr) ?? DateTime.now();
    toDate = DateTime(toDate.year, toDate.month, toDate.day, 23, 59, 59);
    print("fromDate fetched : ${fromDate}");
    print("toDate fetched : ${toDate}");
    bool isWithinDateRange = false;
    bool isDateSelected = false;
    // 0-pending, 1-conform, 2-rejected, 3-partial approved

    List<Payment> filteredPayments = paymentList.where((payment) {
      if (!fromDateStr.isEmpty || !toDateStr.isEmpty) {
        isDateSelected = true;

        DateTime paymentDate = DateTime.parse(payment.createdAt ?? "");
        print("paymentDate: ${paymentDate}");
        print("isBefore: ${paymentDate.isBefore(toDate)}");
        print("isAfter: ${paymentDate.isAfter(fromDate)}");

        isWithinDateRange =
            paymentDate.isAfter(fromDate) && paymentDate.isBefore(toDate);
      } else {
        isDateSelected = false;
      }

      bool matchesPaymentType =
          currentPaymentType.contains(PaymentFilterType.all) ||
              (currentPaymentType.contains(PaymentFilterType.cash) &&
                  payment.type == 1) ||
              (currentPaymentType.contains(PaymentFilterType.cheque) &&
                  payment.type == 2) ||
              (currentPaymentType.contains(PaymentFilterType.netbanking) &&
                  payment.type == 3);

      bool matchesPaymentStatus = paymentStatuses.contains(PaymentStatus.all) ||
          (paymentStatuses.contains(PaymentStatus.pending) &&
              payment.status == 0) ||
          (paymentStatuses.contains(PaymentStatus.approved) &&
              payment.status == 1) ||
          (paymentStatuses.contains(PaymentStatus.rejected) &&
              payment.status == 2);

      // Debugging information
      print("Payment Code: ${payment.code}");
      print("isWithinDateRange: $isWithinDateRange");
      print("matchesPaymentType: $matchesPaymentType");
      print("matchesPaymentStatus: $matchesPaymentStatus");

      if (isDateSelected) {
        if (isWithinDateRange && (matchesPaymentType && matchesPaymentStatus)) {
          print("data added in filteredComplainList");
          filteredPaymentList.add(payment);
        }
      } else {
        if (isWithinDateRange || (matchesPaymentType && matchesPaymentStatus)) {
          print("data added in filteredComplainList");
          filteredPaymentList.add(payment);
        }
      }

      if (filteredPaymentList.length > 0) {
        filteredPaymentList.value
            .sort((a, b) => b.createdAt!.compareTo(a.createdAt!));
      }

      fetchAmounts();
      return matchesPaymentType && matchesPaymentStatus;
    }).toList();
    print("filteredPaymentList: ${filteredPaymentList}");
    print("filteredPaymentList.length ${filteredPayments.length}");
  }
}
