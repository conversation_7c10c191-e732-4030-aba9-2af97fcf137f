import '../controller/leave_controller.dart';
import '../models/product4_item_model.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';

// ignore: must_be_immutable
class Product4ItemWidget extends StatelessWidget {
  Product4ItemWidget(
    this.product4ItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  Product4ItemModel product4ItemModelObj;

  var controller = Get.find<LeaveController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 9.h,
        vertical: 6.v,
      ),
      decoration: AppDecoration.outlineGray.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder10,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(right: 4.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Obx(
                  () => Text(
                    product4ItemModelObj.halfLeaveText!.value,
                    style: CustomTextStyles.titleSmallBluegray900,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 2.v),
                  child: Obx(
                    () => Text(
                      product4ItemModelObj.dateText!.value,
                      style: CustomTextStyles.bodySmallOpenSansGray500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 4.v),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: "lbl_reason".tr,
                  style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                ),
                TextSpan(
                  text: "lbl_full_leave".tr,
                  style: CustomTextStyles.labelLargeOpenSansff8e8e8e,
                ),
              ],
            ),
            textAlign: TextAlign.left,
          ),
          SizedBox(height: 4.v),
          Obx(
            () => Text(
              product4ItemModelObj.statusText!.value,
              style: CustomTextStyles.labelLargeOpenSansBluegray400,
            ),
          ),
        ],
      ),
    );
  }
}
