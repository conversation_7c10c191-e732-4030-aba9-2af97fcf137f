// ignore_for_file: invalid_use_of_protected_member

import 'package:sfm_new/data/filter_option.dart';
import 'package:sfm_new/sfmDatabase/models/leave_request_model.dart';

import 'controller/leave_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_icon_button.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

// ignore_for_file: must_be_immutable
class LeaveScreen extends GetWidget<LeaveController> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(),
        body: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 15.v),
              CustomSearchBar(),
              SizedBox(height: 12.v),
              _buildLeaveList(),
              SizedBox(height: 12.v),
            ],
          ),
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "lbl_leave".tr,
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildLeaveList() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 4.h),
        child: Obx(
          () {
            if (controller.isLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            } else if (controller.filteredLeaveList.isEmpty) {
              return Center(
                child: Text(
                  'No leave found',
                  style: TextStyle(fontSize: 16),
                ),
              );
            } else {
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.h),
                child: ListView.separated(
                  itemCount: controller.filteredLeaveList.length,
                  physics: BouncingScrollPhysics(),
                  shrinkWrap: true,
                  separatorBuilder: (
                    context,
                    index,
                  ) {
                    return SizedBox(
                      height: 10.v,
                    );
                  },
                  itemBuilder: (context, index) => CustomLeaveListItem(
                    leave: controller.filteredLeaveList[index],
                  ),
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return CustomIconButton(
      height: 70.adaptSize,
      width: 70.adaptSize,
      padding: EdgeInsets.all(19.h),
      decoration: IconButtonStyleHelper.outlineOnErrorContainer,
      alignment: Alignment.bottomRight,
      child: CustomImageView(
        imagePath: ImageConstant.imgGroup29,
      ),
      onTap: () {
        print("Plus button pressed");
        Get.toNamed(AppRoutes.addLeaveScreen);
      },
    );
  }
}

class CustomSearchBar extends StatelessWidget {
  DateRangeDialog controllerDateRange = Get.put(DateRangeDialog());
  LeaveController controller = Get.put(LeaveController());

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 12, right: 12, top: 4, bottom: 4),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.5),
              spreadRadius: 2,
              blurRadius: 7,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                style: CustomTextStyles.bodyMediumBluegray700,
                onChanged: (value) {
                  print('Search value: $value');

                  controller.updateSearchText(value);
                },
                decoration: InputDecoration(
                  hintText: 'Search Leave',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 10.0),
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(10.0),
                  bottomRight: Radius.circular(10.0),
                ),
              ),
              child: IconButton(
                icon: Container(
                  child: CustomImageView(
                    svgPath: ImageConstant.imgFilterIcon,
                    height: 20.adaptSize,
                    width: 20.adaptSize,
                  ),
                ),
                onPressed: () {
                  print('Filter button pressed');
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return CustomDialogFilter();
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomLeaveListItem extends StatelessWidget {
  final Leave leave;
  final LeaveController controller = Get.put(LeaveController());

  CustomLeaveListItem({
    required this.leave,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // controller.onNotificationTap(notification);
        // Get.offNamed(AppRoutes.notificationThreeScreen);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 9.h,
          vertical: 6.v,
        ),
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.end,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 4.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    leave.typeId == 1 ? "Full Leave" : "Half Leave",
                    style: CustomTextStyles.titleSmallBluegray900,
                  ),
                ],
              ),
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: MediaQuery.of(context).size.width * 0.2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Date",
                        style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                      ),
                      SizedBox(width: 4),
                      Visibility(
                        visible: leave.typeId == 1 ? true : false,
                        child: Text(
                          "Total Days",
                          style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(width: 4),
                          Text(
                            "Reason",
                            style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                          ),
                          SizedBox(width: 4),
                          Text(
                            "Status",
                            style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                          ),
                          SizedBox(width: 4),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      ":",
                      style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                    ),
                    SizedBox(width: 4),
                    Visibility(
                      visible: leave.typeId == 1 ? true : false,
                      child: Text(
                        ":",
                        style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                      ),
                    ),
                    SizedBox(width: 4),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          ":",
                          style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                        ),
                        SizedBox(width: 4),
                        Text(
                          ":",
                          style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                        ),
                        SizedBox(width: 4),
                      ],
                    ),
                  ],
                ),
                SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      leave.typeId == 1
                          ? "${controller.formatDate(leave.fromDate ?? "")} to ${controller.formatDate(leave.toDate ?? "")}"
                          : "${controller.formatDate(leave.fromDate ?? "")}",
                      style: CustomTextStyles.labelLargeOpenSansff8e8e8e,
                    ),
                    SizedBox(width: 4),
                    Visibility(
                      visible: leave.typeId == 1 ? true : false,
                      child: Text(
                        "${leave.totalDays}",
                        style: CustomTextStyles.labelLargeOpenSansff8e8e8e,
                      ),
                    ),
                    SizedBox(width: 4),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${leave.reason}",
                          style: CustomTextStyles.labelLargeOpenSansff8e8e8e,
                          maxLines: 10,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(width: 4),
                        Text(
                          leave.approvedStatus == 1
                              ? "Pending"
                              : leave.approvedStatus == 2
                                  ? "Approved"
                                  : "Rejected",
                          style: CustomTextStyles.labelLargeOpenSansBluegray400
                              .copyWith(
                            color: leave.approvedStatus == 1
                                ? Colors.orange
                                : leave.approvedStatus == 2
                                    ? Colors.green
                                    : Colors.red,
                          ),
                        ),
                        SizedBox(width: 4),
                      ],
                    ),
                  ],
                ),
                SizedBox(width: 8),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Visibility(
                  visible: leave.syncStatus == 0 ? true : false,
                  child: CustomImageView(
                    svgPath: ImageConstant.imgNotSynced,
                    color: Colors.red,
                    height: 15.adaptSize,
                    width: 15.adaptSize,
                    margin: EdgeInsets.only(bottom: 2.v),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class CustomDialogFilter extends StatelessWidget {
  CustomDialogFilter({
    Key? key,
  }) : super(key: key);

  LeaveController controller = Get.put(LeaveController());

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      insetPadding: EdgeInsets.all(12),
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Filters",
              style: CustomTextStyles.titleLargeGray900,
            ),
            SizedBox(height: 8.v),
            filterLeaveByStatus(),
            SizedBox(height: 16.0),
            Text(
              "Sort By",
              style: CustomTextStyles.titleLargeGray900,
            ),
            SizedBox(height: 8.v),
            filterLeaveByType(),
            SizedBox(height: 16.v),
            _buildDate(context),
            SizedBox(height: 16.v),
            Container(
              width: MediaQuery.of(context).size.width * 0.75,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomElevatedButton(
                    width: MediaQuery.of(context).size.width * 0.35,
                    text: "lbl_cancel".tr,
                    buttonStyle: CustomButtonStyles.fillOnErrorContainer,
                    buttonTextStyle: CustomTextStyles.titleSmallPoppinsOnError,
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  CustomElevatedButton(
                    width: MediaQuery.of(context).size.width * 0.35,
                    text: "lbl_apply".tr,
                    margin: EdgeInsets.only(left: 16.h),
                    buttonStyle: CustomButtonStyles.fillPrimaryTL8,
                    buttonTextStyle:
                        CustomTextStyles.titleSmallPoppinsOnErrorContainer,
                    onPressed: () {
                      print(
                          "controller.filterFromDate.value: ${controller.filterFromDate.value}");
                      print(
                          "controller.filterToDate.value: ${controller.filterToDate.value}");
                      print(
                          "controller.currentLeaveTypes.value: ${controller.currentLeaveTypes.value}");
                      print(
                          "controller.currentLeaveStatuses.value: ${controller.currentLeaveStatuses.value}");
                      controller.filterLeavesByDateRange(
                          controller.filterFromDate.value,
                          controller.filterToDate.value,
                          controller.currentLeaveTypes.value,
                          controller.currentLeaveStatuses.value);
                      Get.back();
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDate(BuildContext context) {
    return Obx(
      () => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "lbl_date".tr,
            style: CustomTextStyles.titleLargeGray900,
          ),
          SizedBox(height: 9.v),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  // Get.dialog(DateRangeDialog());
                  showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                    builder: (context, child) {
                      return Theme(
                        data: Theme.of(context).copyWith(
                          colorScheme: ColorScheme.light(
                            primary: theme.colorScheme.primary,
                          ),
                        ),
                        child: child!,
                      );
                    },
                  ).then((value) {
                    if (value != null) {
                      print("fromdate value: $value");
                      String dateValue = value.toString();
                      controller.formattedFromDate.value =
                          controller.formatDateTime(dateValue);
                      controller.filterFromDate.value =
                          controller.formatFilterDateTime(dateValue);
                      print(controller.formattedFromDate.value);
                      controller.fromDate.value = value;
                    }
                  });
                },
                child: Container(
                  width: 115.h,
                  decoration: AppDecoration.outlineBlack90013,
                  child: Row(
                    children: [
                      _buildDateInputColored(
                        dateText: controller.formattedFromDate.value,
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                  left: 8.h,
                  right: 8.h,
                  // top: 11.v,
                  // bottom: 7.v,
                ),
                child: Text(
                  "to",
                  style: CustomTextStyles.bodyMediumOpenSansBlack900,
                ),
              ),
              GestureDetector(
                onTap: () {
                  showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                    builder: (context, child) {
                      return Theme(
                        data: Theme.of(context).copyWith(
                          colorScheme: ColorScheme.light(
                            primary: theme.colorScheme.primary,
                          ),
                        ),
                        child: child!,
                      );
                    },
                  ).then((value) {
                    if (value != null) {
                      print("todate value: $value");
                      String dateValue = value.toString();
                      controller.formattedToDate.value =
                          controller.formatDateTime(dateValue);
                      controller.filterToDate.value =
                          controller.formatFilterDateTime(dateValue);

                      print(controller.formattedToDate.value);
                      controller.toDate.value = value;
                    }
                  });
                },
                child: Container(
                  width: 115.h,
                  decoration: AppDecoration.outlineBlack90013,
                  child: Row(
                    children: [
                      _buildDateInputColored(
                        dateText: controller.formattedToDate.value,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateInputColored({required String dateText}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 14.h,
        vertical: 9.v,
      ),
      decoration: AppDecoration.outlineBlack90014.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder10,
      ),
      child: Row(
        children: [
          CustomImageView(
            svgPath: ImageConstant.imgCalendarPrimary,
            height: 13.v,
            width: 12.h,
            margin: EdgeInsets.symmetric(vertical: 3.v),
          ),
          Padding(
            padding: EdgeInsets.only(
              left: 10.h,
              top: 2.v,
            ),
            child: Text(
              dateText,
              style: CustomTextStyles.bodySmallOpenSansGray700.copyWith(
                color: appTheme.gray700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget filterLeaveByStatus() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          FilterOption(
            text: "All",
            isSelected:
                controller.currentLeaveStatuses.contains(LeaveStatus.all)
                    ? true
                    : false,
            onTap: () {
              if (controller.currentLeaveStatuses.contains(LeaveStatus.all)) {
                controller.currentLeaveStatuses.remove(LeaveStatus.all);
              } else {
                controller.currentLeaveStatuses.add(LeaveStatus.all);
              }

              if (controller.currentLeaveStatuses
                  .contains(LeaveStatus.pending)) {
                controller.currentLeaveStatuses.remove(LeaveStatus.pending);
              }

              if (controller.currentLeaveStatuses
                  .contains(LeaveStatus.approved)) {
                controller.currentLeaveStatuses.remove(LeaveStatus.approved);
              }

              if (controller.currentLeaveStatuses
                  .contains(LeaveStatus.rejected)) {
                controller.currentLeaveStatuses.remove(LeaveStatus.rejected);
              }

              print(
                  "controller.currentLeaveStatuses: ${controller.currentLeaveStatuses}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Pending",
            isSelected:
                controller.currentLeaveStatuses.contains(LeaveStatus.pending)
                    ? true
                    : false,
            onTap: () {
              if (controller.currentLeaveStatuses
                  .contains(LeaveStatus.pending)) {
                controller.currentLeaveStatuses.remove(LeaveStatus.pending);
              } else {
                controller.currentLeaveStatuses.add(LeaveStatus.pending);
              }

              if (controller.currentLeaveStatuses.contains(LeaveStatus.all)) {
                controller.currentLeaveStatuses.remove(LeaveStatus.all);
              }

              print(
                  "controller.currentLeaveStatuses: ${controller.currentLeaveStatuses}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Approved",
            isSelected:
                controller.currentLeaveStatuses.contains(LeaveStatus.approved)
                    ? true
                    : false,
            onTap: () {
              if (controller.currentLeaveStatuses
                  .contains(LeaveStatus.approved)) {
                controller.currentLeaveStatuses.remove(LeaveStatus.approved);
              } else {
                controller.currentLeaveStatuses.add(LeaveStatus.approved);
              }

              if (controller.currentLeaveStatuses.contains(LeaveStatus.all)) {
                controller.currentLeaveStatuses.remove(LeaveStatus.all);
              }

              print(
                  "controller.currentLeaveStatuses: ${controller.currentLeaveStatuses}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Rejected",
            isSelected:
                controller.currentLeaveStatuses.contains(LeaveStatus.rejected)
                    ? true
                    : false,
            onTap: () {
              if (controller.currentLeaveStatuses
                  .contains(LeaveStatus.rejected)) {
                controller.currentLeaveStatuses.remove(LeaveStatus.rejected);
              } else {
                controller.currentLeaveStatuses.add(LeaveStatus.rejected);
              }

              if (controller.currentLeaveStatuses.contains(LeaveStatus.all)) {
                controller.currentLeaveStatuses.remove(LeaveStatus.all);
              }

              print(
                  "controller.currentLeaveStatuses: ${controller.currentLeaveStatuses}");
            },
          ),
        ],
      ),
    );
  }

  Widget filterLeaveByType() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          FilterOption(
            text: "All",
            isSelected: controller.currentLeaveTypes.contains(LeaveType.all)
                ? true
                : false,
            onTap: () {
              controller.currentLeaveTypes.value = {LeaveType.all};
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Full",
            isSelected: controller.currentLeaveTypes.contains(LeaveType.full)
                ? true
                : false,
            onTap: () {
              controller.currentLeaveTypes.value = {LeaveType.full};
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Half",
            isSelected: controller.currentLeaveTypes.contains(LeaveType.half)
                ? true
                : false,
            onTap: () {
              controller.currentLeaveTypes.value = {LeaveType.half};
            },
          ),
        ],
      ),
    );
  }
}
