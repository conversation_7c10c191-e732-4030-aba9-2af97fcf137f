import '../../../core/app_export.dart';

/// This class is used in the [product4_item_widget] screen.
class Product4ItemModel {
  Product4ItemModel({
    this.halfLeaveText,
    this.dateText,
    this.statusText,
    this.id,
  }) {
    halfLeaveText = halfLeaveText ?? Rx("Half Leave");
    dateText = dateText ?? Rx("7 Jan 2021");
    statusText = statusText ?? Rx("Pending");
    id = id ?? Rx("");
  }

  Rx<String>? halfLeaveText;

  Rx<String>? dateText;

  Rx<String>? statusText;

  Rx<String>? id;
}
