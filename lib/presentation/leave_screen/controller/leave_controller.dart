import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/leave_request_model.dart';
import 'package:dio/dio.dart' as dio_package;

enum LeaveType { all, full, half }

enum LeaveStatus { all, pending, approved, rejected }

class LeaveController extends GetxController {
  final searchText = ''.obs;

  RxBool isLoading = true.obs;

  late String formattedDateTime;

  late Rx<DateTime> fromDate;
  late Rx<DateTime> toDate;

  RxString formattedFromDate = ''.obs;
  RxString formattedToDate = ''.obs;

  RxString filterFromDate = ''.obs;
  RxString filterToDate = ''.obs;

  var selectedOptions = <String>['all', 'All'].obs;

  RxList<Leave> leaveList = <Leave>[].obs;
  RxList<Leave> filteredLeaveList = <Leave>[].obs;

  RxSet<LeaveType> currentLeaveTypes = <LeaveType>{LeaveType.all}.obs;
  RxSet<LeaveStatus> currentLeaveStatuses = <LeaveStatus>{LeaveStatus.all}.obs;

  bool isLeaveAPISuccess = false;

  final Connectivity _connectivity = Connectivity();

  RxString employeeToken = "".obs;

  // Selected conditions
  var selectedTypeIds = <int>[1, 2].obs; 
  var selectedApprovedStatuses =
      <int>[0, 1, 2, 3].obs; 

  @override
  void onInit() async {
    super.onInit();

    EasyLoading.show(status: 'Loading...');
    fetchLeaveDetails();

    await checkInternetConnectivity();
  }

  void fetchLeaveDetails() async {
    fromDate = DateTime.now().obs;
    toDate = (DateTime.now().add(Duration(days: 1))).obs;

    formattedFromDate.value = formatDateTime(fromDate.toString());
    formattedToDate.value = formatDateTime(toDate.toString());

    await fetchedToken();
    await fetchLeaves();

    EasyLoading.dismiss();
  }

  Future<bool> checkInternetConnectivity() async {
    var connectivityResult = await _connectivity.checkConnectivity();
    if (connectivityResult != ConnectivityResult.none) {
      syncLeavesWithServer();
    }
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> syncLeavesWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedLeaves = await db.query(
        '${TableValues.tableLeaveRequest}',
        where: '${TableValues.leaveSyncStatus} = ?',
        whereArgs: [0]);

    if (unsyncedLeaves.isNotEmpty) {
      print('Syncing Leaves with the server...');
      print("unsyncedLeaves: ${unsyncedLeaves}");

      for (final leave in unsyncedLeaves) {
        try {
          await addLeaveAPI(leave);

          if (isLeaveAPISuccess == true) {
            await db.update('${TableValues.tableLeaveRequest}',
                {'${TableValues.leaveSyncStatus}': 1},
                where: '${TableValues.leaveCode} = ?',
                whereArgs: [leave['${TableValues.leaveCode}']]);
            print(
                'Leave with ID ${leave['${TableValues.leaveCode}']} synced successfully');
          } else {
            print(
                'Error syncing leave with ID ${leave['${TableValues.leaveCode}']}');
          }
        } catch (error) {
          print(
              'Error syncing leave with ID ${leave['${TableValues.leaveCode}']}: $error');
        }
      }
      fetchLeaveDetails();
    } else {
      print('No Leaves to sync.');
    }
  }

  Future<void> addLeaveAPI(Map<String, dynamic> leave) async {
    // EasyLoading.show(status: 'Loading...');

    try {
      Map<String, dynamic> data = {};

      data = {
        "la_code": "${leave['la_code']}",
        "la_type_id": "${leave['la_type_id']}",
        "la_from_date": "${leave['la_from_date']}",
        "la_to_date": "${leave['la_to_date']}",
        "la_total_day": "${leave['la_total_day']}",
        "la_approved_status": "${leave['la_approved_status']}",
        "la_reason": "${leave['la_reason']}",
        "emp_code": "${leave['la_emp_code']}",
      };

      print("data: ${data}");

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };
      print("employeeToken: ${employeeToken.value}");

      // dio_package.Dio dioClient = dio_package.Dio();
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.postAddLeaveEndpoint),
        data: data,
        options: dio_package.Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      if (response.statusCode == 200) {
        print('Response data: ${response.data}');
        isLeaveAPISuccess = true;
        EasyLoading.dismiss();
      } else {
        isLeaveAPISuccess = false;
        print('Unexpected status code: ${response.statusCode}');
      }
    } on DioException catch (e) {
      isLeaveAPISuccess = false;
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
          showToastMessage(e.response!.data["message"] ?? "No data found");
        } else {
          showToastMessage(
              '${e.response!.statusCode}: ${e.response!.statusMessage}');
        }
      }
    } catch (e) {
      isLeaveAPISuccess = false;
      EasyLoading.dismiss();
      print('Unexpected error: $e');
    }
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  Future<void> fetchLeaves() async {
    isLoading.value = true;
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableLeaveRequest}");
    print(maps);
    leaveList.value = List.generate(maps.length, (index) {
      return Leave.fromMap(maps[index]);
    });
    filteredLeaveList.value = List.generate(maps.length, (index) {
      return Leave.fromMap(maps[index]);
    });

    filteredLeaveList.sort((a, b) => b.createdAt!.compareTo(a.createdAt!));

    isLoading.value = false;
  }

  Future<void> fetchFilteredLeaves() async {
    filteredLeaveList.value = leaveList
        .where((leave) => leave.reason!
            .toLowerCase()
            .contains(searchText.value.toLowerCase()))
        .toList();

    print("filteredLeaveList: ${filteredLeaveList}");

    filteredLeaveList.sort((a, b) => b.createdAt!.compareTo(a.createdAt!));
  }

  void updateSearchText(String newText) {
    searchText.value = newText;
    fetchFilteredLeaves();
  }

  Future<void> fetchLeavesWithConditions() async {
    final db = await DatabaseProvider.database;

    // Define the conditions for fetching leaves
    final String whereClause =
        '${TableValues.leaveType} IN (${selectedTypeIds.join(', ')}) AND ' +
            '${TableValues.leaveApprovedStatus} IN (${selectedApprovedStatuses.join(', ')})';

    // Execute the query with the conditions
    final List<Map<String, dynamic>> maps = await db.query(
      TableValues.tableLeaveRequest,
      where: whereClause,
    );

    // Parse the results and update the leaveList value
    leaveList.value = List.generate(maps.length, (index) {
      return Leave.fromMap(maps[index]);
    });

    print("leaveList.value : ${leaveList}");
  }

  // Function to update selected conditions
  void updateSelectedConditions({
    required List<int> typeIds,
    required List<int> approvedStatuses,
  }) {
    selectedTypeIds.value = typeIds;
    selectedApprovedStatuses.value = approvedStatuses;

    fetchLeavesWithConditions();
  }

  void toggleOption(String option) {
    if (selectedOptions.contains(option)) {
      selectedOptions.remove(option);
    } else {
      selectedOptions.add(option);
    }
  }

  void toggleApprovedStatus(int option, bool isSelected) {
    if (selectedApprovedStatuses.contains(option)) {
      selectedApprovedStatuses.remove(option);
    } else {
      selectedApprovedStatuses.add(option);
    }
    print("selectedApprovedStatuses ${selectedApprovedStatuses}");
  }

  String formatDate(String dateString) {
    if (dateString.isNotEmpty) {
      DateTime dateTime = DateTime.parse(dateString);
      String formattedDate = DateFormat('dd MMM yyyy').format(dateTime);
      return formattedDate;
    } else {
      return "";
    }
  }

  String formatFilterDateTime(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(dateTime);
  }

  String formatDateTime(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('dd MMM');
    return formatter.format(dateTime);
  }

  Future<void> selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isFromDate ? fromDate.value : toDate.value,
      firstDate: DateTime(2015, 8),
      lastDate: DateTime(2101),
    );
    if (picked != null) {
      if (isFromDate) {
        fromDate.value = picked;
      } else {
        toDate.value = picked;
      }
    }
  }

  void applyDateRange() {
    print('Selected Date Range: ${fromDate.value} to ${toDate.value}');
    Get.back();
  }

  void filterLeavesByDateRange(String fromDateStr, String toDateStr,
      Set<LeaveType> currentLeaveType, Set<LeaveStatus> leaveStatuses) {
    filteredLeaveList.clear();

    DateTime fromDate = DateTime.tryParse(fromDateStr) ?? DateTime.now();
    print('fromDate: ${fromDate}');
    DateTime toDate = DateTime.tryParse(toDateStr) ?? DateTime.now();
    toDate = DateTime(toDate.year, toDate.month, toDate.day, 23, 59, 59);
    print('toDate: ${toDate}');
    bool isWithinDateRange = false;
    bool isDateSelected = false;

    List<Leave> filteredLeaves = leaveList.where((leave) {
      if (!fromDateStr.isEmpty || !toDateStr.isEmpty) {
        isDateSelected = true;


        DateTime leaveDate = DateTime.parse(leave.fromDate ?? "");

        DateTime leaveDateOnly =
            DateTime(leaveDate.year, leaveDate.month, leaveDate.day);
        DateTime fromDateOnly =
            DateTime(fromDate.year, fromDate.month, fromDate.day);
        DateTime toDateOnly = DateTime(toDate.year, toDate.month, toDate.day);

        print('leaveDateOnly: ${leaveDateOnly}');
        print('fromDateOnly: ${fromDateOnly}');
        print('toDateOnly: ${toDateOnly}');

        isWithinDateRange = leaveDateOnly.isAtSameMomentAs(fromDateOnly) ||
            leaveDateOnly.isAtSameMomentAs(toDateOnly) ||
            (leaveDateOnly.isAfter(fromDateOnly) &&
                leaveDateOnly.isBefore(toDateOnly));

        // isWithinDateRange =
        //     leaveDate.isAfter(fromDate) && leaveDate.isBefore(toDate);
      } else {
        isDateSelected = false;
      }

      bool matchesLeaveType = currentLeaveType.contains(LeaveType.all) ||
          (currentLeaveType.contains(LeaveType.full) && leave.typeId == 1) ||
          (currentLeaveType.contains(LeaveType.half) && leave.typeId == 2);

      bool matchesLeaveStatus = leaveStatuses.contains(LeaveStatus.all) ||
          (leaveStatuses.contains(LeaveStatus.pending) &&
              leave.approvedStatus == 1) ||
          (leaveStatuses.contains(LeaveStatus.approved) &&
              leave.approvedStatus == 2) ||
          (leaveStatuses.contains(LeaveStatus.rejected) &&
              leave.approvedStatus == 3);

      // Debugging information
      print("Leave code: ${leave.code}");
      print("isWithinDateRange: $isWithinDateRange");
      print("matchesLeaveType: $matchesLeaveType");
      print("matchesLeaveStatus: $matchesLeaveStatus");

      if (isDateSelected) {
        if (isWithinDateRange && (matchesLeaveType && matchesLeaveStatus)) {
          print("data added in filteredLeaveList");
          filteredLeaveList.add(leave);
        }
      } else {
        if (isWithinDateRange || (matchesLeaveType && matchesLeaveStatus)) {
          print("data added in filteredLeaveList");
          filteredLeaveList.add(leave);
        }
      }

      if (filteredLeaveList.length > 0) {
        filteredLeaveList.sort((a, b) => b.createdAt!.compareTo(a.createdAt!));
      }

      return matchesLeaveType && matchesLeaveStatus;
    }).toList();
    print("filteredLeaveList: ${filteredLeaveList}");
    print("filteredLeaves ${filteredLeaves}");
  }
}

class DateRangeDialog extends StatelessWidget {
  final LeaveController controller = Get.put(LeaveController());

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Select Date Range'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Row(
            children: <Widget>[
              Text('From:'),
              TextButton(
                onPressed: () => controller.selectDate(context, true),
                child: Obx(() => Text(DateFormat('yyyy-MM-dd')
                    .format(controller.fromDate.value))),
              ),
            ],
          ),
          Row(
            children: <Widget>[
              Text('To:'),
              TextButton(
                onPressed: () => controller.selectDate(context, false),
                child: Obx(() => Text(
                    DateFormat('yyyy-MM-dd').format(controller.toDate.value))),
              ),
            ],
          ),
        ],
      ),
      actions: <Widget>[
        TextButton(
          onPressed: () {
            Get.back(); 
          },
          child: SizedBox(
            width: MediaQuery.of(context).size.width *
                0.3,
            height: 50, 
            child: Center(child: Text('Cancel')),
          ),
        ),
        ElevatedButton(
          onPressed: () => controller.applyDateRange(),
          child: SizedBox(
            width: MediaQuery.of(context).size.width *
                0.3, 
            height: 50, 
            child: Center(child: Text('Apply')),
          ),
        ),
      ],
    );
  }
}
