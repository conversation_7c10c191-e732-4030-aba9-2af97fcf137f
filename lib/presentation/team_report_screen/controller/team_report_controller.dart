// ignore_for_file: invalid_use_of_protected_member, unnecessary_null_comparison

import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';
import 'package:open_file_plus/open_file_plus.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/login_screen/models/login_model.dart';
import 'package:sfm_new/presentation/team_report_screen/models/team_report_model.dart';

class TeamReportController extends GetxController {
  TextEditingController fromDateController = TextEditingController();
  TextEditingController toDateController = TextEditingController();
  RxList<TeamMember> teamMembers = <TeamMember>[].obs;

  RxList<ReportType> reportTypeList = <ReportType>[].obs;
  RxList<CustomerReportType> customerTypes = <CustomerReportType>[].obs;
  var showCustomerDropdown = false.obs;

  RxString selectedEmpCode = ''.obs;
  RxString selectedReportType = ''.obs;
  RxString selectedCustomerType = ''.obs;

  RxString selectedReportName = ''.obs;

  RxString fromDateToShow = ''.obs;
  RxString fromDateToSave = ''.obs;

  RxString toDateToShow = ''.obs;
  RxString toDateToSave = ''.obs;

  RxString employeeToken = "".obs;
  var isLoading = false.obs;

  @override
  void onInit() async {
    super.onInit();

    EasyLoading.dismiss();

    await fetchedToken();

    final List<ConnectivityResult> connectivityResult =
        await (Connectivity().checkConnectivity());

    if (connectivityResult.contains(ConnectivityResult.none)) {
      showToastMessage("Please check your internet connection");
      isLoading.value = false;
    } else {
      await getTeamMemberListAPI();
      await getTeamReportListAPI();
    }
  }

  String formatDateTimeToSave(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(dateTime);
  }

  String formatDateTimeToShow(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('dd MMM yyyy');
    return formatter.format(dateTime);
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  Future<void> getTeamMemberListAPI() async {
    isLoading.value = true;
    try {
      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getTeamMemberListEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      if (response.data != null) {
        if (response.statusCode == 200) {
          try {
            teamMembers.value = (response.data['team_members'] as List)
                .map((member) => TeamMember.fromJson(member))
                .toList();

            print("teamMembers.value: ${teamMembers.value}");
          } catch (e) {
            print('Error parsing response data into model: $e');
          }
        } else {
          print('Unexpected status code: ${response.statusCode}');
        }
      } else {
        print('Response data is null');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: ApiClient.getRoutePlanListEndpoint,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: ApiClient.getRoutePlanListEndpoint,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> getTeamReportListAPI() async {
    isLoading.value = true;
    try {
      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getTeamReportListEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      if (response.data != null) {
        if (response.statusCode == 200) {
          // Parse the response into the model
          try {
            final reportTypeResponse = ReportTypeData.fromJson(response.data);
            reportTypeList.value = reportTypeResponse.reportTypes;
            print(reportTypeList);
          } catch (e) {
            print('Error parsing response data into model: $e');
          }
        } else {
          print('Unexpected status code: ${response.statusCode}');
        }
      } else {
        print('Response data is null');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: ApiClient.getRoutePlanListEndpoint,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: ApiClient.getRoutePlanListEndpoint,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> postTeamReportAPI() async {
    Map<String, dynamic> data = {};

    data = {
      "report": "${selectedReportType.value}",
      "date": "${fromDateToSave.value},${toDateToSave.value}",
      "search_employee": "${selectedEmpCode.value}",
      "cust_report_type": "${selectedCustomerType.value}",
      "d_type": "excel"
    };

    try {
      print("data: ${data}");

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };
      print("headers: ${headers}");

      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.postTeamReportEndpoint),
        data: data,
        options: Options(headers: headers, responseType: ResponseType.bytes),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.data);

      if (response.statusCode == 200) {
        try {
          // Get temporary directory path

          print('response.data: $response.data');

          // Create SFM folder if it doesn't exist
          Directory sfmDir = Directory('storage/emulated/0/SFM');
          if (!await sfmDir.exists()) {
            await sfmDir.create();
          }
          print("sfmDir: ${sfmDir}");

          // Create Report folder inside SFM if it doesn't exist
          Directory reportDir = Directory('${sfmDir.path}/Team Report');
          if (!await reportDir.exists()) {
            await reportDir.create();
          }
          print("reportDir: ${reportDir}");
          print(
              "selectedReportType.value.valueir: ${selectedReportType.value}");

          String fileName = "";

          String reportName = "";

          reportName = trimTrailingSlash(selectedReportType.value);
          print("reportName: ${reportName}");

          fileName =
              "${selectedEmpCode.value}_${fromDateToSave.value}_${toDateToSave.value}_${selectedReportType.value}.xlsx";

          print("fileName: ${fileName}");

          // Define file path for the downloaded file
          String filePath = '${reportDir.path}/$fileName';

          // Write file bytes to the file
          File file = File(filePath);
          await file.writeAsBytes(response.data);
          print("file: ${file}");

          // File saved successfully
          print('File saved to: $filePath');

          Get.dialog(
            AlertDialog(
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.transparent,
              title: Text(
                'File Saved',
                style: CustomTextStyles.titleMediumPrimary.copyWith(
                  color: theme.colorScheme.primary,
                ),
              ),
              content:
                  Text('The file has been saved to SFM/Report/${fileName}'),
              actions: [
                TextButton(
                  onPressed: () {
                    print("reportDir.path: ${reportDir.path}");
                    OpenFile.open(filePath);
                    Get.back();
                  },
                  child: Text(
                    'Open File',
                    style:
                        CustomTextStyles.bodyMediumPoppinsBluegray900.copyWith(
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
          );
        } catch (e) {
          print('Error saving file: $e');
        }
      } else {
        print('Response data is null');
        showToastMessage("${response.statusMessage}");
      }
      EasyLoading.dismiss();
    } on DioException catch (e) {
      String errorMessage;

      if (e.response != null) {
        errorMessage =
            'Error ${e.response!.statusCode}: ${e.response!.statusMessage}';
        print(errorMessage);
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        }
      } else {
        errorMessage = 'Network error: ${e.message}';
        print(errorMessage);
      }

      await ApiLogger.logError(
        apiName: ApiClient.postTeamReportEndpoint,
        apiRequestData: data,
        apiResponse: errorMessage,
      );
      EasyLoading.dismiss();
    } on TimeoutException catch (e) {
      print('Request timed out: $e');
      EasyLoading.dismiss();

      await ApiLogger.logError(
        apiName: ApiClient.postTeamReportEndpoint,
        apiRequestData: data,
        apiResponse: "TimeoutException: $e",
      );
    } finally {
      EasyLoading.dismiss();
    }
  }

  void saveEmpCode(String selectedEmpName) {
    final selectedEmployee = teamMembers.firstWhere(
      (item) => item.empName == selectedEmpName,
      orElse: () => TeamMember(),
    );

    if (selectedEmployee != null) {
      selectedEmpCode.value = selectedEmployee.empCode ?? ""; 
      print('Selected Employee empCode: ${selectedEmpCode.value}');
    }
  }

  void saveCustomerType(String selectedCustomer) {
    final selectedCustomerTypeObj = customerTypes.value.firstWhere(
      (customer) => customer.title == selectedCustomer,
      orElse: () => CustomerReportType(title: '', value: ''),
    );
    print("selectedCustomerTypeObj: ${selectedCustomerTypeObj}");

    if (selectedCustomerTypeObj != null) {
      selectedCustomerType.value =
          selectedCustomerTypeObj.value; 
      print('Selected Customer Type: ${selectedCustomerType.value}');
    }
  }

  void updateCustomerDropdown(String selectedReport) {
    // Find the ReportType object based on the selected report title
    final selectedReportObj = reportTypeList.firstWhere(
      (item) => item.title == selectedReport,
      orElse: () => ReportType(
          title: '',
          value: '',
          filters: []), 
    );

    if (selectedReportObj != null) {
      selectedReportType.value =
          selectedReportObj.value; 
      print('Selected selectedReportType Type: ${selectedReportType.value}');
    }

    if (selectedReportObj.filters != null &&
        selectedReportObj.filters!.isNotEmpty) {
      final filter = selectedReportObj.filters!.firstWhere(
        (filter) =>
            filter.customerReportTypes != null &&
            filter.customerReportTypes!.isNotEmpty,
        orElse: () => Filter(
            title: '',
            value: '',
            customerReportTypes: []),
      );

      if (filter.customerReportTypes != null &&
          filter.customerReportTypes!.isNotEmpty) {
        customerTypes.value = filter.customerReportTypes!
            .map<CustomerReportType>((customer) => customer)
            .toList();

        print("customerTypes: ${customerTypes.value}");

        showCustomerDropdown.value = true;
      } else {
        showCustomerDropdown.value = false;
      }
    } else {
      showCustomerDropdown.value = false;
    }
  }

  String trimTrailingSlash(String input) {
    return input.replaceAll(RegExp(r'\/+$'), '');
  }
}
