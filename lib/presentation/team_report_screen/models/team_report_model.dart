
class ReportTypeData {
  final List<ReportType> reportTypes;

  ReportTypeData({required this.reportTypes});

  factory ReportTypeData.fromJson(Map<String, dynamic> json) {
    return ReportTypeData(
      reportTypes: (json['report_types'] as List)
          .map((item) => ReportType.fromJson(item))
          .toList(),
    );
  }

  @override
  String toString() {
    return 'ReportTypeResponse(reportTypes: $reportTypes)';
  }
}

class ReportType {
  final String title;
  final String value;
  final List<Filter>? filters;

  ReportType({
    required this.title,
    required this.value,
    this.filters,
  });

  factory ReportType.fromJson(Map<String, dynamic> json) {
    return ReportType(
      title: json['title'],
      value: json['value'],
      filters: json['filters'] != null
          ? (json['filters'] as List)
              .map((item) => Filter.fromJson(item))
              .toList()
          : null,
    );
  }

  @override
  String toString() {
    return 'ReportType(title: $title, value: $value, filters: $filters)';
  }
}

class Filter {
  final String title;
  final String value;
  final List<CustomerReportType>? customerReportTypes;

  Filter({
    required this.title,
    required this.value,
    this.customerReportTypes,
  });

  factory Filter.fromJson(Map<String, dynamic> json) {
    return Filter(
      title: json['title'],
      value: json['value'],
      customerReportTypes: json['customer_report_type'] != null
          ? (json['customer_report_type'] as List)
              .map((item) => CustomerReportType.fromJson(item))
              .toList()
          : null,
    );
  }

  @override
  String toString() {
    return 'Filter(title: $title, value: $value, customerReportTypes: $customerReportTypes)';
  }
}

class CustomerReportType {
  final String title;
  final String value;

  CustomerReportType({
    required this.title,
    required this.value,
  });

  factory CustomerReportType.fromJson(Map<String, dynamic> json) {
    return CustomerReportType(
      title: json['title'],
      value: json['value'],
    );
  }

  @override
  String toString() {
    return 'CustomerReportType(title: $title, value: $value)';
  }
}
