import 'dart:async';
import 'dart:convert';

import 'package:battery_plus/battery_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geolocator/geolocator.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/presentation/login_screen/models/login_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LoginOneController extends GetxController {
  TextEditingController employeeCodeController = TextEditingController();

  TextEditingController passwordController = TextEditingController();

  Rx<bool> isShowPassword = true.obs;

  final String _loginDateKey = 'loginDate';

  final Dio dio = Dio();
  var isLoading = false.obs;

  RxString employeeCode = "0".obs;

  RxBool isLocationEnabled = false.obs;
  RxInt batteryLevel = 0.obs;
  RxString internetConnectivity = "".obs;
  RxString devicePlatform = "".obs;
  RxString osVersion = "".obs;
  RxString manufacturerName = "".obs;
  RxString modelName = "".obs;
  RxString deviceUUID = "".obs;

  RxDouble lat = 0.0.obs;
  RxDouble long = 0.0.obs;
  RxString locationMessage = ''.obs;

  final Rx<String> fcmToken = Rx<String>('');

  RxBool arePermissionsGranted = false.obs;

  var installedVersion = ''.obs;
  RxString appVersion = ''.obs;
  RxString buildVersion = ''.obs;
  RxString versionCode = ''.obs;

  var imageUrl = 'images/';
  var imgAppLogo = ''.obs;

  @override
  void onInit() async {
    super.onInit();

    // employeeCodeController.text = "emp0001";
    // passwordController.text = "0001";

    await fetchEmployeeData();
    getToken();
    getLocation();
    getBatteryLevel();
    fetchDeviceInfo();
    fetchAppVersion();
    await getInstalledVersion();

    imgAppLogo.value = await SharedPrefManager.instance
            .getString(ConstantValues.loginAppLogo) ??
        "";
    print("imgAppLogo.value: ${imgAppLogo.value}");
    print(imgAppLogo.value.isEmpty);
  }

  Future<void> fetchAppVersion() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      appVersion.value = packageInfo.version;
      buildVersion.value = packageInfo.buildNumber;
      versionCode.value = packageInfo.version;
    } catch (error) {
      print('Error fetching app version: $error');
    }
  }

  Future<void> getInstalledVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    installedVersion.value = packageInfo.version;
    print("installedVersion: ${installedVersion.value}");
  }

  @override
  void onClose() {
    super.onClose();
    employeeCodeController.dispose();
    passwordController.dispose();
  }

  Future<void> requestPermissions() async {
    final notificationsPermissionStatus =
        await Permission.notification.request();
    print("notificationsPermissionStatus: ${notificationsPermissionStatus}");
    final locationPermissionStatus = await Permission.location.request();
    arePermissionsGranted.value = notificationsPermissionStatus.isGranted &&
        locationPermissionStatus.isGranted;

    if (arePermissionsGranted == true) {
      print("arePermissionsGranted: ${arePermissionsGranted}");
      login();
    } else {
      showToastMessage("Please allow permissions to login");
    }
  }

  Future<void> requestPermission() async {
    final notificationPermissionStatus =
        await Permission.notification.request();
    print("notificationsPermissinStatus: ${notificationPermissionStatus}");

    final locationPermissionStatus = await Permission.location.request();
    arePermissionsGranted.value = notificationPermissionStatus.isGranted &&
        locationPermissionStatus.isGranted;

    if (arePermissionsGranted == true) {
      print("arePermissionsGranted: ${arePermissionsGranted}");
      login();
    } else {
      showToastMessage("Please allow permissions to login");
    }
  }

  Future<void> getLocation() async {
    Position position;
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();

    isLocationEnabled.value = serviceEnabled;

    if (!serviceEnabled) {
      locationMessage.value = "Location services are disabled";
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        locationMessage.value = 'Location permissions are denied.';
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      locationMessage.value =
          'Location permissions are permanently denied, we cannot request permissions.';
      return;
    }

    if (permission == LocationPermission.unableToDetermine) {}

    position = await Geolocator.getCurrentPosition();
    locationMessage.value =
        'Latitude: ${position.latitude}, Longitude: ${position.longitude}';

    lat.value = position.latitude;
    long.value = position.longitude;
  }

  void getToken() async {
    await FirebaseMessaging.instance.getToken().then((token) {
      print("token: ${token}");
      fcmToken.value = token ?? '';
    });
  }

  Future<void> getBatteryLevel() async {
    var battery = Battery();
    print(await battery.batteryLevel);

    batteryLevel.value = await battery.batteryLevel;

    final List<ConnectivityResult> connectivityResult =
        await (Connectivity().checkConnectivity());

    if (connectivityResult.contains(ConnectivityResult.mobile)) {
      internetConnectivity.value = "mobile";
    } else if (connectivityResult.contains(ConnectivityResult.wifi)) {
      internetConnectivity.value = "wifi";
    } else if (connectivityResult.contains(ConnectivityResult.none)) {
      // No available network types
    }
  }

  Future<void> fetchDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    if (GetPlatform.isAndroid) {
      devicePlatform.value = "Android";

      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      print('model ${androidInfo.model}');
      print('manufacturer ${androidInfo.manufacturer}');
      print('brand ${androidInfo.brand}');
      print('baseOS ${androidInfo.version.baseOS}');
      print('codename ${androidInfo.version.codename}');
      print('release ${androidInfo.version.release}');
      print('serialNumber ${androidInfo.serialNumber}');
      print('deviceId ${androidInfo.id}');

      deviceUUID.value = androidInfo.id;
      manufacturerName.value = androidInfo.brand;
      modelName.value = androidInfo.model;
      osVersion.value = androidInfo.version.release;

      final deviceInfoPlugin = DeviceInfoPlugin();
      final deviceInfo1 = await deviceInfoPlugin.deviceInfo;
      final allInfo = deviceInfo1.data;
      print("allInfo: ${allInfo}");
    } else if (GetPlatform.isIOS) {
      devicePlatform.value = "iOS";

      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      print("iosInfo: ${iosInfo}");

      deviceUUID.value = iosInfo.identifierForVendor ?? "";
      manufacturerName.value = iosInfo.systemName;
      modelName.value = iosInfo.systemName;
      osVersion.value = iosInfo.utsname.version;
    }

    print("devicePlatform: ${devicePlatform.value}");
  }

  Future<void> login() async {
    try {
      isLoading.value = true;
      EasyLoading.show(status: 'Loading...');

      Map<String, dynamic> deviceInfo = {
        'manufacturer': manufacturerName.value,
        'model': modelName.value,
        'device_type': devicePlatform.value,
        'os_version': osVersion.value,
        'uuid': deviceUUID.value,
        'fcm_token': fcmToken.value,
        'location': isLocationEnabled.value,
        'internet': internetConnectivity.value,
        'battery': batteryLevel.value,
        'buildVersion': buildVersion.value,
        'appVersion': installedVersion.value,
      };

      print("deviceInfo: ${deviceInfo}");

      String jsonDeviceInfo = jsonEncode(deviceInfo);

      Map<String, dynamic> data = {
        // 'company_code': '${companyCodeController.text}',
        'emp_code': '${employeeCode.value}',
        'emp_pin': '${passwordController.text}',
        'device_info': jsonDeviceInfo,
      };

      print("data: ${data}");

      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.loginEndpoint),
        data: data,
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      print(response.statusCode);

      print("response.data: ${response.data}");
      try {
        if (response.statusCode == 200) {
          UserInfo user = UserInfo.fromJson(response.data);
          print("Login Response ${user}");

          await storeUserInfoInSharedPreferences(response.data);
          await fetchEmployeeData();
          await fetchTeamMembers();
          await storeLoginDate();

          EasyLoading.dismiss();

          if (user.isCheckedIn) {
            await Get.toNamed(AppRoutes.syncDataScreen);
          } else {
            await Get.toNamed(AppRoutes.attendanceScreen);
          }
        } else {
          // Handle the case where response.data is null
          print('Response data is null');
          showToastMessage("${response.statusMessage}");
        }
      } catch (e) {
        // Handle any other parsing errors

        print('Error parsing response data: $e');
      }
      print("EasyLoading.show");
      EasyLoading.dismiss();
    } on DioException catch (e) {
      EasyLoading.dismiss();
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
          showToastMessage(e.response!.data["message"] ?? "No data found");
        } else {
          showToastMessage(
              '${e.response!.statusCode}: ${e.response!.statusMessage}');
        }
      }
    } catch (e) {
      EasyLoading.dismiss();
      print('Unexpected error: $e');
    }
  }

  // Method to store today's date in SharedPreferences
  Future<void> storeLoginDate() async {
    print("storeLoginDate called");
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final DateTime now = DateTime.now();
    print("Now: ${now}");
    final String formattedDate = '${now.year}-${now.month}-${now.day}';
    await prefs.setString(_loginDateKey, formattedDate);
  }

  // Method to retrieve stored login date from SharedPreferences
  Future<String?> getLoginDate() async {
    print("getLoginDate called");
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString(_loginDateKey);
  }

  String getTokenFromInput(String input) {
    print("getTokenFromInput called");
    List<String> parts = input.split('|');
    if (parts.length > 1) {
      return parts[1];
    } else {
      return input; // If no '|' found, return the original input
    }
  }

  Future<void> storeUserInfoInSharedPreferences(
      Map<String, dynamic> apiResponse) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    SharedPrefManager.instance.setBool(ConstantValues.isDailyLogOut, false);

    print("storeUserInfoInSharedPreferences called");
    // Store token
    final String token = getTokenFromInput(apiResponse['token']);
    await prefs.setString('token', token);
    print("token: ${token}");

    // Store isCheckedIn
    final bool isCheckedIn = apiResponse['isCheckedIn'];
    await prefs.setBool('isCheckedIn', isCheckedIn);
    print("isCheckedIn: ${isCheckedIn}");

    if (apiResponse['checkInType'] != null) {
      SharedPrefManager.instance
          .setInt(ConstantValues.checkInType, apiResponse['checkInType']);
    } else {
      SharedPrefManager.instance.setInt(ConstantValues.checkInType, 0);
    }

    // Store employee data
    final Map<String, dynamic> employeeData = apiResponse['employee'];
    final String employeeDataJson = json.encode(employeeData);
    await prefs.setString('employee', employeeDataJson);
    print("employeeData: ${employeeData}");

    // Store team members data
    final List<dynamic> teamMembers = employeeData['team_members'];
    final String teamMembersJson = json.encode(teamMembers);

    await prefs.setString('team_members', teamMembersJson);
    print("teamMembers: ${teamMembers}");
  }

  Future<String?> fetchEmployeeData() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? employeeDataJson = prefs.getString('employee');
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];
      employeeCode.value = empCode!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> fetchTeamMembers() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? teamMembersJson = prefs.getString('team_members');
    if (teamMembersJson != null) {
      return json.decode(teamMembersJson);
    } else {
      throw Exception('Team members data not found in SharedPreferences');
    }
  }
}
