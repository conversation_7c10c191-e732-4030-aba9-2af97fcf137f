import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/sfmDatabase/models/beat_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:sfm_new/widgets/custom_icon_button.dart';

import 'controller/super_stockist_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:dynamic_tabbar/dynamic_tabbar.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';

// ignore_for_file: must_be_immutable
class SuperStockistScreen extends GetWidget<SuperStockistController> {
  bool isScrollable = false;
  bool showNextIcon = true;
  bool showBackIcon = true;

  Widget? leading;

  Widget? trailing;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: SizedBox(
          width: double.maxFinite,
          child: Stack(
            children: [
              Column(
                children: [
                  Expanded(
                    child: Obx(
                      () {
                        List<TabData> tabs = [];
                        if (controller.isCustomerTypeActive('retailer')) {
                          tabs.add(
                            TabData(
                              index: 1,
                              title: Tab(
                                child: Text('lbl_retailer'.tr,
                                    style:
                                        CustomTextStyles.titleSmallBluegray900),
                              ),
                              content: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomSearchBar(
                                      customerType: CustomerType.retailer),
                                  SizedBox(height: 10),
                                  _buildRetailerList(),
                                  SizedBox(height: 10),
                                  Visibility(
                                    visible: controller.addBeat.value == 1
                                        ? true
                                        : false,
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                          bottom: 12, right: 12),
                                      child: _buildFloatingActionButton(5),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        if (controller.isCustomerTypeActive('distributor')) {
                          tabs.add(
                            TabData(
                              index: 2,
                              title: Tab(
                                child: Text('lbl_distributor'.tr,
                                    style:
                                        CustomTextStyles.titleSmallBluegray900),
                              ),
                              content: Column(
                                children: [
                                  CustomSearchBar(
                                      customerType: CustomerType.distributor),
                                  SizedBox(height: 10),
                                  _buildDistributorList(),
                                  SizedBox(height: 10),
                                  Visibility(
                                    visible:
                                        controller.addDistributor.value == 1
                                            ? true
                                            : false,
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                          bottom: 12, right: 12),
                                      child: _buildFloatingActionButton(3),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        if (controller.isCustomerTypeActive('ss')) {
                          tabs.add(
                            TabData(
                              index: 3,
                              title: Tab(
                                child: Text('lbl_ss'.tr,
                                    style:
                                        CustomTextStyles.titleSmallBluegray900),
                              ),
                              content: Column(
                                children: [
                                  CustomSearchBar(
                                      customerType: CustomerType.ss),
                                  SizedBox(height: 10),
                                  _buildCFList(),
                                  SizedBox(height: 10),
                                ],
                              ),
                            ),
                          );
                        }

                        if (controller.isCustomerTypeActive('dealer')) {
                          tabs.add(
                            TabData(
                              index: 4,
                              title: Tab(
                                child: Text('lbl_dealer'.tr,
                                    style:
                                        CustomTextStyles.titleSmallBluegray900),
                              ),
                              content: Column(
                                children: [
                                  CustomSearchBar(
                                      customerType: CustomerType.dealer),
                                  SizedBox(height: 10),
                                  _buildDealerList(),
                                  SizedBox(height: 10),
                                  Visibility(
                                    visible: controller.addDealer.value == 1
                                        ? true
                                        : false,
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                          bottom: 12, right: 12),
                                      child: _buildFloatingActionButton(4),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }

                        if (tabs.length > 0) {
                          return DynamicTabBarWidget(
                            dynamicTabs: tabs,
                            isScrollable: isScrollable,
                            onTabControllerUpdated: (controller) {
                              print("onTabControllerUpdated");
                            },
                            onTabChanged: (index) {
                              print("Tab changed: $index");
                              switch (index) {
                                case 1:
                                  controller.updateCurrentTab('retailer');
                                  break;
                                case 2:
                                  controller.updateCurrentTab('distributor');
                                  break;
                                case 3:
                                  controller.updateCurrentTab('dealer');
                                  break;
                                case 4:
                                  controller.updateCurrentTab('ss');
                                  break;
                              }
                            },
                            onAddTabMoveTo: MoveToTab.last,
                            labelPadding: EdgeInsets.only(left: 10, right: 10),
                            showBackIcon: showBackIcon,
                            showNextIcon: showNextIcon,
                            leading: leading,
                            trailing: trailing,
                            onTap: (value) {
                              print("Tab onTap event called");
                            },
                          );
                        } else {
                          return Container();
                        }
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton(int customerType) {
    // dist = 3 , dealer = 4, retailer = 5

    return CustomIconButton(
      height: 70.adaptSize,
      width: 70.adaptSize,
      padding: EdgeInsets.all(19.h),
      decoration: IconButtonStyleHelper.outlineOnErrorContainer,
      alignment: Alignment.bottomRight,
      child: CustomImageView(
        imagePath: ImageConstant.imgGroup29,
      ),
      onTap: () {
        print("Plus button pressed");
        SharedPrefManager.instance
            .setInt(ConstantValues.selectedAddCustType, customerType);
        if (customerType == 3 || customerType == 4) {
          SharedPrefManager.instance
              .setBool(ConstantValues.isFromProductShopInfo, false);
          Get.toNamed(AppRoutes.addDealerScreen);
        } else if (customerType == 5) {
          SharedPrefManager.instance
              .setBool(ConstantValues.isFromProductShopInfo, false);
          Get.toNamed(AppRoutes.addBeatScreen);
        }
      },
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "lbl_order".tr,
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildRetailerList() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.h),
        child: Obx(
          () {
            if (controller.isLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            } else if (controller.filteredBeatList.isEmpty) {
              return Center(
                child: Text(
                  'No beat found',
                  style: TextStyle(fontSize: 16),
                ),
              );
            } else {
              return ListView.separated(
                physics: BouncingScrollPhysics(),
                shrinkWrap: true,
                separatorBuilder: (
                  context,
                  index,
                ) {
                  return SizedBox(
                    height: 10.v,
                  );
                },
                itemCount: controller.filteredBeatList.length,
                itemBuilder: (context, index) => BeatListItem(
                  beat: controller.filteredBeatList[index],
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildDistributorList() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.h),
        child: Obx(
          () {
            if (controller.isLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            } else if (controller.distributorList.isEmpty) {
              return Center(
                child: Text(
                  'No distributor found',
                  style: TextStyle(fontSize: 16),
                ),
              );
            } else {
              return ListView.separated(
                physics: BouncingScrollPhysics(),
                shrinkWrap: true,
                separatorBuilder: (
                  context,
                  index,
                ) {
                  return SizedBox(
                    height: 10.v,
                  );
                },
                itemCount: controller.distributorList.length,
                itemBuilder: (context, index) => CustomCustomerListItem(
                  customer: controller.distributorList[index],
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildCFList() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.h),
        child: Obx(
          () {
            if (controller.isLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            } else if (controller.cfList.isEmpty) {
              return Center(
                child: Text(
                  'No SS found',
                  style: TextStyle(fontSize: 16),
                ),
              );
            } else {
              return ListView.separated(
                physics: BouncingScrollPhysics(),
                shrinkWrap: true,
                separatorBuilder: (
                  context,
                  index,
                ) {
                  return SizedBox(
                    height: 10.v,
                  );
                },
                itemCount: controller.cfList.length,
                itemBuilder: (context, index) => CustomSSListItem(
                  customer: controller.cfList[index],
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildDealerList() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.h),
        child: Obx(
          () {
            if (controller.isLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            } else if (controller.dealerList.isEmpty) {
              return Center(
                child: Text(
                  'No Dealer found',
                  style: TextStyle(fontSize: 16),
                ),
              );
            } else {
              return ListView.separated(
                physics: BouncingScrollPhysics(),
                shrinkWrap: true,
                separatorBuilder: (
                  context,
                  index,
                ) {
                  return SizedBox(
                    height: 10.v,
                  );
                },
                itemCount: controller.dealerList.length,
                itemBuilder: (context, index) => CustomDealerListItem(
                  customer: controller.dealerList[index],
                ),
              );
            }
          },
        ),
      ),
    );
  }
}

class CustomDealerListItem extends StatelessWidget {
  final Customer customer;
  final SuperStockistController controller = Get.put(SuperStockistController());

  CustomDealerListItem({
    required this.customer,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        SharedPrefManager.instance
            .setString(ConstantValues.selectedCustCode, customer.cmCode!);
        SharedPrefManager.instance.setString(
            ConstantValues.selectedCustRelationCode, customer.cmRelationCode!);
        SharedPrefManager.instance
            .setString(ConstantValues.selectedCustName, customer.cmName!);
        SharedPrefManager.instance.setInt(ConstantValues.selectedCustType, 4);
        SharedPrefManager.instance.setBool(ConstantValues.isFromBeat, false);

        Get.toNamed(AppRoutes.productScreen);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 9.h,
          vertical: 15.v,
        ),
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 4.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [],
              ),
            ),
            SizedBox(height: 4.v),
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: customer.cmName,
                    style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                  ),
                ],
              ),
              textAlign: TextAlign.left,
            ),
          ],
        ),
      ),
    );
  }
}

class CustomSSListItem extends StatelessWidget {
  final Customer customer;
  final SuperStockistController controller = Get.put(SuperStockistController());

  CustomSSListItem({
    required this.customer,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        SharedPrefManager.instance
            .setString(ConstantValues.selectedCustCode, customer.cmCode!);
        SharedPrefManager.instance.setString(
            ConstantValues.selectedCustRelationCode, customer.cmRelationCode!);

        SharedPrefManager.instance
            .setString(ConstantValues.selectedCustName, customer.cmName!);

        SharedPrefManager.instance.setInt(ConstantValues.selectedCustType, 2);

        SharedPrefManager.instance.setBool(ConstantValues.isFromBeat, false);

        Get.toNamed(AppRoutes.productScreen);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 9.h,
          vertical: 15.v,
        ),
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 4.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [],
              ),
            ),
            SizedBox(height: 4.v),
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: customer.cmName,
                    style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                  ),
                ],
              ),
              textAlign: TextAlign.left,
            ),
          ],
        ),
      ),
    );
  }
}

class CustomCustomerListItem extends StatelessWidget {
  final Customer customer;
  final SuperStockistController controller = Get.put(SuperStockistController());

  CustomCustomerListItem({
    required this.customer,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        SharedPrefManager.instance
            .setString(ConstantValues.selectedCustCode, customer.cmCode!);
        SharedPrefManager.instance.setString(
            ConstantValues.selectedCustRelationCode, customer.cmRelationCode!);

        SharedPrefManager.instance
            .setString(ConstantValues.selectedCustName, customer.cmName!);

        SharedPrefManager.instance.setInt(ConstantValues.selectedCustType, 3);

        SharedPrefManager.instance.setBool(ConstantValues.isFromBeat, false);

        SharedPrefManager.instance.setInt(ConstantValues.syncDays, 7);

        Get.toNamed(AppRoutes.productScreen);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 9.h,
          vertical: 15.v,
        ),
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 4.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [],
              ),
            ),
            SizedBox(height: 4.v),
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: customer.cmName,
                    style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                  ),
                ],
              ),
              textAlign: TextAlign.left,
            ),
          ],
        ),
      ),
    );
  }
}

class BeatListItem extends StatelessWidget {
  final Beat beat;
  final SuperStockistController controller = Get.put(SuperStockistController());

  BeatListItem({
    required this.beat,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (beat.beat_status == 1) {
          print("beat.beat_code: ${beat.beat_code}");
          print("beat.selectedBeatName: ${beat.beat_name}");

          SharedPrefManager.instance
              .setString(ConstantValues.selectedCustCode, beat.beat_code!);

          SharedPrefManager.instance
              .setString(ConstantValues.selectedCustName, beat.beat_name!);

          SharedPrefManager.instance.setInt(ConstantValues.selectedCustType, 5);

          SharedPrefManager.instance.setBool(ConstantValues.isFromBeat, true);

          Get.toNamed(AppRoutes.beatScreen);
        } else {
          showToastMessage("Please approve this beat to proceed");
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 9.h,
          vertical: 15.v,
        ),
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Row(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(right: 4.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [],
                  ),
                ),
                SizedBox(height: 4.v),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: beat.beat_name,
                        style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                      ),
                    ],
                  ),
                  textAlign: TextAlign.left,
                ),
              ],
            ),
            Spacer(),
            Visibility(
              visible: beat.beat_status == 0 ? true : false,
              child: CustomImageView(
                svgPath: ImageConstant.imgNotSynced,
                color: Colors.red,
                height: 15.adaptSize,
                width: 15.adaptSize,
                margin: EdgeInsets.only(bottom: 2.v),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomSearchBar extends StatelessWidget {
  final CustomerType customerType;

  CustomSearchBar({
    required this.customerType,
  });

  final SuperStockistController controller = Get.put(SuperStockistController());

  @override
  Widget build(BuildContext context) {
    controller.currentCustomerType.value = customerType;
    print("customerType: ${controller.currentCustomerType.value}");
    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 12),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.5),
              spreadRadius: 2,
              blurRadius: 7,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Icon(
                Icons.search,
                color: Colors.grey,
              ),
            ),
            Expanded(
              child: TextField(
                controller: controller.searchController,
                style: CustomTextStyles.bodyMediumBluegray700,
                decoration: InputDecoration(
                  hintText: 'Search',
                  hintStyle: TextStyle(color: Colors.grey),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 10.0),
                ),
                onChanged: (value) {
                  print('Search value: $value');
                  controller.updateSearchText(value, customerType);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
