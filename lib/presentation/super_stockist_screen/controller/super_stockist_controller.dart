import 'dart:async';
import 'dart:convert';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:faker/faker.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/beat_model.dart';
import 'package:sfm_new/sfmDatabase/models/config_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:dio/dio.dart' as dio_package;
import 'package:sqflite/sqflite.dart';

enum CustomerType { retailer, distributor, ss, dealer }

class SuperStockistController extends GetxController
    with GetSingleTickerProviderStateMixin {
  TextEditingController searchController = TextEditingController();

  Faker faker = Faker();

  RxList<Customer> customerList = <Customer>[].obs;
  RxList<Customer> retailerList = <Customer>[].obs;
  RxList<Customer> distributorList = <Customer>[].obs;
  RxList<Customer> cfList = <Customer>[].obs;
  RxList<Customer> dealerList = <Customer>[].obs;
  RxList<Beat> beatList = <Beat>[].obs;
  RxList<Beat> filteredBeatList = <Beat>[].obs;

  Rx<CustomerType> currentCustomerType = CustomerType.retailer.obs;

  var isLoading = true.obs;
  final searchText = ''.obs;

  late TabController tabviewController =
      Get.put(TabController(vsync: this, length: 3));

  RxList<Config> customerTypeList = <Config>[].obs;
  var customerTypes = <String, dynamic>{}.obs;

  RxList<Config> configList = <Config>[].obs;
  RxString addDealerStatus = ''.obs;
  RxString addDistributorStatus = ''.obs;

  final Connectivity _connectivity = Connectivity();

  bool isCustomerAPISuccess = false;
  RxString employeeToken = "".obs;

  RxInt addBeat = 0.obs;
  RxInt addDistributor = 0.obs;
  RxInt addDealer = 0.obs;
  RxInt addSS = 0.obs;

  RxString currentTab = ''.obs;

  @override
  void onInit() async {
    super.onInit();

    EasyLoading.dismiss();

    await fetchedToken();
    await fetchConfig();

    final List<ConnectivityResult> connectivityResult =
        await (Connectivity().checkConnectivity());

    if (connectivityResult.contains(ConnectivityResult.none)) {
      showToastMessage("Please check your internet connection");
      await fetchBeats();
    } else {
      await getBeatsAPI();
    }

    await fetchRetailer();
  }

  @override
  void onClose() {
    super.onClose();
    searchController.dispose();
  }

  // Method to update the current tab
  void updateCurrentTab(String tabName) {
    currentTab.value = tabName;
  }

  // Check if the FAB should be visible
  bool isFabVisible() {
    if (currentTab.value == 'retailer' && addBeat.value == 1) {
      return true;
    } else if (currentTab.value == 'distributor' && addDistributor.value == 1) {
      return true;
    } else if (currentTab.value == 'dealer' && addDealer.value == 1) {
      return true;
    } else if (currentTab.value == 'ss' && addSS.value == 1) {
      return true;
    }
    return false;
  }

  Future<bool> checkInternetConnectivity() async {
    var connectivityResult = await _connectivity.checkConnectivity();
    if (connectivityResult != ConnectivityResult.none) {
      syncCustomersWithServer();
    }
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> syncCustomersWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedCustomers = await db.query(
        '${TableValues.tableCustomer}',
        where: '${TableValues.customerSyncStatus} = ?',
        whereArgs: [0]);

    if (unsyncedCustomers.isNotEmpty) {
      print('Syncing customers with the server...');
      print("unsyncedCustomers: ${unsyncedCustomers}");

      for (final customer in unsyncedCustomers) {
        try {
          print("Customer: ${customer}");
          await addCustomerAPI(customer);

          if (isCustomerAPISuccess == true) {
            await db.update('${TableValues.tableCustomer}',
                {'${TableValues.customerSyncStatus}': 1},
                where: '${TableValues.customerCode} = ?',
                whereArgs: [customer['${TableValues.customerCode}']]);
            print(
                'Customer with ID ${customer['${TableValues.customerCode}']} synced successfully');
          } else {
            print(
                'Error syncing Customer with ID ${customer['${TableValues.customerCode}']}');
          }
        } catch (error) {
          print(
              'Error syncing Customer with ID ${customer['${TableValues.customerCode}']}: $error');
        }
      }

      fetchRetailer();
    } else {
      print('No customers to sync.');
    }
  }

  Future<void> addCustomerAPI(Map<String, dynamic> customer) async {
    try {
      Map<String, dynamic> data = {};

      String strImage = "${customer['cm_image_relation']}";
      List<String> strImagesList = strImage.split(",");
      print("strImagesList: ${strImagesList}");

      data = {
        "cm_code": "${customer['cm_code']}",
        "cm_name": "${customer['cm_name']}",
        "cm_mobile": customer['cm_mobile'],
        "cm_mobile2": customer['cm_mobile2'],
        "cm_email": "${customer['cm_email']}",
        "cm_address": "${customer['cm_address']}",
        "cm_pincode": customer['cm_pincode'],
        "cm_gst": "${customer['cm_gst']}",
        "cm_pan": "${customer['cm_pan']}",
        "latitude": "${customer['cm_lat']}",
        "longitude": "${customer['cm_long']}",
        "cm_town_id": customer['cm_town_id'],
        "cm_outstanding_amount": customer['cm_outstanding_amount'],
        "cm_contact_person": "${customer['cm_contact_person']}",
        "cm_area": "${customer['cm_area']}",
        "cm_relation_code": "${customer['cm_relation_code']}",
        "cm_type": customer['cm_type'],
        "birth_date": "${customer['cm_dob']}",
        "anni_date": "${customer['cm_anniversary']}",
      };

      print("data: ${data}");

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };

      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.postAddCustomerEndpoint),
        data: data,
        options: dio_package.Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.data);
      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            isCustomerAPISuccess = true;
            EasyLoading.dismiss();
          }
        } else {
          isCustomerAPISuccess = false;
          print('Response data is null');
        }
      } catch (e) {
        isCustomerAPISuccess = false;
        print('Error parsing response data: $e');
      }
    } on dio_package.DioException catch (e) {
      isCustomerAPISuccess = false;
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        print(e.response!.data["message"] ?? "No data found");
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
    } finally {
      EasyLoading.dismiss();
    }
  }

  Future<void> getBeatsAPI() async {
    EasyLoading.show(status: 'Loading...');
    try {
      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };
      final response = await ApiClient.dioClient
          .get(
        ApiClient.getUrl(ApiClient.getBeatRouteEndpoint),
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });

      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            print(response.data);

            final List<Map<String, dynamic>> beatRoute =
                List<Map<String, dynamic>>.from(response.data['beat_route']);
            print(beatRoute);

            // Create a new list containing only the desired fields
            List<Map<String, dynamic>> filteredBeats = beatRoute.map((beat) {
              String beatCode = '';
              String beatName = '';
              int beatStatus = 0;
              String bdmDate = '';
              int bdmNumberwise = 0;
              int bdmType = 0;
              String bdmDay = '';
              int bdmPeriod = 0;
              int beatSyncStatus = 1;

              if (beat['beat'] != null && beat['beat']['beat_code'] != null) {
                try {
                  beatCode = convertValue(beat['beat']['beat_code'], String);
                } catch (e) {
                  print("Error converting beat_code: $e");
                }
              }

              if (beat['beat'] != null && beat['beat']['beat_name'] != null) {
                try {
                  beatName = convertValue(beat['beat']['beat_name'], String);
                } catch (e) {
                  print("Error converting beat_name: $e");
                }
              }

              if (beat['beat'] != null && beat['beat']['beat_status'] != null) {
                try {
                  beatStatus = convertValue(beat['beat']['beat_status'], int);
                } catch (e) {
                  print("Error converting beat_status: $e");
                }
              }

              if (beat['bdm_date'] != null) {
                try {
                  bdmDate = convertValue(beat['bdm_date'], String);
                } catch (e) {
                  print("Error converting bdm_date: $e");
                }
              }

              if (beat['bdm_numberwise'] != null) {
                try {
                  bdmNumberwise = convertValue(beat['bdm_numberwise'], int);
                } catch (e) {
                  print("Error converting bdm_numberwise: $e");
                }
              }

              if (beat['bdm_type'] != null) {
                try {
                  bdmType = convertValue(beat['bdm_type'], int);
                } catch (e) {
                  print("Error converting bdm_type: $e");
                }
              }

              if (beat['bdm_day'] != null) {
                try {
                  bdmDay = convertValue(beat['bdm_day'], String);
                } catch (e) {
                  print("Error converting bdm_day: $e");
                }
              }

              if (beat['bdm_period'] != null) {
                try {
                  bdmPeriod = convertValue(beat['bdm_period'], int);
                } catch (e) {
                  print("Error converting bdm_period: $e");
                }
              }

              return {
                'beat_code': beatCode,
                'beat_name': beatName,
                'beat_status': beatStatus,
                'bdm_date': bdmDate,
                'bdm_numberwise': bdmNumberwise,
                'bdm_type': bdmType,
                'bdm_day': bdmDay,
                'bdm_period': bdmPeriod,
                'beat_sync_status': beatSyncStatus,
              };
            }).toList();

            print("filteredBeats: ${filteredBeats}");

            await insertValues(
                toTable: "${TableValues.tableBeats}", withData: filteredBeats);

            List<String> apiBeatCodes =
                filteredBeats.map((beat) => "${beat["beat_code"]}").toList();
            print("apiBeatCodes: ${apiBeatCodes}");

            await deleteDataNotInList(
                fromTable: "${TableValues.tableBeats}",
                forCode: "${TableValues.beatCode}",
                withData: apiBeatCodes);

            await fetchBeats();
          }
        } else {
          print('Response data is null');
        }
      } catch (e) {
        print('Error parsing response data: $e');
      }
    } on DioException catch (e) {
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
      await ApiLogger.logError(
        apiName: ApiClient.getBeatRouteEndpoint,
        apiRequestData: null,
        apiResponse: 'DioException: $e',
      );
    } on TimeoutException catch (e) {
      print('Request timed out: $e');
      await ApiLogger.logError(
        apiName: ApiClient.getBeatRouteEndpoint,
        apiRequestData: null,
        apiResponse: 'TimeoutException: $e',
      );
    } catch (e) {
      print(e);
    } finally {
      EasyLoading.dismiss();
    }
  }

  static Future<void> deleteDataNotInList(
      {required String fromTable,
      required String forCode,
      required List<String> withData}) async {
    final db = await DatabaseProvider.database;
    final beatCodesString = withData.map((code) => "'$code'").join(', ');
    await db.delete("${fromTable}",
        where: '${forCode} NOT IN ($beatCodesString)');
  }

  static Future<void> insertValues(
      {required String toTable,
      required List<Map<String, dynamic>> withData}) async {
    final db = await DatabaseProvider.database;
    try {
      await db.transaction((txn) async {
        Batch batch = txn.batch();
        for (var data in withData) {
          batch.insert(toTable, data,
              conflictAlgorithm: ConflictAlgorithm.replace);
        }
        await batch.commit(noResult: false);
      });
    } catch (e) {
      throw Exception('Failed to insert data: $e');
    }
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer
  Future<void> fetchRetailer() async {
    isLoading.value = true;
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCustomer}");
    customerList.value = List.generate(maps.length, (index) {
      return Customer.fromMap(maps[index]);
    });
    print("customerList: ${customerList}");

    retailerList.value = customerList
        .where((customer) => customer.cmType == 5)
        .toList()
      ..sort((a, b) => a.cmName!.compareTo(b.cmName!));
    print("retailerList: ${retailerList}");

    distributorList.value = customerList
        .where((customer) => customer.cmType == 3)
        .toList()
      ..sort((a, b) => a.cmName!.compareTo(b.cmName!));
    print("distributorList: ${distributorList}");

    cfList.value = customerList
        .where((customer) => customer.cmType == 2)
        .toList()
      ..sort((a, b) => a.cmName!.compareTo(b.cmName!));
    print("cfList: ${cfList}");

    dealerList.value = customerList
        .where((customer) => customer.cmType == 4)
        .toList()
      ..sort((a, b) => a.cmName!.compareTo(b.cmName!));
    print("dealerList: ${dealerList}");

    EasyLoading.dismiss();
    isLoading.value = false;
  }

  Future<void> fetchConfig() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableConfig}");
    print(maps);
    customerTypeList.value = List.generate(maps.length, (index) {
      return Config.fromJson(maps[index]);
    });
    print("customerTypeList: ${customerTypeList}");

    if (customerTypeList.isNotEmpty) {
      final customerTypeConfig = customerTypeList.firstWhere(
        (config) => config.config_key == 'customer_types',
      );
      print("customerTypeConfig: ${customerTypeConfig}");
      customerTypes.value = json.decode(customerTypeConfig.config_value!);

      final addBeatConfig = customerTypeList.firstWhere(
        (config) => config.config_key == 'add_beat',
      );
      print("addBeatConfig: ${addBeatConfig}");
      addBeat.value = json.decode(addBeatConfig.config_value!);

      final addDistributorConfig = customerTypeList.firstWhere(
        (config) => config.config_key == 'add_distributor',
      );
      print("addDistributorConfig: ${addDistributorConfig}");
      addDistributor.value = json.decode(addDistributorConfig.config_value!);

      final addDealerConfig = customerTypeList.firstWhere(
        (config) => config.config_key == 'add_dealer',
      );
      print("addDealerConfig: ${addDealerConfig}");
      addDealer.value = json.decode(addDealerConfig.config_value!);
    }
  }

  bool isCustomerTypeActive(String type) {
    bool isStatus = false;
    print("customerTypes: ${customerTypes}");
    print("type123: ${type}");
    if (customerTypes.isNotEmpty) {
      if (customerTypes[type]['status'] == 1) {
        isStatus = true;
      } else {
        isStatus = false;
      }
    }

    return isStatus;
  }

  void updateSearchText(String newText, CustomerType selectedCustType) {
    searchText.value = newText;

    print("newText: ${newText}");
    print("selectedCustType: ${selectedCustType}");

    if (selectedCustType == CustomerType.retailer) {
      filteredBeatList.value = beatList
          .where((beat) => beat.beat_name!
              .toLowerCase()
              .contains(searchText.value.toLowerCase()))
          .toList();
      print("filteredBeatList: ${filteredBeatList}");
    } else if (selectedCustType == CustomerType.distributor) {
      distributorList.value = customerList
          .where((customer) =>
              customer.cmType == 3 &&
              customer.cmName!
                  .toLowerCase()
                  .contains(searchText.value.toLowerCase()))
          .toList();
      print("distributorList: ${distributorList}");
    } else if (selectedCustType == CustomerType.ss) {
      cfList.value = customerList
          .where((customer) =>
              customer.cmType == 2 &&
              customer.cmName!
                  .toLowerCase()
                  .contains(searchText.value.toLowerCase()))
          .toList();
      print("cfList: ${cfList}");
    } else if (selectedCustType == CustomerType.dealer) {
      dealerList.value = customerList
          .where((customer) =>
              customer.cmType == 4 &&
              customer.cmName!
                  .toLowerCase()
                  .contains(searchText.value.toLowerCase()))
          .toList();
      print("dealerList: ${dealerList}");
    }
  }

  Future<void> fetchBeats() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableBeats}");
    beatList.value = List.generate(maps.length, (index) {
      return Beat.fromMap(maps[index]);
    });

    filteredBeatList.value = beatList.toList()
      ..sort((a, b) => a.beat_name!.compareTo(b.beat_name!));

    print("beatList: ${beatList}");
    EasyLoading.dismiss();
  }
}

dynamic convertValue(dynamic value, [Type targetType = String]) {
  try {
    if (targetType == int) {
      String stringValue = value is String ? value : value.toString();
      return int.tryParse(stringValue) ?? 0;
    } else if (targetType == double) {
      String stringValue = value is String ? value : value.toString();
      return double.tryParse(stringValue) ?? 0.0;
    } else {
      return value;
    }
  } catch (e) {
    print("Error converting value to $targetType: $e");
    return targetType == int
        ? 0
        : targetType == double
            ? 0.0
            : "";
  }
}
