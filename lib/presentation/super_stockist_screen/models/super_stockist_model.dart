/// This class defines the variables used in the [super_stockist_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class RetailerModel {
  final int id;
  final String name;
  final String address;
  final String city;
  final String phoneNumber;

  RetailerModel({
    required this.id,
    required this.name,
    required this.address,
    required this.city,
    required this.phoneNumber,
  });
}

class DistributorModel {
  final int id;
  final int name;
  final int address;
  final int city;
  final String phoneNumber;

  DistributorModel({
    required this.id,
    required this.name,
    required this.address,
    required this.city,
    required this.phoneNumber,
  });
}

class CFModel {
  final int id;
  final int name;
  final int address;
  final int city;
  final String phoneNumber;

  CFModel({
    required this.id,
    required this.name,
    required this.address,
    required this.city,
    required this.phoneNumber,
  });
}
