// ignore_for_file: unused_local_variable, invalid_use_of_protected_member, unnecessary_null_comparison

import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/add_complain_screen/models/add_complain_model.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

import 'dart:async';
import 'dart:io';

import 'package:image/image.dart' as img;
import 'package:sfm_new/presentation/payment_screen/controller/payment_controller.dart';
import 'package:sfm_new/presentation/product_screen/controller/product_controller.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/bank_model.dart';
import 'package:sfm_new/sfmDatabase/models/claim_complain_type_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:sfm_new/sfmDatabase/models/payment_collection_model.dart';
import 'package:sfm_new/sfmDatabase/models/payment_condition_model.dart';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';

enum PaymentTypes { cash, cheque, netbanking }

class Pair {
  final String text;
  final IconData icon;
  const Pair(this.text, this.icon);

  @override
  String toString() {
    return text;
  }
}

class AddPaymentController extends GetxController {
  TextEditingController clientTypeController = TextEditingController();
  TextEditingController clientNameController = TextEditingController();
  TextEditingController paymentConditionController = TextEditingController();

  TextEditingController accountNoController = TextEditingController();
  TextEditingController chequeNoController = TextEditingController();
  TextEditingController chequeDateController = TextEditingController();

  TextEditingController utrController = TextEditingController();
  TextEditingController selectDateController = TextEditingController();

  TextEditingController amountController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();

  Rx<AddComplainModel> addComplainModelObj = AddComplainModel().obs;
  SelectionPopupModel? selectedDropDownValue;
  SelectionPopupModel? selectedDropDownValue1;

  final ImagePicker _picker = ImagePicker();

  Rx<File?> imageFile = Rx<File?>(null);
  String storedBase64Image = "";

  RxList<ClaimComplainType> complainTypeList = <ClaimComplainType>[].obs;
  RxList<Customer> customerList = <Customer>[].obs;
  RxList<Bank> bankList = <Bank>[].obs;
  RxList<PaymentCondition> paymentConditionList = <PaymentCondition>[].obs;

  List<String> claimComplainList = ['Claim', 'Complain'];

  PaymentTypes currentPaymentType = PaymentTypes.cash;

  RxInt selectedClientType = 0.obs;
  RxString selectedClientName = ''.obs;
  RxInt selectedPaymentCondition = 0.obs;
  RxInt selectedBankID = 0.obs;
  RxString employeeCode = ''.obs;

  RxInt selectedPaymentType = 1.obs;

  RxString employeeToken = "".obs;
  var selectedOption = "Cash".obs;

  RxString selectedDate = "Select Date".obs;
  RxString selectedChequeDate = "Cheque Date".obs;

  RxString finalChequeDate = ''.obs;
  RxString isFromNormalPaymentName = ''.obs;

  List<String> clientTypeList = [
    'SS',
    'Distributor',
    'Dealer',
    'Retailer',
  ];
  // RxInt selectedClientType = 0.obs;
  // RxInt selectedClaimComplainID = 0.obs;
  // RxString selectedCustomerCode = "0".obs;

  bool? isFromNormalAddPayment = false;

  final Rx<String?> base64Image = Rx(null);

  // final Dio dio = Dio();
  var isLoading = false.obs;

  // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer

  @override
  void onInit() async {
    super.onInit();

    EasyLoading.show(status: 'Loading...');

    selectedClientType.value = 0;
    selectedClientName.value = "1";

    await fetchClaimComplainType();
    await fetchCustomers();

    await fetchBanks();
    await fetchPaymentCondition();

    await fetchEmployeeData();

    await fetchedToken();

    isFromNormalAddPayment = await SharedPrefManager.instance
        .getBool(ConstantValues.isFromNormalAddPayment);

    if (isFromNormalAddPayment == true) {
    } else {
      selectedClientType.value = (await SharedPrefManager.instance
          .getInt(ConstantValues.selectedCustType))!;
      print("selectedClientType: ${selectedClientType.value}");

      isFromNormalPaymentName.value = (await SharedPrefManager.instance
          .getString(ConstantValues.selectedCustName))!;
      print("isFromNormalPaymentName: ${isFromNormalPaymentName.value}");

      selectedClientName.value = (await SharedPrefManager.instance
          .getString(ConstantValues.selectedCustCode))!;
      print("isFromNormalPaymentName: ${selectedClientName.value}");
    }

    // (await SharedPrefManager.instance.getInt(ConstantValues.checkInType))!;

    print("isFromNormalAddPayment: ${isFromNormalAddPayment}");
  }

  @override
  void onClose() {
    super.onClose();
    // claimComplainController.dispose();
  }

  void selectOption(String option) {
    selectedOption.value = option;
    print("selectOption: ${selectedOption.value}");
  }

  Future<void> pickImage(ImageSource source) async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      File compressedImage = await compressImage(File(image.path));
      imageFile.value = compressedImage;

      imageToBase64(imageFile.value!);
    }
  }

  Future<File> compressImage(File imageFile) async {
    int maxSize = 500; 
    int quality = 75; 

    List<int> compressedBytes = await FlutterImageCompress.compressWithList(
      imageFile.readAsBytesSync(),
      minHeight: 1920, 
      minWidth: 1080,
      quality: quality,
    );

    return File(imageFile.path)..writeAsBytesSync(compressedBytes);
  }

  Future<void> fetchClaimComplainType() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableClaimComplainType}");
    print(maps);
    complainTypeList.value = List.generate(maps.length, (index) {
      return ClaimComplainType.fromMap(maps[index]);
    });
    print(complainTypeList.value);
  }

  Future<void> fetchCustomers() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCustomer}");
    print(maps);
    customerList.value = List.generate(maps.length, (index) {
      return Customer.fromMap(maps[index]);
    });
    print(customerList.value);
  }

  Future<void> fetchBanks() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableBank}");
    print(maps);
    bankList.value = List.generate(maps.length, (index) {
      return Bank.fromMap(maps[index]);
    });
    print(bankList.value);
  }

  Future<void> fetchPaymentCondition() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tablePaymentCondition}");
    print(maps);
    paymentConditionList.value = List.generate(maps.length, (index) {
      return PaymentCondition.fromMap(maps[index]);
    });
    print(paymentConditionList.value);
  }

  // Function to return id where selectedType == cct_type_name
  int? getClaimComplainIdFromTypeName(String selectedType) {
    try {
      print(selectedType);
      final claimComplainType = complainTypeList.firstWhere(
        (ccType) => ccType.cct_type_name == selectedType,
      );
      return claimComplainType.cct_id;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getClaimComplainIdFromTypeName: $e');
      }
      return null;
    }
  }

  String? getCustomerCodeFromTypeName(String selectedType) {
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmName == selectedType,
      );
      return customerType.cmCode;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerCodeFromTypeName: $e');
      }
      return null;
    }
  }

  // Function to return id where selectedType == cct_type_name
  int? getBankNameFromID(String selectedName) {
    try {
      print(selectedName);
      final bankData = bankList.firstWhere(
        (bank) => bank.bank_name == selectedName,
      );
      return bankData.bank_id;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getBankNameFromID: $e');
      }
      return null;
    }
  }

  int? getPaymentConditionFromID(String selectedName) {
    try {
      print(selectedName);
      final pcData = paymentConditionList.firstWhere(
        (bank) => bank.pc_name == selectedName,
      );
      return pcData.pc_id;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getPaymentConditionFromID: $e');
      }
      return null;
    }
  }

  // Function to fetch user data from shared preferences
  Future<Map<String, dynamic>?> getUserData() async {
    String? userDataString =
        await SharedPrefManager.instance.getString(ConstantValues.userData) ??
            "";
    if (userDataString != null) {
      return Map<String, dynamic>.from(
        Map<String, dynamic>.fromIterable(
          userDataString.replaceAll('{', '').replaceAll('}', '').split(','),
          key: (e) => e.split(':')[0].trim(),
          value: (e) => e.split(':')[1].trim(),
        ),
      );
    } else {
      return null;
    }
  }

  String generateUUID() {
    var uuid = Uuid();
    return uuid.v4();
  }

  Future<String?> fetchEmployeeData() async {
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.employee) ??
            "";
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];
      print("empCode: ${empCode}");
      employeeCode.value = empCode!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  Future<void> addPaymentToDB() async {
    final db = await DatabaseProvider.database;

    String formattedDateTime =
        DateFormat("yyyy-MM-ddTHH:mm:ss.000000'Z'").format(DateTime.now());
    print(formattedDateTime);

    DateTime now = DateTime.now();
    String dateString = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);

    String uuid = generateUUID();

    print(uuid);
    print(selectedClientName.value);
    print(selectedPaymentType.value);
    print(double.tryParse(amountController.text) ?? 0.0);
    print(employeeCode.value);
    print(selectedPaymentCondition.value);

    var paymentAdded = Payment(
      // id: 0,
      code: uuid,
      cmCode: selectedClientName.value,
      type: selectedPaymentType.value,
      chequeDate: finalChequeDate.value,
      amount: double.tryParse(amountController.text) ?? 0.0,
      bankId: selectedBankID.value,
      chequeNo: chequeNoController.text,
      note: descriptionController.text,
      empCode: employeeCode.value,
      accountNo: accountNoController.text,
      chequePhoto: storedBase64Image,
      conditionId: selectedPaymentCondition.value,
      status: 0,
      createdAt: dateString,
      updatedAt: dateString,
      syncStatus: 0,
    );

    print("payment: $paymentAdded");

   
    try {
      await db.insert(
        '${TableValues.tablePaymentCollection}',
        paymentAdded.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      if (isFromNormalAddPayment == true) {
        final payment = Get.find<PaymentController>();
        payment.checkInternetConnectivity();
      } else {
        final payment = Get.find<ProductController>();
        payment.checkInternetConnectivity();
      }

      showToastMessage("Payment added successfully");
  

      Get.back();
    } catch (e) {
      print("Error adding payment to database: $e");

      // Log the error to a log file
      await ApiLogger.logError(
        apiName: 'addPaymentToDB',
        apiRequestData: paymentAdded.toMap(),
        apiResponse: 'Error: $e',
      );

      showToastMessage("Failed to add payment. Please try again.");
    }
  }


  Future<void> requestCameraPermission() async {
    EasyLoading.show(status: 'Loading...');
    final status = await Permission.camera.request();
    if (await Permission.camera.status.isGranted) {
      // Permission already granted, proceed
      await pickImage(ImageSource.camera);
    } else if (await Permission.camera.request().isGranted) {
      // Permission requested and granted, proceed
      await pickImage(ImageSource.camera);
    } else {
      Get.snackbar(
        "Camera Permission",
        "Please grant camera permission to proceed.",
        snackPosition: SnackPosition.BOTTOM,
      );
    }
    EasyLoading.dismiss();
  }

  Future<String> imageToBase64(File imageFile) async {


    Uint8List imageBytes = await imageFile.readAsBytes();

    // Decode the image
    img.Image? image = img.decodeImage(imageBytes);

    if (image != null) {
      // Compress the image if its size exceeds 500KB
      if (image.lengthInBytes > 500 * 1024) {
        image = img.copyResize(image,
            width: image.width ~/ 2, height: image.height ~/ 2);
      }

      // Convert the image to base64 format
      String base64Image = base64Encode(img.encodePng(image));
      storedBase64Image = base64Image;
      print("storedBase64Image:- ${storedBase64Image}");
      return base64Image;
    } else {
      throw Exception('Failed to decode the image');
    }
  }

  String formatDate(DateTime date) {
    String formattedDate = DateFormat('dd MMM yyyy').format(date);
    return formattedDate;
    //"${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  String formatChequeDate(DateTime date) {
    String formattedDate = DateFormat('yyyy-MM-dd').format(date);
    return formattedDate;
    //"${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  Future<void> pickImageFromGallery() async {
    final ImagePicker _picker = ImagePicker();
    final XFile? pickedImage =
        await _picker.pickImage(source: ImageSource.gallery);

    if (pickedImage != null) {
      final File selectedImage = File(pickedImage.path);
      // Add your logic to handle the selected image here.
    }
  }

  void showPermissionDeniedDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('Permission Denied'),
        content: Text('Please grant contact permission in app settings.'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: Text('Open Settings'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void showPermissionDeniedDialogGallery() {
    Get.dialog(
      AlertDialog(
        title: Text('Permission Denied'),
        content: Text('Please grant permission in app settings.'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
              openAppSettings();
            },
            child: Text('Open Settings'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
