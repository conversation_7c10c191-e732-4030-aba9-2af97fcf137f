import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/product_screen/models/product_model.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/orders_model.dart';
import 'package:sfm_new/sfmDatabase/models/price_group_model.dart';
import 'package:sfm_new/sfmDatabase/models/product_brand_model.dart';
import 'package:sfm_new/sfmDatabase/models/product_type_model.dart';

class OrderHistoryDetailController extends GetxController {
  TextEditingController searchController = TextEditingController();
  RxList<ProductBrand> productBrandList = <ProductBrand>[].obs;
  RxList<ProductType> productTypeList = <ProductType>[].obs;
  RxList<PriceGroup> priceGroupList = <PriceGroup>[].obs;

  RxList<AddProduct> productList = <AddProduct>[].obs;
  RxList<OrderData> orderList = <OrderData>[].obs;
  RxList<AddProduct> cartList = <AddProduct>[].obs;

  // final custTypeController = Get.find<SuperStockistController>();

  // selectedCustType

  RxBool isOrderProductive = true.obs;

  var isLoading = true.obs;
  final searchText = ''.obs;

  RxDouble totalPcsAmount = 0.0.obs;
  RxDouble totalBunchAmount = 0.0.obs;
  RxDouble totalBoxAmount = 0.0.obs;

  RxDouble totalAmount = 0.0.obs;

  RxInt pcsQty = 0.obs;
  RxInt bunchQty = 0.obs;
  RxInt boxQty = 0.obs;

  RxInt totalQty = 0.obs;

  RxInt checkinType = 0.obs;

  int selectedCustType = 0;

  RxString selectedCallCode = ''.obs;

  RxDouble grandTotalAmount = 0.0.obs;

  RxDouble packagingChargeValue = 0.0.obs;

  @override
  void onClose() {
    super.onClose();
  }

  @override
  void onInit() async {
    super.onInit();

    selectedCallCode.value =
        (await SharedPrefManager.instance.getString(ConstantValues.callCode)) ??
            "";
    //  SharedPrefManager.instance.setString(ConstantValues.callID, order.callCode);
    //   Get.toNamed(AppRoutes.orderHistoryDetailScreen);

    selectedCustType = await SharedPrefManager.instance
            .getInt(ConstantValues.selectedCustType) ??
        0;
    cartList.clear();
    productList.clear();

    packagingChargeValue.value = await SharedPrefManager.instance
            .getDouble(ConstantValues.packagingCharge) ??
        0.0;

    await fetchOrders();
    await fetchProductType();
    await fetchCheckInType();
  }

  Future<void> fetchCheckInType() async {
    checkinType.value =
        (await SharedPrefManager.instance.getInt(ConstantValues.checkInType))!;
    print("checkinType.value: ${checkinType.value}");
  }

  Future<void> fetchOrders() async {
    grandTotalAmount.value = 0.0;
    isLoading(true); 
    print("fetchOrders called");
    final db = await DatabaseProvider.database;

    final List<Map<String, dynamic>> orderMaps = await db.query(
        "${TableValues.tableOrder}",
        where: "${TableValues.orderCallCode} = ?",
        whereArgs: ["${selectedCallCode.value}"]);

    final List<AddProduct> productListData = [];

    for (final orderMap in orderMaps) {
      final String productCode = orderMap["${TableValues.orderProductCode}"];
      print("order product code: ${productCode}");

      final productMap = await db.query("${TableValues.tableProduct}",
          where: "${TableValues.productCode} = ?", whereArgs: [productCode]);
      print("productMap: ${productMap}");

      if (productMap.isNotEmpty) {
        final productDetails = productMap.first;
        final AddProduct product = AddProduct.fromMap(productDetails);
        print("product data: ${product}");
        grandTotalAmount.value +=
            orderMap["${TableValues.orderProductTotalPriceWithTax}"];
        print("grandTotalAmount: ${grandTotalAmount.value}");

        var productValues = AddProduct(
          orderCode: orderMap["${TableValues.orderCode}"],
          productCode: "${product.productCode}",
          productName: "${product.productName}",
          productType: "${product.productType}",
          productBrand: product.productBrand,
          uom: product.uom,
          unitSize: (product.unitSize ?? 0).toDouble(),
          innerCaseSize: product.innerCaseSize,
          outerCaseSize: product.outerCaseSize,
          productMrp: (product.productMrp ?? 0.0).toDouble(),
          productGst: (product.productGst ?? 0.0).toDouble(),
          productHsnSacCode: "${product.productHsnSacCode}",
          productImage: "${product.productImage}",
          productStatus: product.productStatus,
          barcode: "${product.barcode}",
          marketType: product.marketType,
 
          pmPtId: product.pmPtId,
          quantity: RxInt(orderMap["${TableValues.orderProductQTY}"] ?? 0),
          
          pts: (orderMap["${TableValues.orderProductPTS}"] ?? 0.0).toDouble(),
          rateBasic: (orderMap["${TableValues.orderProductBaseRate}"] ?? 0.0)
              .toDouble(),
          totalBasicRate:
              (orderMap["${TableValues.orderProductTotalBeforeTax}"] ?? 0.0)
                  .toDouble(),
          gst: orderMap["${TableValues.orderProductTax}"],
          gstAmount: (orderMap["${TableValues.orderProductTaxAmount}"] ?? 0.0)
              .toDouble(),
          grandTotal: RxDouble(
              (orderMap["${TableValues.orderProductTotalPriceWithTax}"] ?? 0)
                  .toDouble()),

        );

        print("productValues ${productValues}");

        productListData.add(productValues);
      } else {
        print(
            "Warning: Order references non-existent product (ID: $productCode)");
      }
    }

    print("orderMaps: ${orderMaps}");

    orderList.value =
        orderMaps.map((orderMap) => OrderData.fromMap(orderMap)).toList();

    grandTotalAmount.value += packagingChargeValue.value;

    productList.value = productListData;
    print("orderList: ${orderList}");
    print("productList: ${productList}");
  }

  Future<void> fetchProductType() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableProductType}");
    productTypeList.value = List.generate(maps.length, (index) {
      return ProductType.fromMap(maps[index]);
    });
    isLoading(false);
  }

  String? fetchTypeName(int productTypeId) {
   
    try {
      ProductType? type = productTypeList.firstWhere(
        (type) => type.type_id == productTypeId,
      );
      return type.type_name;
    } catch (e) {
      if (kDebugMode) {
        print('Error in fetchTypeName: $e');
      }
      return null;
    }
  }
}
