import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:sfm_new/core/app_export.dart';

class AddOrder {
  String callCode;
  String empCode;
  int clientId;
  double accuracy;
  int partyId;
  int productOrderType;
  List<AddToCart> products;

  AddOrder({
    required this.callCode,
    required this.empCode,
    required this.clientId,
    required this.accuracy,
    required this.partyId,
    required this.productOrderType,
    required this.products,
  });

  factory AddOrder.fromJson(Map<String, dynamic> json) {
    return AddOrder(
      callCode: json['call_code'],
      empCode: json['emp_code'],
      clientId: json['client_code'],
      accuracy: json['accuracy'],
      partyId: json['party_code'],
      productOrderType: json['product_order_type'],
      products: List<AddToCart>.from(json['products']
          .map((productJson) => AddToCart.fromMap(productJson))),
    );
  }
}

class AddProduct {
  String orderCode;
  String productCode;
  String productName;
  String productType;
  int productBrand;
  int uom;
  double unitSize;
  int innerCaseSize;
  int outerCaseSize;
  double productMrp;
  double productGst;
  String productHsnSacCode;
  String productImage;
  int productStatus;
  String barcode;
  int marketType;
  RxDouble productPtss;
  RxDouble productPtdistributor;
  RxDouble productPtdealer;
  RxDouble productPtretailer;
  int pmPtId;
  RxInt quantity;
  RxInt pcs;
  RxInt box;
  RxInt bunch;
  double mrp;
  double pts;
  double rateBasic;
  double totalBasicRate;
  int gst;
  double gstAmount;
  RxDouble grandTotal;

  AddProduct({
    required this.orderCode,
    required this.productCode,
    required this.productName,
    required this.productType,
    required this.productBrand,
    required this.uom,
    required this.unitSize,
    required this.innerCaseSize,
    required this.outerCaseSize,
    required this.productMrp,
    required this.productGst,
    required this.productHsnSacCode,
    required this.productImage,
    required this.productStatus,
    required this.barcode,
    required this.marketType,
    required this.productPtss,
    required this.productPtdistributor,
    required this.productPtdealer,
    required this.productPtretailer,
    required this.pmPtId,
    required this.quantity,
    required this.pcs,
    required this.box,
    required this.bunch,
    required this.mrp,
    required this.pts,
    required this.rateBasic,
    required this.totalBasicRate,
    required this.gst,
    required this.gstAmount,
    required this.grandTotal,
  });

  // Factory method to create AddProductMaster instance from map data
  factory AddProduct.fromMap(Map<String, dynamic> map) {
    return AddProduct(
      orderCode: map['orderCode'] ?? '',
      productCode: map['product_code'] ?? 0,
      productName: map['product_name'] ?? '',
      productType: map['product_type'] ?? '',
      productBrand: map['product_brand'] ?? 0,
      uom: map['uom'] ?? 0,
      unitSize: (map['unit_size'] ?? 0).toDouble(),
      innerCaseSize: map['inner_case_size'] ?? 0,
      outerCaseSize: map['outer_case_size'] ?? 0,
      productMrp: (map['product_mrp'] ?? 0).toDouble(),
      productGst: (map['product_gst'] ?? 0).toDouble(),
      productHsnSacCode: map['product_hsn_sac_code'] ?? '',
      productImage: map['product_image'] ?? '',
      productStatus: map['product_status'] ?? 0,
      barcode: map['barcode'] ?? '',
      marketType: map['market_type'] ?? 0,
      productPtss: RxDouble((map['product_ptss'] ?? 0).toDouble()),
      productPtdistributor:
          RxDouble((map['product_ptdistributor'] ?? 0).toDouble()),
      productPtdealer: RxDouble((map['product_ptdealer'] ?? 0).toDouble()),
      productPtretailer: RxDouble((map['product_ptretailer'] ?? 0).toDouble()),
      pmPtId: map['pm_pt_id'] ?? 0,
      quantity: RxInt(map['quantity'] ?? 0),
      pcs: RxInt(map['pcs'] ?? 0),
      box: RxInt(map['box'] ?? 0),
      bunch: RxInt(map['bunch'] ?? 0),
      mrp: (map['mrp'] ?? 0).toDouble(),
      pts: (map['pts'] ?? 0).toDouble(),
      rateBasic: (map['rate_basic'] ?? 0).toDouble(),
      totalBasicRate: (map['total_basic_rate'] ?? 0).toDouble(),
      gst: map['gst'] ?? 0,
      gstAmount: (map['gst_amount'] ?? 0).toDouble(),
      grandTotal: RxDouble((map['grand_total'] ?? 0).toDouble()),
    );
  }

  @override
  String toString() {
    return 'AddProduct {'
        'orderCode: $orderCode, '
        'productCode: $productCode, '
        'productName: $productName, '
        'productType: $productType, '
        'productBrand: $productBrand, '
        'uom: $uom, '
        'unitSize: $unitSize, '
        'innerCaseSize: $innerCaseSize, '
        'outerCaseSize: $outerCaseSize, '
        'productMrp: $productMrp, '
        'productGst: $productGst, '
        'productHsnSacCode: $productHsnSacCode, '
        'productImage: $productImage, '
        'productStatus: $productStatus, '
        'barcode: $barcode, '
        'marketType: $marketType, '
        'productPtss: $productPtss, '
        'productPtdistributor: $productPtdistributor, '
        'productPtdealer: $productPtdealer, '
        'productPtretailer: $productPtretailer, '
        'pmPtId: $pmPtId, '
        'quantity: $quantity, '
        'pcs: $pcs, '
        'box: $box, '
        'bunch: $bunch, '
        'mrp: $mrp, '
        'pts: $pts, '
        'rateBasic: $rateBasic, '
        'totalBasicRate: $totalBasicRate, '
        'gst: $gst, '
        'gstAmount: $gstAmount, '
        'grandTotal: $grandTotal}';
  }
}

class AddToCart {
  String orderCode;
  String productCode;
  String productName;
  String productType;
  int productBrand;
  int uom;
  double unitSize;
  int innerCaseSize;
  int outerCaseSize;
  double productMrp;
  double productGst;
  String productHsnSacCode;
  String productImage;
  int productStatus;
  String barcode;
  int marketType;
  RxDouble productPtss;
  RxDouble productPtdistributor;
  RxDouble productPtdealer;
  RxDouble productPtretailer;
  int pmPtId;
  RxInt quantity;
  RxInt pcs;
  RxInt box;
  RxInt bunch;
  double mrp;
  double pts;
  double rateBasic;
  double totalBasicRate;
  int gst;
  double gstAmount;
  RxDouble grandTotal;

  AddToCart({
    required this.orderCode,
    required this.productCode,
    required this.productName,
    required this.productType,
    required this.productBrand,
    required this.uom,
    required this.unitSize,
    required this.innerCaseSize,
    required this.outerCaseSize,
    required this.productMrp,
    required this.productGst,
    required this.productHsnSacCode,
    required this.productImage,
    required this.productStatus,
    required this.barcode,
    required this.marketType,
    required this.productPtss,
    required this.productPtdistributor,
    required this.productPtdealer,
    required this.productPtretailer,
    required this.pmPtId,
    required this.quantity,
    required this.pcs,
    required this.box,
    required this.bunch,
    required this.mrp,
    required this.pts,
    required this.rateBasic,
    required this.totalBasicRate,
    required this.gst,
    required this.gstAmount,
    required this.grandTotal,
  });

  // Factory method to create AddProductMaster instance from map data
  factory AddToCart.fromMap(Map<String, dynamic> map) {
    return AddToCart(
      orderCode: map['orderCode'] ?? '',
      productCode: map['product_code'] ?? 0,
      productName: map['product_name'] ?? '',
      productType: map['product_type'] ?? '',
      productBrand: map['product_brand'] ?? 0,
      uom: map['uom'] ?? 0,
      unitSize: (map['unit_size'] ?? 0).toDouble(),
      innerCaseSize: map['inner_case_size'] ?? 0,
      outerCaseSize: map['outer_case_size'] ?? 0,
      productMrp: (map['product_mrp'] ?? 0).toDouble(),
      productGst: (map['product_gst'] ?? 0).toDouble(),
      productHsnSacCode: map['product_hsn_sac_code'] ?? '',
      productImage: map['product_image'] ?? '',
      productStatus: map['product_status'] ?? 0,
      barcode: map['barcode'] ?? '',
      marketType: map['market_type'] ?? 0,
      productPtss: RxDouble((map['product_ptss'] ?? 0).toDouble()),
      productPtdistributor:
          RxDouble((map['product_ptdistributor'] ?? 0).toDouble()),
      productPtdealer: RxDouble((map['product_ptdealer'] ?? 0).toDouble()),
      productPtretailer: RxDouble((map['product_ptretailer'] ?? 0).toDouble()),
      pmPtId: map['pm_pt_id'] ?? 0,
      quantity: RxInt(map['quantity'] ?? 0),
      pcs: RxInt(map['pcs'] ?? 0),
      box: RxInt(map['box'] ?? 0),
      bunch: RxInt(map['bunch'] ?? 0),
      mrp: (map['mrp'] ?? 0).toDouble(),
      pts: (map['pts'] ?? 0).toDouble(),
      rateBasic: (map['rate_basic'] ?? 0).toDouble(),
      totalBasicRate: (map['total_basic_rate'] ?? 0).toDouble(),
      gst: map['gst'] ?? 0,
      gstAmount: (map['gst_amount'] ?? 0).toDouble(),
      grandTotal: RxDouble((map['grand_total'] ?? 0).toDouble()),
    );
  }

  @override
  String toString() {
    return 'AddProduct {'
        'orderCode: $orderCode, '
        'productCode: $productCode, '
        'productName: $productName, '
        'productType: $productType, '
        'productBrand: $productBrand, '
        'uom: $uom, '
        'unitSize: $unitSize, '
        'innerCaseSize: $innerCaseSize, '
        'outerCaseSize: $outerCaseSize, '
        'productMrp: $productMrp, '
        'productGst: $productGst, '
        'productHsnSacCode: $productHsnSacCode, '
        'productImage: $productImage, '
        'productStatus: $productStatus, '
        'barcode: $barcode, '
        'marketType: $marketType, '
        'productPtss: $productPtss, '
        'productPtdistributor: $productPtdistributor, '
        'productPtdealer: $productPtdealer, '
        'productPtretailer: $productPtretailer, '
        'pmPtId: $pmPtId, '
        'quantity: $quantity, '
        'pcs: $pcs, '
        'box: $box, '
        'bunch: $bunch, '
        'mrp: $mrp, '
        'pts: $pts, '
        'rateBasic: $rateBasic, '
        'totalBasicRate: $totalBasicRate, '
        'gst: $gst, '
        'gstAmount: $gstAmount, '
        'grandTotal: $grandTotal}';
  }
}
