// ignore_for_file: , unnecessary_null_comparison, unused_local_variable

import 'package:cached_network_image/cached_network_image.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/presentation/product_screen/models/product_model.dart';

import 'controller/order_history_detail_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';

import 'package:dynamic_tabbar/dynamic_tabbar.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';

// ignore_for_file: must_be_immutable
class OrderHistoryDetailScreen extends GetWidget<OrderHistoryDetailController> {
  bool isScrollable = false;
  bool showNextIcon = true;
  bool showBackIcon = true;

  Widget? leading;
  Widget? trailing;
  List<TabData> tabs = [];

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: Column(
          children: [
            Expanded(
              child: _addOrderView(context),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "Order",
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _addOrderView(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 10.v),
        _buildProductList(),
        SizedBox(height: 10.v),
        _buildVIEWCART(),
      ],
    );
  }

  Widget _buildVIEWCART() {
    return Obx(
      () => Container(
        margin: EdgeInsets.only(
          left: 0.h,
          right: 0.h,
          bottom: 0.v,
        ),
        decoration: AppDecoration.outlineBlack9007.copyWith(
          borderRadius: BorderRadiusStyle.customBorderTL10,
        ),
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                height: 50.v,
                width: double.maxFinite,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(10.h),
                  ),
                ),
              ),
            ),
            Align(
              alignment: Alignment.center,
              child: Container(
                width: double.maxFinite,
                // margin: EdgeInsets.only(top: 10.v),
                padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                //padding: EdgeInsets.only(top: 10, bottom: 0, left: 16),
                decoration: AppDecoration.outlineBlack9007,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Grand Total",
                          style: CustomTextStyles
                              .labelLargeOpenSansOnErrorContainer,
                        ),
                        SizedBox(height: 1.v),
                        Text(
                          "₹ ${controller.grandTotalAmount.value.toStringAsFixed(2)}",
                          style:
                              CustomTextStyles.titleSmallOnErrorContainerBold,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductList() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.h),
        child: Obx(
          () {
            if (controller.isLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            } else if (controller.productList.isEmpty) {
              return Center(
                child: Text(
                  'No products found',
                  style: TextStyle(fontSize: 16),
                ),
              );
            } else {
              return ListView.builder(
                physics: ClampingScrollPhysics(),
                itemCount: controller.productList.length,
                itemBuilder: (context, index) => CustomProductListItem(
                  product: controller.productList[index],
                ),
              );
            }
          },
        ),
      ),
    );
  }
}

class CustomProductListItem extends StatelessWidget {
  final AddProduct product;
  final OrderHistoryDetailController controller =
      Get.put(OrderHistoryDetailController());

  CustomProductListItem({
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8, bottom: 0),
      child: Container(
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder20,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 4.h),
              child: Row(
                children: [
                  Container(
                    // height: 120.v,
                    width: 107.h,
                    margin: EdgeInsets.only(top: 0.v, left: 6),
                    child: Stack(
                      alignment: Alignment.topCenter,
                      children: [
                        CachedNetworkImage(
                          imageUrl: product.productImage != null &&
                                  product.productImage!.isNotEmpty
                              ? "${ApiClient.imgProductBaseURL}${product.productImage}"
                              : "",
                          height: 100.v,
                          width: 100.h,
                          alignment: Alignment.center,
                          placeholder: (context, url) =>
                              Image.asset(ImageConstant.imgSplash),
                          errorWidget: (context, url, error) =>
                              Image.asset(ImageConstant.imgSplash),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 5.h, top: 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${product.productName}",
                          style: CustomTextStyles.titleSmallBluegray90015_1,
                        ),
                        SizedBox(height: 2.v),
                        Text(
                          "${controller.fetchTypeName(product.pmPtId!)}",
                          style: CustomTextStyles.titleSmallBluegray90015_1,
                        ),
                        SizedBox(height: 3.v),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            SizedBox(width: 0),
                            Row(
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Price",
                                      style: CustomTextStyles
                                          .bodyMediumGray6000214_1,
                                    ),
                                  ],
                                ),
                                SizedBox(width: 8),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      ":",
                                      style: CustomTextStyles
                                          .bodyMediumGray6000214_1,
                                    ),
                                  ],
                                ),
                                SizedBox(width: 8),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      product.outerCaseSize == null
                                          ? '0'
                                          : '${product.pts}',
                                      style: CustomTextStyles
                                          .bodyMediumGray6000214_1,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 2.v),
            Container(
              padding: EdgeInsets.symmetric(vertical: 6.v),
              decoration: AppDecoration.fillPrimaryContainer.copyWith(
                borderRadius: BorderRadiusStyle.roundedBorder20,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(bottom: 0.v),
                    child: Column(
                      children: [
                        Container(
                          width: MediaQuery.of(context).size.width * 0.8,
                          margin: EdgeInsets.only(
                            left: 8.h,
                            right: 8.h,
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    "Qty : ",
                                    style: CustomTextStyles
                                        .labelLargeOpenSansBluegray900Bold,
                                  ),
                                  SizedBox(height: 2.v),
                                  Text(
                                    "${product.quantity}",
                                    style: CustomTextStyles
                                        .bodyMediumGray6000214_1,
                                  ),
                                ],
                              ),
                              SizedBox(width: 8),
                              Row(
                                children: [
                                  Text(
                                    "Total Price : ",
                                    style: CustomTextStyles
                                        .labelLargeOpenSansBluegray900Bold,
                                  ),
                                  SizedBox(height: 2.v),
                                  Text(
                                    "${product.grandTotal!.value.toStringAsFixed(2)}",
                                    style: CustomTextStyles
                                        .bodyMediumGray6000214_1,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 2.v),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
