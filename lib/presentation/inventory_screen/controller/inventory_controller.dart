import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/presentation/inventory_screen/models/inventory_model.dart';
import 'package:flutter/material.dart';


class InventoryController extends GetxController {
  TextEditingController searchController = TextEditingController();

  Rx<InventoryModel> inventoryModelObj = InventoryModel().obs;

  @override
  void onClose() {
    super.onClose();
    searchController.dispose();
  }
}
