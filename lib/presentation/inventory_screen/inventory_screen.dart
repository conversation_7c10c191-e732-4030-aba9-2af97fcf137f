import '../inventory_screen/widgets/inventory_item_widget.dart';
import 'controller/inventory_controller.dart';
import 'models/inventory_item_model.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_eight.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_nine.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_seven.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';
import 'package:sfm_new/widgets/custom_search_view.dart';

// ignore_for_file: must_be_immutable
class InventoryScreen extends GetWidget<InventoryController> {
  const InventoryScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: SizedBox(
          width: SizeUtils.width,
          child: SingleChildScrollView(
            child: Container(
              margin: EdgeInsets.only(right: 1.h),
              padding: EdgeInsets.symmetric(
                horizontal: 15.h,
                vertical: 10.v,
              ),
              decoration: AppDecoration.outlineBlack9004.copyWith(
                borderRadius: BorderRadiusStyle.customBorderTL25,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CustomSearchView(
                    controller: controller.searchController,
                    borderDecoration: SearchViewStyleHelper.outlineBlack,
                    filled: true,
                    fillColor:
                        theme.colorScheme.onErrorContainer.withValues(alpha: 1),
                  ),
                  SizedBox(height: 10.v),
                  _buildInventory(),
                  SizedBox(height: 243.v),
                  CustomElevatedButton(
                    text: "lbl_submit".tr,
                    margin: EdgeInsets.only(
                      left: 10.h,
                      right: 7.h,
                    ),
                  ),
                  SizedBox(height: 28.v),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 56.v,
      title: Row(
        children: [
          AppbarSubtitleNine(
            text: "lbl_add_order".tr,
            margin: EdgeInsets.only(bottom: 2.v),
          ),
          AppbarSubtitleNine(
            text: "lbl_order_history".tr,
            margin: EdgeInsets.only(
              left: 10.h,
              top: 1.v,
            ),
          ),
          AppbarSubtitleNine(
            text: "lbl_payment".tr,
            margin: EdgeInsets.only(
              left: 13.h,
              top: 1.v,
            ),
          ),
        ],
      ),
      actions: [
        Padding(
          padding: EdgeInsets.fromLTRB(13.h, 13.v, 20.h, 12.v),
          child: Column(
            children: [
              Row(
                children: [
                  AppbarSubtitleEight(
                    text: "lbl_shop_details".tr,
                  ),
                  AppbarSubtitleSeven(
                    text: "lbl_inventory".tr,
                    margin: EdgeInsets.only(left: 12.h),
                  ),
                ],
              ),
              SizedBox(height: 6.v),
              Align(
                alignment: Alignment.centerRight,
                child: Container(
                  height: 3.v,
                  width: 30.h,
                  margin: EdgeInsets.only(
                    left: 104.h,
                    right: 14.h,
                  ),
                  decoration: BoxDecoration(
                    color: appTheme.lightBlue900,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInventory() {
    return Obx(
      () => ListView.separated(
        physics: NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        separatorBuilder: (
          context,
          index,
        ) {
          return SizedBox(
            height: 1.v,
          );
        },
        itemCount:
            controller.inventoryModelObj.value.inventoryItemList.value.length,
        itemBuilder: (context, index) {
          InventoryItemModel model =
              controller.inventoryModelObj.value.inventoryItemList.value[index];
          return InventoryItemWidget(
            model,
          );
        },
      ),
    );
  }
}
