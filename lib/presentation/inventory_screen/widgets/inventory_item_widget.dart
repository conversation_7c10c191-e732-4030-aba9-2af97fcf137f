import '../controller/inventory_controller.dart';
import '../models/inventory_item_model.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';

// ignore: must_be_immutable
class InventoryItemWidget extends StatelessWidget {
  InventoryItemWidget(
    this.inventoryItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  InventoryItemModel inventoryItemModelObj;

  var controller = Get.find<InventoryController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 173.v,
      width: 343.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
          15.h,
        ),
        border: Border.all(
          color: appTheme.gray300,
          width: 1.h,
        ),
      ),
    );
  }
}
