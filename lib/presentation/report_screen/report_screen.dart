import 'controller/report_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg_provider/flutter_svg_provider.dart' as fs;
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_one.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

// ignore_for_file: must_be_immutable
class ReportScreen extends GetWidget<ReportController> {
  const ReportScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(),
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(
            horizontal: 2.h,
            vertical: 1.v,
          ),
          child: Column(
            children: [
              _buildSalesReport(),
              <PERSON><PERSON><PERSON><PERSON>(height: 10.v),
              _buildFortyThousand(),
              <PERSON><PERSON><PERSON><PERSON>(height: 23.v),
              <PERSON><PERSON>(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: EdgeInsets.only(left: 7.h),
                  child: Text(
                    "lbl_export".tr,
                    style: CustomTextStyles.titleSmallPoppinsBluegray800Bold,
                  ),
                ),
              ),
              SizedBox(height: 7.v),
              _buildPdfOne(),
              SizedBox(height: 25.v),
              _buildPrimaryTarget(),
              _buildEightyOne(
                index: "lbl_index".tr,
                total: "lbl_total".tr,
                achieve: "lbl_achieve".tr,
                remaining: "lbl_remaining".tr,
              ),
              _buildAmount1(),
              SizedBox(height: 1.v),
              _buildFiveHundred2(),
              SizedBox(height: 1.v),
              _buildLiter(
                liter: "lbl_liter".tr,
                ninetyOne: "lbl_200".tr,
                ninety: "lbl_500".tr,
                eightyTwo: "lbl_500".tr,
              ),
              SizedBox(height: 6.v),
              _buildSecondaryTarget(),
              Padding(
                padding: EdgeInsets.only(right: 3.h),
                child: _buildEightyOne(
                  index: "lbl_index".tr,
                  total: "lbl_total".tr,
                  achieve: "lbl_achieve".tr,
                  remaining: "lbl_remaining".tr,
                ),
              ),
              _buildAmount3(),
              SizedBox(height: 1.v),
              _buildFiveHundred5(),
              SizedBox(height: 1.v),
              _buildLiter(
                liter: "lbl_liter".tr,
                ninetyOne: "lbl_200".tr,
                ninety: "lbl_500".tr,
                eightyTwo: "lbl_500".tr,
              ),
              SizedBox(height: 5.v),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 53.v,
      leadingWidth: 37.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 22.v,
        ),
      ),
      centerTitle: true,
      title: AppbarSubtitleOne(
        text: "lbl_report".tr,
      ),
    );
  }

  Widget _buildSalesReport() {
    return Padding(
      padding: EdgeInsets.only(
        left: 6.h,
        right: 9.h,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            "lbl_sales_report".tr,
            style: CustomTextStyles.titleSmallPoppinsBluegray800,
          ),
          Spacer(),
          Container(
            height: 12.adaptSize,
            width: 12.adaptSize,
            margin: EdgeInsets.only(
              top: 7.v,
              bottom: 4.v,
            ),
            decoration: BoxDecoration(
              color: appTheme.gray5001,
              borderRadius: BorderRadius.circular(
                6.h,
              ),
              border: Border.all(
                color: theme.colorScheme.primary,
                width: 3.h,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              left: 8.h,
              top: 5.v,
            ),
            child: Text(
              "lbl_primary".tr,
              style: CustomTextStyles.bodySmallGray50002,
            ),
          ),
          Container(
            width: 85.h,
            margin: EdgeInsets.only(
              left: 11.h,
              top: 5.v,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 12.adaptSize,
                  width: 12.adaptSize,
                  margin: EdgeInsets.only(
                    top: 1.v,
                    bottom: 4.v,
                  ),
                  decoration: BoxDecoration(
                    color: appTheme.gray5001,
                    borderRadius: BorderRadius.circular(
                      6.h,
                    ),
                    border: Border.all(
                      color: appTheme.blue100,
                      width: 3.h,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 8.h),
                  child: Text(
                    "lbl_secondary".tr,
                    style: CustomTextStyles.bodySmallGray50002,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFortyThousand() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 5.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(bottom: 15.v),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    "lbl_40_000".tr,
                    style: CustomTextStyles.bodySmallGray90002,
                  ),
                ),
                SizedBox(height: 33.v),
                Text(
                  "lbl_36_000".tr,
                  style: CustomTextStyles.bodySmallGray90002,
                ),
                SizedBox(height: 37.v),
                _buildTwentyFourThousand(
                  twentyFourThousand: "lbl_28_000".tr,
                  twentyFourThousand1: "lbl_28_000".tr,
                ),
                SizedBox(height: 35.v),
                _buildTwentyFourThousand(
                  twentyFourThousand: "lbl_24_000".tr,
                  twentyFourThousand1: "lbl_24_000".tr,
                ),
                SizedBox(height: 34.v),
                _buildTwentyFourThousand(
                  twentyFourThousand: "lbl_20_000".tr,
                  twentyFourThousand1: "lbl_20_000".tr,
                ),
                SizedBox(height: 33.v),
                Align(
                  alignment: Alignment.center,
                  child: SizedBox(
                    height: 15.v,
                    width: 30.h,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Align(
                          alignment: Alignment.center,
                          child: Text(
                            "lbl_16_000".tr,
                            style: CustomTextStyles.bodySmallGray90002,
                          ),
                        ),
                        Align(
                          alignment: Alignment.center,
                          child: Text(
                            "lbl_16_000".tr,
                            style: CustomTextStyles.bodySmallGray90002,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                left: 1.h,
                top: 4.v,
              ),
              child: Column(
                children: [
                  Container(
                    height: 256.v,
                    width: 323.h,
                    padding: EdgeInsets.symmetric(horizontal: 6.h),
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: fs.Svg(
                          ImageConstant.imgGroup732,
                        ),
                        fit: BoxFit.cover,
                      ),
                    ),
                    child: Stack(
                      alignment: Alignment.topLeft,
                      children: [
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Container(
                            width: 309.h,
                            margin: EdgeInsets.only(
                              left: 1.h,
                              top: 65.v,
                            ),
                            padding: EdgeInsets.symmetric(vertical: 36.v),
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image: fs.Svg(
                                  ImageConstant.imgGroup733,
                                ),
                                fit: BoxFit.cover,
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  margin: EdgeInsets.only(bottom: 108.v),
                                  decoration: AppDecoration.outlineBlack9001,
                                  child: Text(
                                    "lbl_22_752".tr,
                                    style: CustomTextStyles.poppinsBlack900,
                                  ),
                                ),
                                Spacer(),
                                Container(
                                  margin: EdgeInsets.only(bottom: 106.v),
                                  decoration: AppDecoration.outlineBlack9001,
                                  child: Text(
                                    "lbl_22_752".tr,
                                    style: CustomTextStyles.poppinsBlack900,
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                    left: 6.h,
                                    top: 34.v,
                                    bottom: 74.v,
                                  ),
                                  decoration: AppDecoration.outlineBlack9001,
                                  child: Text(
                                    "lbl_22_752".tr,
                                    style: CustomTextStyles.poppinsBlack900,
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                    left: 15.h,
                                    top: 68.v,
                                    bottom: 40.v,
                                  ),
                                  decoration: AppDecoration.outlineBlack9001,
                                  child: Text(
                                    "lbl_22_752".tr,
                                    style: CustomTextStyles.poppinsBlack900,
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                    left: 7.h,
                                    top: 53.v,
                                    bottom: 55.v,
                                  ),
                                  decoration: AppDecoration.outlineBlack9001,
                                  child: Text(
                                    "lbl_22_752".tr,
                                    style: CustomTextStyles.poppinsBlack900,
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                    left: 14.h,
                                    top: 41.v,
                                    bottom: 67.v,
                                  ),
                                  decoration: AppDecoration.outlineBlack9001,
                                  child: Text(
                                    "lbl_22_752".tr,
                                    style: CustomTextStyles.poppinsBlack900,
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                    left: 5.h,
                                    top: 20.v,
                                    bottom: 88.v,
                                  ),
                                  decoration: AppDecoration.outlineBlack9001,
                                  child: Text(
                                    "lbl_22_752".tr,
                                    style: CustomTextStyles.poppinsBlack900,
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                    left: 14.h,
                                    top: 92.v,
                                    bottom: 16.v,
                                  ),
                                  decoration: AppDecoration.outlineBlack9001,
                                  child: Text(
                                    "lbl_22_752".tr,
                                    style: CustomTextStyles.poppinsBlack900,
                                  ),
                                ),
                                Container(
                                  margin: EdgeInsets.only(
                                    left: 8.h,
                                    top: 68.v,
                                    bottom: 39.v,
                                  ),
                                  decoration: AppDecoration.outlineBlack9001,
                                  child: Text(
                                    "lbl_22_752".tr,
                                    style: CustomTextStyles.poppinsBlack900,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.topLeft,
                          child: Container(
                            margin: EdgeInsets.only(
                              left: 29.h,
                              top: 55.v,
                            ),
                            decoration: AppDecoration.outlineBlack9001,
                            child: Text(
                              "lbl_22_752".tr,
                              style: CustomTextStyles.poppinsBlack900,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 3.v),
                  SizedBox(
                    height: 15.v,
                    width: 313.h,
                    child: Stack(
                      alignment: Alignment.centerLeft,
                      children: [
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "lbl_2021_04_01".tr,
                            style: theme.textTheme.labelMedium,
                          ),
                        ),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Padding(
                            padding: EdgeInsets.only(left: 63.h),
                            child: Text(
                              "lbl_2021_04_01".tr,
                              style: theme.textTheme.labelMedium,
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.center,
                          child: Text(
                            "lbl_2021_04_01".tr,
                            style: theme.textTheme.labelMedium,
                          ),
                        ),
                        Align(
                          alignment: Alignment.centerRight,
                          child: Padding(
                            padding: EdgeInsets.only(right: 65.h),
                            child: Text(
                              "lbl_2021_04_01".tr,
                              style: theme.textTheme.labelMedium,
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Padding(
                            padding: EdgeInsets.only(left: 33.h),
                            child: Text(
                              "lbl_2021_04_01".tr,
                              style: theme.textTheme.labelMedium,
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Padding(
                            padding: EdgeInsets.only(left: 96.h),
                            child: Text(
                              "lbl_2021_04_01".tr,
                              style: theme.textTheme.labelMedium,
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.centerRight,
                          child: Padding(
                            padding: EdgeInsets.only(right: 94.h),
                            child: Text(
                              "lbl_2021_04_01".tr,
                              style: theme.textTheme.labelMedium,
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.centerRight,
                          child: Padding(
                            padding: EdgeInsets.only(right: 32.h),
                            child: Text(
                              "lbl_2021_04_01".tr,
                              style: theme.textTheme.labelMedium,
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.centerRight,
                          child: Text(
                            "lbl_2021_04_01".tr,
                            style: theme.textTheme.labelMedium,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPdfOne() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 9.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 131.h,
            padding: EdgeInsets.symmetric(
              horizontal: 5.h,
              vertical: 8.v,
            ),
            decoration: AppDecoration.outlineGray.copyWith(
              borderRadius: BorderRadiusStyle.roundedBorder6,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomImageView(
                  svgPath: ImageConstant.imgPdf1,
                  height: 30.adaptSize,
                  width: 30.adaptSize,
                  margin: EdgeInsets.only(bottom: 2.v),
                ),
                Container(
                  width: 75.h,
                  margin: EdgeInsets.only(left: 11.h),
                  child: Text(
                    "msg_sku_sales_current".tr,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: CustomTextStyles.labelMediumBluegray800,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 8.h),
            child: _buildUser(
              user: ImageConstant.imgSettings,
              dSR: "lbl_dbr".tr,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 7.h),
            child: _buildUser(
              user: ImageConstant.imgUser,
              dSR: "lbl_dsr".tr,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrimaryTarget() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 134.h),
      decoration: AppDecoration.fillSecondaryContainer,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 3.v),
          Text(
            "lbl_primary_target".tr,
            style: CustomTextStyles.bodyMediumPoppinsBlack900,
          ),
        ],
      ),
    );
  }

  Widget _buildAmount() {
    return CustomElevatedButton(
      height: 25.v,
      width: 90.h,
      text: "lbl_amount".tr,
      buttonStyle: CustomButtonStyles.fillBlue,
      buttonTextStyle: theme.textTheme.bodySmall!,
    );
  }

  Widget _buildAmount1() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildAmount(),
        Container(
          width: 90.h,
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(
            horizontal: 28.h,
            vertical: 2.v,
          ),
          decoration: AppDecoration.fillBlue,
          child: Text(
            "lbl_10000".tr,
            style: theme.textTheme.bodySmall,
          ),
        ),
        Container(
          width: 90.h,
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(
            horizontal: 25.h,
            vertical: 1.v,
          ),
          decoration: AppDecoration.fillBlue,
          child: Text(
            "lbl_10000".tr,
            style: theme.textTheme.bodySmall,
          ),
        ),
        Container(
          width: 90.h,
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(
            horizontal: 30.h,
            vertical: 2.v,
          ),
          decoration: AppDecoration.fillBlue,
          child: Text(
            "lbl_500".tr,
            style: theme.textTheme.bodySmall,
          ),
        ),
      ],
    );
  }

  Widget _buildFiveHundred() {
    return CustomElevatedButton(
      height: 25.v,
      width: 90.h,
      text: "lbl_500".tr,
      margin: EdgeInsets.only(left: 2.h),
      buttonStyle: CustomButtonStyles.fillSecondaryContainer,
      buttonTextStyle: theme.textTheme.bodySmall!,
    );
  }

  Widget _buildFiveHundred1() {
    return CustomElevatedButton(
      height: 25.v,
      width: 90.h,
      text: "lbl_500".tr,
      margin: EdgeInsets.only(left: 2.h),
      buttonStyle: CustomButtonStyles.fillSecondaryContainer,
      buttonTextStyle: theme.textTheme.bodySmall!,
    );
  }

  Widget _buildFiveHundred2() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 90.h,
          padding: EdgeInsets.symmetric(
            horizontal: 17.h,
            vertical: 1.v,
          ),
          decoration: AppDecoration.fillSecondaryContainer,
          child: Text(
            "lbl_kilogram".tr,
            style: theme.textTheme.bodySmall,
          ),
        ),
        Container(
          width: 90.h,
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(
            horizontal: 29.h,
            vertical: 1.v,
          ),
          decoration: AppDecoration.fillSecondaryContainer,
          child: Text(
            "lbl_500".tr,
            style: theme.textTheme.bodySmall,
          ),
        ),
        _buildFiveHundred(),
        _buildFiveHundred1(),
      ],
    );
  }

  Widget _buildSecondaryTarget() {
    return Container(
      width: 368.h,
      margin: EdgeInsets.only(right: 3.h),
      padding: EdgeInsets.symmetric(
        horizontal: 120.h,
        vertical: 1.v,
      ),
      decoration: AppDecoration.fillSecondaryContainer,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 1.v),
          Text(
            "msg_secondary_target".tr,
            style: CustomTextStyles.bodyMediumPoppinsBlack900,
          ),
        ],
      ),
    );
  }

  Widget _buildAmount2() {
    return CustomElevatedButton(
      height: 25.v,
      width: 90.h,
      text: "lbl_amount".tr,
      buttonStyle: CustomButtonStyles.fillBlue,
      buttonTextStyle: theme.textTheme.bodySmall!,
    );
  }

  Widget _buildAmount3() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildAmount2(),
        Container(
          width: 90.h,
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(
            horizontal: 28.h,
            vertical: 2.v,
          ),
          decoration: AppDecoration.fillBlue,
          child: Text(
            "lbl_10000".tr,
            style: theme.textTheme.bodySmall,
          ),
        ),
        Container(
          width: 90.h,
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(
            horizontal: 25.h,
            vertical: 1.v,
          ),
          decoration: AppDecoration.fillBlue,
          child: Text(
            "lbl_10000".tr,
            style: theme.textTheme.bodySmall,
          ),
        ),
        Container(
          width: 90.h,
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(
            horizontal: 30.h,
            vertical: 2.v,
          ),
          decoration: AppDecoration.fillBlue,
          child: Text(
            "lbl_500".tr,
            style: theme.textTheme.bodySmall,
          ),
        ),
      ],
    );
  }

  Widget _buildFiveHundred3() {
    return CustomElevatedButton(
      height: 25.v,
      width: 90.h,
      text: "lbl_500".tr,
      margin: EdgeInsets.only(left: 2.h),
      buttonStyle: CustomButtonStyles.fillSecondaryContainer,
      buttonTextStyle: theme.textTheme.bodySmall!,
    );
  }

  Widget _buildFiveHundred4() {
    return CustomElevatedButton(
      height: 25.v,
      width: 90.h,
      text: "lbl_500".tr,
      margin: EdgeInsets.only(left: 2.h),
      buttonStyle: CustomButtonStyles.fillSecondaryContainer,
      buttonTextStyle: theme.textTheme.bodySmall!,
    );
  }

  Widget _buildFiveHundred5() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 90.h,
          padding: EdgeInsets.symmetric(
            horizontal: 17.h,
            vertical: 1.v,
          ),
          decoration: AppDecoration.fillSecondaryContainer,
          child: Text(
            "lbl_kilogram".tr,
            style: theme.textTheme.bodySmall,
          ),
        ),
        Container(
          width: 90.h,
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(
            horizontal: 29.h,
            vertical: 1.v,
          ),
          decoration: AppDecoration.fillSecondaryContainer,
          child: Text(
            "lbl_500".tr,
            style: theme.textTheme.bodySmall,
          ),
        ),
        _buildFiveHundred3(),
        _buildFiveHundred4(),
      ],
    );
  }

  Widget _buildTwentyFourThousand({
    required String twentyFourThousand,
    required String twentyFourThousand1,
  }) {
    return SizedBox(
      height: 15.v,
      width: 32.h,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Align(
            alignment: Alignment.center,
            child: Text(
              twentyFourThousand,
              style: CustomTextStyles.bodySmallGray90002.copyWith(
                color: appTheme.gray90002,
              ),
            ),
          ),
          Align(
            alignment: Alignment.center,
            child: Text(
              twentyFourThousand1,
              style: CustomTextStyles.bodySmallGray90002.copyWith(
                color: appTheme.gray90002,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUser({
    required String user,
    required String dSR,
  }) {
    return Container(
      width: 103.h,
      padding: EdgeInsets.symmetric(vertical: 9.v),
      decoration: AppDecoration.outlineGray.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder6,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          CustomImageView(
            imagePath: user,
            height: 30.adaptSize,
            width: 30.adaptSize,
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 3.v),
            child: Text(
              dSR,
              style: CustomTextStyles.titleSmallPoppinsBluegray800Bold.copyWith(
                color: appTheme.blueGray800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEightyOne({
    required String index,
    required String total,
    required String achieve,
    required String remaining,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 10.h,
        vertical: 1.v,
      ),
      decoration: AppDecoration.fillOnErrorContainer1,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Padding(
            padding: EdgeInsets.only(
              left: 14.h,
              top: 2.v,
            ),
            child: Text(
              index,
              style: theme.textTheme.labelLarge!.copyWith(
                color: appTheme.gray60002,
              ),
            ),
          ),
          Spacer(
            flex: 39,
          ),
          Padding(
            padding: EdgeInsets.only(top: 2.v),
            child: Text(
              total,
              style: theme.textTheme.labelLarge!.copyWith(
                color: appTheme.gray60002,
              ),
            ),
          ),
          Spacer(
            flex: 38,
          ),
          Padding(
            padding: EdgeInsets.only(top: 2.v),
            child: Text(
              achieve,
              style: theme.textTheme.labelLarge!.copyWith(
                color: appTheme.gray60002,
              ),
            ),
          ),
          Spacer(
            flex: 22,
          ),
          Padding(
            padding: EdgeInsets.only(top: 3.v),
            child: Text(
              remaining,
              style: theme.textTheme.labelLarge!.copyWith(
                color: appTheme.gray60002,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLiter({
    required String liter,
    required String ninetyOne,
    required String ninety,
    required String eightyTwo,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 90.h,
          padding: EdgeInsets.symmetric(
            horizontal: 30.h,
            vertical: 1.v,
          ),
          decoration: AppDecoration.fillBlue,
          child: Text(
            liter,
            style: theme.textTheme.bodySmall!.copyWith(
              color: appTheme.black900,
            ),
          ),
        ),
        Container(
          width: 90.h,
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(
            horizontal: 28.h,
            vertical: 1.v,
          ),
          decoration: AppDecoration.fillBlue,
          child: Text(
            ninetyOne,
            style: theme.textTheme.bodySmall!.copyWith(
              color: appTheme.black900,
            ),
          ),
        ),
        Container(
          width: 90.h,
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(
            horizontal: 30.h,
            vertical: 2.v,
          ),
          decoration: AppDecoration.fillBlue,
          child: Text(
            ninety,
            style: theme.textTheme.bodySmall!.copyWith(
              color: appTheme.black900,
            ),
          ),
        ),
        Container(
          width: 90.h,
          margin: EdgeInsets.only(left: 2.h),
          padding: EdgeInsets.symmetric(
            horizontal: 30.h,
            vertical: 1.v,
          ),
          decoration: AppDecoration.fillBlue,
          child: Text(
            eightyTwo,
            style: theme.textTheme.bodySmall!.copyWith(
              color: appTheme.black900,
            ),
          ),
        ),
      ],
    );
  }
}
