import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/presentation/expense_screen/controller/expense_controller.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:sfm_new/sfmDatabase/models/expense_model.dart';
import 'package:sfm_new/sfmDatabase/models/expense_type_model.dart';

class ViewExpenseDetailsController extends GetxController {

  String ccCode = "";

  RxList<Expense> expenseList = <Expense>[].obs;

  RxList<ExpenseType> expenseTypeList = <ExpenseType>[].obs;
  RxList<Customer> customerList = <Customer>[].obs;

  @override
  void onInit() async {
    super.onInit();

    EasyLoading.show(status: 'Loading...');
    await fetchExpense();
    await fetchExpenseType();
    await fetchCustomers();
  }

  Future<void> fetchExpense() async {
    print("fetchClaimComplain called");
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps = await db.query(
        "${TableValues.tableExpense}",
        where: "${TableValues.expenseCode} = ?",
        whereArgs: ["${ExpenseController.instance.selectedExpenseCode.value}"]);
    expenseList.value = List.generate(maps.length, (index) {
      return Expense.fromMap(maps[index]);
    });
    await EasyLoading.dismiss();
  }

  Future<void> fetchExpenseType() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableExpenseType}");
    print(maps);
    expenseTypeList.value = List.generate(maps.length, (index) {
      return ExpenseType.fromMap(maps[index]);
    });
  }

  Future<void> fetchCustomers() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCustomer}");
    print(maps);
    customerList.value = List.generate(maps.length, (index) {
      return Customer.fromMap(maps[index]);
    });
  }

  String? getExpenseNameFromTypeID(int selectedType) {
    try {
      final customerType = expenseTypeList.firstWhere(
        (ccType) => ccType.id == selectedType,
      );
      return customerType.expenseName ?? "";
    } catch (e) {
      if (kDebugMode) {
        print('Error in getExpenseNameFromTypeID: $e');
      }
      return null;
    }
  }

// 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer

  String getClientType(int clientType) {
    String clientTypeText;

    if (clientType == 1) {
      clientTypeText = "Company";
    } else if (clientType == 2) {
      clientTypeText = "SS";
    } else if (clientType == 3) {
      clientTypeText = "Distributor";
    } else if (clientType == 4) {
      clientTypeText = "Dealer";
    } else {
      clientTypeText = "Retailer";
    }
    return clientTypeText;
  }

  String formatDate(String dateString) {
    // Parse the input date string
    DateTime dateTime = DateTime.parse(dateString);

    // Define the date format you want
    DateFormat formatter = DateFormat('dd MMM yyyy');

    // Format the date using the defined format
    String formattedDate = formatter.format(dateTime);

    return formattedDate;
  }
}
