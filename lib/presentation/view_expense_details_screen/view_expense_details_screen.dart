// ignore_for_file: 

import 'package:sfm_new/core/utils/webview_image_load.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/presentation/view_expense_details_screen/controller/view_expense_details_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

// ignore_for_file: must_be_immutable
class ViewExpenseDetailsScreen extends GetWidget<ViewExpenseDetailsController> {
  const ViewExpenseDetailsScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(),
        body: Container(
          margin: EdgeInsets.fromLTRB(14.h, 15.v, 18.h, 5.v),
          decoration: AppDecoration.outlineGray.copyWith(
            borderRadius: BorderRadiusStyle.roundedBorder10,
          ),
          child: Obx(
            () {
              if (controller.expenseList.length > 0) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(height: 8.v),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                        title: "Expense Type",
                        textValue: controller.getExpenseNameFromTypeID(
                                controller.expenseList[0].expenseType ?? 0) ??
                            "",
                      ),
                    ),
                    SizedBox(height: 8.v),

                    // 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                        title: "Expense Amount",
                        textValue: "₹${controller.expenseList[0].amount}",
                      ),
                    ),

                    SizedBox(height: 8.v),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                        title: "Date",
                        textValue: controller
                            .formatDate("${controller.expenseList[0].date}"),
                      ),
                    ),
                    SizedBox(height: 8.v),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                          title: "Status",
                          textValue:
                              controller.expenseList[0].expenseStatus == 0
                                  ? "Pending"
                                  : controller.expenseList[0].expenseStatus == 1
                                      ? "Approved"
                                      : "Rejected"),
                    ),
                    SizedBox(height: 8.v),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                        title: "Details",
                        textValue: "${controller.expenseList[0].expenseDetail}",
                      ),
                    ),
                    SizedBox(height: 16.v),
                    Visibility(
                      visible: controller.expenseList[0].billPicture != ""
                          ? true
                          : false,
                      child: CustomElevatedButton(
                        height: 60.v,
                        text: 'View expense photo',
                        buttonStyle: CustomButtonStyles.fillPrimary,
                        buttonTextStyle: CustomTextStyles
                            .labelLargeOpenSansOnErrorContainerExtraBold,
                        onPressed: () {
                          print("expense button tapped");
                          String? myString =
                              controller.expenseList[0].billPicture;
                          int imgLength = myString?.length ?? 0;

                          bool isPdf = myString?.endsWith('.pdf') ?? false;
                          print("isPdf: ${isPdf}");

                          print(
                              "${ApiClient.imgExpenseBaseURL}${controller.expenseList[0].billPicture}");

                          if (imgLength < 75) {
                            openImageDialog(
                                "Expense Image",
                                "${ApiClient.imgExpenseBaseURL}${controller.expenseList[0].billPicture}",
                                1);
                          } else {
                            openImageDialog("Expense Image",
                                "${controller.expenseList[0].billPicture}", 2);
                          }
                        },
                      ),
                    ),
                  ],
                );
              } else {
                return Center(
                  child: CircularProgressIndicator(),
                );
              }
            },
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "Expense Details",
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildClaimType({
    required String title,
    required String textValue,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 1.v),
          child: Text(
            title,
            style: CustomTextStyles.titleSmallPoppinsGray60002.copyWith(
              color: appTheme.gray60002,
            ),
          ),
        ),
        SizedBox(width: 20),
        Expanded(
          child: Text(
            textValue,
            textAlign: TextAlign.end,
            style: CustomTextStyles.bodyMediumPoppinsBluegray900.copyWith(
              color: appTheme.blueGray900,
            ),
            maxLines: 10,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
