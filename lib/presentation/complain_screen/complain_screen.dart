// ignore_for_file: unused_local_variable, invalid_use_of_protected_member

import 'package:sfm_new/data/filter_option.dart';
import 'package:sfm_new/sfmDatabase/models/claim_complain_model.dart';
import 'controller/complain_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_icon_button.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

// ignore_for_file: must_be_immutable
class ComplainScreen extends GetWidget<ComplainController> {
  const ComplainScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(),
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(horizontal: 4.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 15.v),
              CustomSearchBar(),
              SizedBox(height: 12.v),
              _buildComplainList(),
              SizedBox(height: 12.v),
            ],
          ),
        ),
        floatingActionButton: _buildFloatingActionButton(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "lbl_complain".tr,
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildComplainList() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 0.h),
        child: Obx(
          () {
            if (controller.isLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            } else if (controller.filteredComplainList.isEmpty) {
              return Center(
                child: Text(
                  'No complains found',
                  style: TextStyle(fontSize: 16),
                ),
              );
            } else {
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.h),
                child: ListView.separated(
                  itemCount: controller.filteredComplainList.length,
                  physics: BouncingScrollPhysics(),
                  shrinkWrap: true,
                  separatorBuilder: (
                    context,
                    index,
                  ) {
                    return SizedBox(
                      height: 10.v,
                    );
                  },
                  itemBuilder: (context, index) => CustomComplainListItem(
                    complain: controller.filteredComplainList[index],
                  ),
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return CustomIconButton(
      height: 70.adaptSize,
      width: 70.adaptSize,
      padding: EdgeInsets.all(19.h),
      decoration: IconButtonStyleHelper.outlineOnErrorContainer,
      alignment: Alignment.bottomRight,
      child: CustomImageView(
        imagePath: ImageConstant.imgGroup29,
      ),
      onTap: () {
        print("Plus button pressed");
        Get.toNamed(AppRoutes.addComplainScreen);
      },
    );
  }
}

class CustomSearchBar extends StatelessWidget {
  ComplainController controller = Get.put(ComplainController());

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 8, right: 8, top: 4, bottom: 4),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.5),
              spreadRadius: 2,
              blurRadius: 7,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                style: CustomTextStyles
                    .bodyMediumBluegray700, 
                onChanged: (value) {
                  print('Search value: $value');
                  controller.updateSearchText(value);
                },
                decoration: InputDecoration(
                  hintText: 'Search Complain',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 10.0),
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(10.0),
                  bottomRight: Radius.circular(10.0),
                ),
              ),
              child: IconButton(
                icon: Container(
                  child: CustomImageView(
                    svgPath: ImageConstant.imgFilterIcon,
                    height: 20.adaptSize,
                    width: 20.adaptSize,
                  ),
                ), 
                onPressed: () {
                  print('Filter button pressed');
                  FocusManager.instance.primaryFocus?.unfocus();
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return CustomDialogFilter();
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomDialogFilter extends StatelessWidget {
  CustomDialogFilter({
    Key? key,
  }) : super(key: key);

  ComplainController controller = Get.put(ComplainController());

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      insetPadding: EdgeInsets.all(12),
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Filters",
              style: CustomTextStyles.titleLargeGray900,
            ),
            SizedBox(height: 8.v),
            filterComplainByType(),
            SizedBox(height: 16.0),
            Text(
              "Status",
              style: CustomTextStyles.titleLargeGray900,
            ),
            SizedBox(height: 8.v),
            filterComplainByStatus(),
            SizedBox(height: 16.v),
            _buildDate(context),
            SizedBox(height: 16.v),
            Container(
              width: MediaQuery.of(context).size.width * 0.75,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomElevatedButton(
                    width: MediaQuery.of(context).size.width * 0.35,
                    text: "lbl_cancel".tr,
                    buttonStyle: CustomButtonStyles.fillOnErrorContainer,
                    buttonTextStyle: CustomTextStyles.titleSmallPoppinsOnError,
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  CustomElevatedButton(
                    width: MediaQuery.of(context).size.width * 0.35,
                    text: "lbl_apply".tr,
                    margin: EdgeInsets.only(left: 16.h),
                    buttonStyle: CustomButtonStyles.fillPrimaryTL8,
                    buttonTextStyle:
                        CustomTextStyles.titleSmallPoppinsOnErrorContainer,
                    onPressed: () {
                      print(
                          "controller.filterFromDate.value: ${controller.filterFromDate.value}");
                      print(
                          "controller.filterToDate.value: ${controller.filterToDate.value}");
                      print(
                          "controller.currentLeaveTypes.value: ${controller.currentComplainTypes.value}");
                      print(
                          "controller.currentLeaveStatuses.value: ${controller.currentComplainStatuses.value}");
                      controller.filterClaimComplain(
                          controller.filterFromDate.value,
                          controller.filterToDate.value,
                          controller.currentComplainTypes.value,
                          controller.currentComplainStatuses.value);
                      Get.back();
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDate(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "lbl_date".tr,
          style: CustomTextStyles.titleLargeGray900,
        ),
        SizedBox(height: 9.v),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                // Get.dialog(DateRangeDialog());
                showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                  builder: (context, child) {
                    return Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: ColorScheme.light(
                          primary: theme.colorScheme
                              .primary,
                        ),
                      ),
                      child: child!,
                    );
                  },
                ).then((value) {
                  if (value != null) {
                    print("fromdate value: $value");
                    String dateValue = value.toString();
                    controller.formattedFromDate.value =
                        controller.formatDateTime(dateValue);
                    print(controller.formattedFromDate.value);
                    controller.filterFromDate.value =
                        controller.formatFilterDateTime(dateValue);
                    print(controller.formattedFromDate.value);
                    controller.fromDate.value = value;
                  }
                });
              },
              child: Obx(
                () => Container(
                  width: 115.h,
                  decoration: AppDecoration.outlineBlack90013,
                  child: Row(
                    children: [
                      _buildDateInputColored(
                        dateText: controller.formattedFromDate.value,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 8.h,
                right: 8.h,
                // top: 11.v,
                // bottom: 7.v,
              ),
              child: Text(
                "to",
                style: CustomTextStyles.bodyMediumOpenSansBlack900,
              ),
            ),
            GestureDetector(
              onTap: () {
                showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                  builder: (context, child) {
                    return Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: ColorScheme.light(
                          primary: theme.colorScheme
                              .primary, 
                        ),
                      ),
                      child: child!,
                    );
                  },
                ).then((value) {
                  if (value != null) {
                    print("todate value123: $value");
                    String dateValue = value.toString();
                    controller.formattedToDate.value =
                        controller.formatDateTime(dateValue);
                    print(controller.formattedToDate.value);
                    controller.filterToDate.value =
                        controller.formatFilterDateTime(dateValue);
                    print(controller.filterToDate.value);
                    controller.toDate.value = value;
                  }
                });
              },
              child: Obx(
                () => Container(
                  width: 115.h,
                  decoration: AppDecoration.outlineBlack90013,
                  child: Row(
                    children: [
                      _buildDateInputColored(
                        dateText: controller.formattedToDate.value,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateInputColored({required String dateText}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 14.h,
        vertical: 9.v,
      ),
      decoration: AppDecoration.outlineBlack90014.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder10,
      ),
      child: Row(
        children: [
          CustomImageView(
            svgPath: ImageConstant.imgCalendarPrimary,
            height: 13.v,
            width: 12.h,
            margin: EdgeInsets.symmetric(vertical: 3.v),
          ),
          Padding(
            padding: EdgeInsets.only(
              left: 10.h,
              top: 2.v,
            ),
            child: Text(
              dateText,
              style: CustomTextStyles.bodySmallOpenSansGray700.copyWith(
                color: appTheme.gray700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget filterComplainByType() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          FilterOption(
            text: "All",
            isSelected:
                controller.currentComplainTypes.contains(ComplainType.all)
                    ? true
                    : false,
            onTap: () {
              controller.currentComplainTypes.value = {ComplainType.all};
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Claim",
            isSelected:
                controller.currentComplainTypes.contains(ComplainType.claim)
                    ? true
                    : false,
            onTap: () {
              controller.currentComplainTypes.value = {ComplainType.claim};
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Complain",
            isSelected:
                controller.currentComplainTypes.contains(ComplainType.complain)
                    ? true
                    : false,
            onTap: () {
              controller.currentComplainTypes.value = {ComplainType.complain};
            },
          ),
        ],
      ),
    );
  }

  Widget filterComplainByStatus() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          FilterOption(
            text: "lbl_all".tr,
            isSelected:
                controller.currentComplainStatuses.contains(ComplainStatus.all)
                    ? true
                    : false,
            onTap: () {
              if (controller.currentComplainStatuses
                  .contains(ComplainStatus.all)) {
                controller.currentComplainStatuses.remove(ComplainStatus.all);
              } else {
                controller.currentComplainStatuses.add(ComplainStatus.all);
              }

              if (controller.currentComplainStatuses
                  .contains(ComplainStatus.pending)) {
                controller.currentComplainStatuses
                    .remove(ComplainStatus.pending);
              }

              if (controller.currentComplainStatuses
                  .contains(ComplainStatus.approved)) {
                controller.currentComplainStatuses
                    .remove(ComplainStatus.approved);
              }

              if (controller.currentComplainStatuses
                  .contains(ComplainStatus.rejected)) {
                controller.currentComplainStatuses
                    .remove(ComplainStatus.rejected);
              }

              print(
                  "controller.currentComplainStatuses: ${controller.currentComplainStatuses}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "lbl_pending".tr,
            isSelected: controller.currentComplainStatuses
                    .contains(ComplainStatus.pending)
                ? true
                : false,
            onTap: () {
              if (controller.currentComplainStatuses
                  .contains(ComplainStatus.pending)) {
                controller.currentComplainStatuses
                    .remove(ComplainStatus.pending);
              } else {
                controller.currentComplainStatuses.add(ComplainStatus.pending);
              }

              if (controller.currentComplainStatuses
                  .contains(ComplainStatus.all)) {
                controller.currentComplainStatuses.remove(ComplainStatus.all);
              }

              print(
                  "controller.currentComplainStatuses: ${controller.currentComplainStatuses}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "lbl_approved".tr,
            isSelected: controller.currentComplainStatuses
                    .contains(ComplainStatus.approved)
                ? true
                : false,
            onTap: () {
              if (controller.currentComplainStatuses
                  .contains(ComplainStatus.approved)) {
                controller.currentComplainStatuses
                    .remove(ComplainStatus.approved);
              } else {
                controller.currentComplainStatuses.add(ComplainStatus.approved);
              }

              if (controller.currentComplainStatuses
                  .contains(ComplainStatus.all)) {
                controller.currentComplainStatuses.remove(ComplainStatus.all);
              }

              print(
                  "controller.currentComplainStatuses: ${controller.currentComplainStatuses}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "lbl_rejected".tr,
            isSelected: controller.currentComplainStatuses
                    .contains(ComplainStatus.rejected)
                ? true
                : false,
            onTap: () {
              if (controller.currentComplainStatuses
                  .contains(ComplainStatus.rejected)) {
                controller.currentComplainStatuses
                    .remove(ComplainStatus.rejected);
              } else {
                controller.currentComplainStatuses.add(ComplainStatus.rejected);
              }

              if (controller.currentComplainStatuses
                  .contains(ComplainStatus.all)) {
                controller.currentComplainStatuses.remove(ComplainStatus.all);
              }

              print(
                  "controller.currentComplainStatuses: ${controller.currentComplainStatuses}");
            },
          ),
        ],
      ),
    );
  }

  Widget filterComplainBy() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          FilterOption(
            text: "lbl_all".tr,
            isSelected: controller.currentComplainBy.contains(ComplainBy.all)
                ? true
                : false,
            onTap: () {
              if (controller.currentComplainBy.contains(ComplainBy.all)) {
                controller.currentComplainBy.remove(ComplainBy.all);
              } else {
                controller.currentComplainBy.add(ComplainBy.all);
              }

              if (controller.currentComplainBy.contains(ComplainBy.cf)) {
                controller.currentComplainBy.remove(ComplainBy.cf);
              }

              if (controller.currentComplainBy.contains(ComplainBy.retailer)) {
                controller.currentComplainBy.remove(ComplainBy.retailer);
              }

              if (controller.currentComplainBy
                  .contains(ComplainBy.distributor)) {
                controller.currentComplainBy.remove(ComplainBy.distributor);
              }

              print(
                  "controller.currentComplainBy: ${controller.currentComplainBy}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "lbl_ss".tr,
            isSelected: controller.currentComplainBy.contains(ComplainBy.cf)
                ? true
                : false,
            onTap: () {
              if (controller.currentComplainBy.contains(ComplainBy.cf)) {
                controller.currentComplainBy.remove(ComplainBy.cf);
              } else {
                controller.currentComplainBy.add(ComplainBy.cf);
              }

              if (controller.currentComplainBy.contains(ComplainBy.all)) {
                controller.currentComplainBy.remove(ComplainBy.all);
              }

              print(
                  "controller.currentComplainBy: ${controller.currentComplainBy}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "lbl_retailer".tr,
            isSelected:
                controller.currentComplainBy.contains(ComplainBy.retailer)
                    ? true
                    : false,
            onTap: () {
              if (controller.currentComplainBy.contains(ComplainBy.retailer)) {
                controller.currentComplainBy.remove(ComplainBy.retailer);
              } else {
                controller.currentComplainBy.add(ComplainBy.retailer);
              }

              if (controller.currentComplainBy.contains(ComplainBy.all)) {
                controller.currentComplainBy.remove(ComplainBy.all);
              }

              print(
                  "controller.currentComplainBy: ${controller.currentComplainBy}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "lbl_distributor",
            isSelected:
                controller.currentComplainBy.contains(ComplainBy.distributor)
                    ? true
                    : false,
            onTap: () {
              if (controller.currentComplainBy
                  .contains(ComplainBy.distributor)) {
                controller.currentComplainBy.remove(ComplainBy.distributor);
              } else {
                controller.currentComplainBy.add(ComplainBy.distributor);
              }

              if (controller.currentComplainBy.contains(ComplainBy.all)) {
                controller.currentComplainBy.remove(ComplainBy.all);
              }

              print(
                  "controller.currentComplainBy: ${controller.currentComplainBy}");
            },
          ),
        ],
      ),
    );
  }
}

class CustomComplainListItem extends StatelessWidget {
  final ClaimComplain complain;
  final ComplainController controller = Get.put(ComplainController());

  CustomComplainListItem({
    required this.complain,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        print("ccCode ${complain.ccCode}");
        ComplainController.instance.selectedCCCode.value = "${complain.ccCode}";
        print(
            "selectedCCCode ${ComplainController.instance.selectedCCCode.value}");
        Get.toNamed(AppRoutes.viewComplainScreen);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 9.h,
          vertical: 6.v,
        ),
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 4.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    complain.ccTypeID == 1 ? "Claim" : "Complain",
                    style: CustomTextStyles.titleSmallBluegray900,
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 2.v),
                    child: Text(
                      controller.formatDate(complain.ccCreatedAt ?? "date"),
                      style: CustomTextStyles.bodySmallOpenSansGray500,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 8.v),
            Row(
              children: [
                Text(
                  "Description:",
                  style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                ),
                SizedBox(width: 3),
                Expanded(
                  child: Text(
                    "${complain.ccNote}",
                    style: CustomTextStyles.labelLargeOpenSansff8e8e8e,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8),
                Visibility(
                  visible: complain.syncStatus == 0 ? true : false,
                  child: CustomImageView(
                    svgPath: ImageConstant.imgNotSynced,
                    color: Colors.red,
                    height: 15.adaptSize,
                    width: 15.adaptSize,
                    margin: EdgeInsets.only(bottom: 2.v),
                  ),
                ),
                SizedBox(width: 4),
                CustomImageView(
                  svgPath: ImageConstant.imgArrowRight,
                  // color: Colors.black,
                  height: 15.adaptSize,
                  width: 15.adaptSize,
                  margin: EdgeInsets.only(bottom: 2.v),
                ),
              ],
            ),
            SizedBox(height: 4.v),
            Text(
              complain.ccStatus == 0
                  ? "Pending"
                  : complain.ccStatus == 1
                      ? "Approved"
                      : "Rejected",
              style: CustomTextStyles.labelLargeOpenSansBluegray400.copyWith(
                color: complain.ccStatus == 0
                    ? Colors.orange
                    : complain.ccStatus == 1
                        ? Colors.green
                        : Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
