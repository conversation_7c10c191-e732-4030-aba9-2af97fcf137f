// ignore_for_file:

import 'dart:async';

import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/claim_complain_model.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart' as dio_package;

enum ComplainType { all, claim, complain }

enum ComplainStatus { all, pending, approved, rejected }

enum ComplainBy { all, cf, retailer, distributor }

class ComplainController extends GetxController {
  var selectedOptions = <String>[].obs;

  RxList<ClaimComplain> complainList = <ClaimComplain>[].obs;
  RxList<ClaimComplain> filteredComplainList = <ClaimComplain>[].obs;

  RxList<ClaimComplain> selectedComplainList = <ClaimComplain>[].obs;

  RxSet<ComplainType> currentComplainTypes =
      <ComplainType>{ComplainType.all}.obs;
  RxSet<ComplainStatus> currentComplainStatuses =
      <ComplainStatus>{ComplainStatus.all}.obs;
  RxSet<ComplainBy> currentComplainBy = <ComplainBy>{ComplainBy.all}.obs;

  var isLoading = true.obs;

  final searchText = ''.obs;

  late String formattedDateTime;

  late Rx<DateTime> fromDate;
  late Rx<DateTime> toDate;

  RxString formattedFromDate = ''.obs;
  RxString formattedToDate = ''.obs;

  RxString filterFromDate = ''.obs;
  RxString filterToDate = ''.obs;

  RxString selectedCCCode = ''.obs;

  static ComplainController get instance => Get.find();

  final Connectivity _connectivity = Connectivity();

  RxString employeeToken = "".obs;
  bool isComplainAPISuccess = false;

  @override
  void onInit() async {
    super.onInit();

    String dateTimeString = '2024-03-19T07:54:10.000000Z';
    String formattedDate = formatFilterDateTime(dateTimeString);
    print('Formatted Date: $formattedDate');

    EasyLoading.show(status: 'Loading...');

    fetchComplainDetails();

    await checkInternetConnectivity();
  }

  void fetchComplainDetails() async {
    // EasyLoading.show(status: 'Loading...');
    isLoading.value = true;
    fromDate = DateTime.now().obs;
    toDate = (DateTime.now().add(Duration(days: 0))).obs;

    formattedFromDate.value = formatDateTime(fromDate.toString());
    formattedToDate.value = formatDateTime(toDate.toString());

    formattedFromDate.value = formatDateTime(fromDate.toString());
    formattedToDate.value = formatDateTime(toDate.toString());

    await fetchedToken();

    await fetchClaimComplain();

    EasyLoading.dismiss();
    isLoading.value = false;
  }

  Future<bool> checkInternetConnectivity() async {
    var connectivityResult = await _connectivity.checkConnectivity();
    if (connectivityResult != ConnectivityResult.none) {
      syncComplainsWithServer();
    }
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> syncComplainsWithServer() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedComplains = await db.query(
        '${TableValues.tableClaimComplain}',
        where: '${TableValues.ccSyncStatus} = ?',
        whereArgs: [0]);

    if (unsyncedComplains.isNotEmpty) {
      print('Syncing complains with the server...');
      print("unsyncedComplains: ${unsyncedComplains}");

      for (final complain in unsyncedComplains) {
        try {
          await addComplainAPI(complain);

          if (isComplainAPISuccess == true) {
            await db.update('${TableValues.tableClaimComplain}',
                {'${TableValues.ccSyncStatus}': 1},
                where: '${TableValues.ccCode} = ?',
                whereArgs: [complain['${TableValues.ccCode}']]);
            print(
                'Complain with ID ${complain['${TableValues.ccCode}']} synced successfully');
          } else {
            print(
                'Error syncing Complain with ID ${complain['${TableValues.ccCode}']}');
            await ApiLogger.logError(
              apiName: "Add complain sync",
              apiRequestData: complain,
              apiResponse:
                  'Error syncing complain with ID ${complain['${TableValues.ccCode}']}',
            );
          }
        } catch (error) {
          print(
              'Error syncing Complain with ID ${complain['${TableValues.ccCode}']}: $error');

          await ApiLogger.logError(
            apiName: "Add complain sync with error",
            apiRequestData: complain,
            apiResponse:
                'Error syncing complain with ID ${complain['${TableValues.ccCode}']}: $error',
          );
        }
      }

      fetchComplainDetails();
    } else {
      print('No complains to sync.');
    }
  }

  void toggleOption(String option) {
    if (selectedOptions.contains(option)) {
      selectedOptions.remove(option);
    } else {
      selectedOptions.add(option);
    }
  }

  String formatFilterDateTime(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('yyyy-MM-dd HH:mm:ss.SSSZ');
    return formatter.format(dateTime);
  }

  String formatDateTime(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('dd MMM');
    return formatter.format(dateTime);
  }

  Future<void> selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isFromDate ? fromDate.value : toDate.value,
      firstDate: DateTime(2015, 8),
      lastDate: DateTime(2101),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: theme.colorScheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      if (isFromDate) {
        fromDate.value = picked;
      } else {
        toDate.value = picked;
      }
    }
  }

  void applyDateRange() {
    print('Selected Date Range: ${fromDate.value} to ${toDate.value}');
    Get.back();
  }

  Future<List<ClaimComplain>> getUnsubmittedComplaints() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps = await db.query(
      '${TableValues.tableClaimComplain}',
      where: '${TableValues.ccSyncStatus} = 0',
    );
    return List.generate(maps.length, (i) => ClaimComplain.fromMap(maps[i]));
  }

  Future<void> markSubmitted(int id) async {
    final db = await DatabaseProvider.database;
    await db.update(
      '${TableValues.tableClaimComplain}',
      {'${TableValues.ccSyncStatus}': 1},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> addComplainAPI(Map<String, dynamic> complain) async {
    try {
      Map<String, dynamic> data = {};

      data = {
        "ccm_code": "${complain['ccm_code']}",
        "ccm_type_id": "${complain['ccm_type_id']}",
        "ccm_note": "${complain['ccm_note']}",
        "cm_code": "${complain['cm_code']}",
        "emp_code": "${complain['emp_code']}",
        "ccm_cc_type": "${complain['ccm_cc_type']}",
        "ccm_image": "${complain['ccm_image']}",
      };

      print("data: ${data}");

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };

      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.postAddComplainEndpoint),
        data: data,
        options: dio_package.Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.data);
      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            isComplainAPISuccess = true;
            EasyLoading.dismiss();
          }
        } else {
          isComplainAPISuccess = false;
          print('Response data is null');
        }
      } catch (e) {
        isComplainAPISuccess = false;
        print('Error parsing response data: $e');
      }
    } on dio_package.DioException catch (e) {
      isComplainAPISuccess = false;
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        } else {}
      }
    } finally {
      EasyLoading.dismiss();
    }
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  Future<void> fetchClaimComplain() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableClaimComplain}");
    complainList.value = List.generate(maps.length, (index) {
      return ClaimComplain.fromMap(maps[index]);
    });
    print("complainList: ${complainList}");

    filteredComplainList.value = List.generate(maps.length, (index) {
      return ClaimComplain.fromMap(maps[index]);
    });

    filteredComplainList
        .sort((a, b) => b.ccCreatedAt!.compareTo(a.ccCreatedAt!));
  }

  Future<void> fetchFilteredComplains() async {
    filteredComplainList.value = complainList
        .where((complain) => complain.ccNote!
            .toLowerCase()
            .contains(searchText.value.toLowerCase()))
        .toList();

    print("filteredComplainList: ${filteredComplainList}");

    filteredComplainList
        .sort((a, b) => b.ccCreatedAt!.compareTo(a.ccCreatedAt!));
  }

  void updateSearchText(String newText) {
    searchText.value = newText;
    fetchFilteredComplains();
  }

  String formatDate(String dateString) {
    if (dateString.isNotEmpty) {
      DateTime dateTime = DateTime.parse(dateString);
      String formattedDate = DateFormat('dd MMM yyyy').format(dateTime);
      return formattedDate;
    } else {
      return "";
    }
  }

  void filterClaimComplain(
      String fromDateStr,
      String toDateStr,
      Set<ComplainType> currentComplainType,
      Set<ComplainStatus> complainStatuses) {
    filteredComplainList.clear();

    DateTime fromDate = DateTime.tryParse(fromDateStr) ?? DateTime.now();
    print('fromDate: ${fromDate}');
    DateTime toDate = DateTime.tryParse(toDateStr) ?? DateTime.now();
    print('toDate: ${toDate}');
    bool isWithinDateRange = false;
    bool isDateSelected = false;

    List<ClaimComplain> filteredComplains = complainList.where((complain) {
      if (!fromDateStr.isEmpty || !toDateStr.isEmpty) {
        isDateSelected = true;

        DateTime complainDate = DateTime.parse(complain.ccCreatedAt ?? "");
        print("complainDate: ${complainDate}");

        DateTime complainDateOnly =
            DateTime(complainDate.year, complainDate.month, complainDate.day);
        DateTime fromDateOnly =
            DateTime(fromDate.year, fromDate.month, fromDate.day);
        DateTime toDateOnly = DateTime(toDate.year, toDate.month, toDate.day);

        isWithinDateRange = complainDateOnly.isAtSameMomentAs(fromDateOnly) ||
            complainDateOnly.isAtSameMomentAs(toDateOnly) ||
            (complainDateOnly.isAfter(fromDateOnly) &&
                complainDateOnly.isBefore(toDateOnly));
      } else {
        isDateSelected = false;
      }

      bool matchesComplainType =
          currentComplainType.contains(ComplainType.all) ||
              (currentComplainType.contains(ComplainType.claim) &&
                  complain.ccTypeID == 1) ||
              (currentComplainType.contains(ComplainType.complain) &&
                  complain.ccTypeID == 2);

      bool matchesComplainStatus =
          complainStatuses.contains(ComplainStatus.all) ||
              (complainStatuses.contains(ComplainStatus.pending) &&
                  complain.ccStatus == 0) ||
              (complainStatuses.contains(ComplainStatus.approved) &&
                  complain.ccStatus == 1) ||
              (complainStatuses.contains(ComplainStatus.rejected) &&
                  complain.ccStatus == 3);

      // Debugging information
      print("Complain Code: ${complain.ccCode}");
      print("isWithinDateRange: $isWithinDateRange");
      print("matchesComplainType: $matchesComplainType");
      print("matchesComplainStatus: $matchesComplainStatus");

      if (isDateSelected) {
        if (isWithinDateRange &&
            (matchesComplainType && matchesComplainStatus)) {
          print("data added in filteredComplainList");
          filteredComplainList.add(complain);
        }
      } else {
        if (isWithinDateRange ||
            (matchesComplainType && matchesComplainStatus)) {
          print("data added in filteredComplainList");
          filteredComplainList.add(complain);
        }
      }

      if (filteredComplainList.length > 0) {
        filteredComplainList
            .sort((a, b) => b.ccCreatedAt!.compareTo(a.ccCreatedAt!));
      }

      return matchesComplainType && matchesComplainStatus;
    }).toList();
    print("filteredComplainList: ${filteredComplainList}");
    print("filteredComplains ${filteredComplains}");
  }
}

class DateRangeDialog extends StatelessWidget {
  final ComplainController controller = Get.put(ComplainController());

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Select Date Range'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Row(
            children: <Widget>[
              Text('From:'),
              TextButton(
                onPressed: () => controller.selectDate(context, true),
                child: Obx(() => Text(DateFormat('yyyy-MM-dd')
                    .format(controller.fromDate.value))),
              ),
            ],
          ),
          Row(
            children: <Widget>[
              Text('To:'),
              TextButton(
                onPressed: () => controller.selectDate(context, false),
                child: Obx(() => Text(
                    DateFormat('yyyy-MM-dd').format(controller.toDate.value))),
              ),
            ],
          ),
        ],
      ),
      actions: <Widget>[
        TextButton(
          onPressed: () {
            Get.back();
          },
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 0.3,
            height: 50,
            child: Center(child: Text('Cancel')),
          ),
        ),
        ElevatedButton(
          onPressed: () => controller.applyDateRange(),
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 0.3,
            height: 50,
            child: Center(
              child: Text('Apply'),
            ),
          ),
        ),
      ],
    );
  }
}
