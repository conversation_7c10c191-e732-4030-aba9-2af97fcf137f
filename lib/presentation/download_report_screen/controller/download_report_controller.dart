// ignore_for_file:  invalid_use_of_protected_member, unnecessary_null_comparison

import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:open_file_plus/open_file_plus.dart';
import 'dart:async';
import 'package:uuid/uuid.dart';

class Pair {
  final String text;
  final IconData icon;
  const Pair(this.text, this.icon);

  @override
  String toString() {
    return text;
  }
}

class DownloadReportController extends GetxController {
  RxString employeeCode = ''.obs;
  RxString employeeToken = "".obs;

  List<Map<String, dynamic>> reportNameList = [
    {'key': 'dbr', 'value': 'DBR'},
    {'key': 'dsr', 'value': 'DSR'},
    {'key': 'attendance_employee', 'value': 'Employee Attendance'},
    {'key': 'expense_report', 'value': 'Expense Report'},
    {'key': 'beatwise_retailer_report', 'value': 'Beat Wise Retailer Report'},
    {'key': 'target_vs_achivement', 'value': 'Target VS Achievement'},
  ];

  List<Map<String, dynamic>> reportTypeList = [
    {'key': 'brand_wise', 'value': 'Brand Wise'},
    {'key': 'product_wise', 'value': 'Product Wise'},
  ];

  List<Map<String, dynamic>> targetList = [
    {'key': 'primary', 'value': 'Primary'},
    {'key': 'secondary', 'value': 'Secondary'},
    {'key': 'both', 'value': 'Both'},
  ];

  List<Map<String, dynamic>> downloadTypeList = [
    {'key': 'excel', 'value': 'EXCEL'},
    {'key': 'pdf', 'value': 'PDF'},
  ];

  List<Map<String, dynamic>> monthList = [
    {'key': '01', 'value': 'January'},
    {'key': '02', 'value': 'February'},
    {'key': '03', 'value': 'March'},
    {'key': '04', 'value': 'April'},
    {'key': '05', 'value': 'May'},
    {'key': '06', 'value': 'June'},
    {'key': '07', 'value': 'July'},
    {'key': '08', 'value': 'August'},
    {'key': '09', 'value': 'September'},
    {'key': '10', 'value': 'October'},
    {'key': '11', 'value': 'November'},
    {'key': '12', 'value': 'December'},
  ];

  List<String> yearList = [
    '${DateTime.now().year}',
    '${DateTime.now().year - 1}',
    '${DateTime.now().year - 2}',
  ];

  RxString selectedReportName = ''.obs;
  RxString selectedReportType = ''.obs;
  RxString selectedTargetType = ''.obs;
  RxString selectedDownloadType = ''.obs;
  RxString selectedMonthName = ''.obs;
  RxString selectedMonthKey = ''.obs;
  RxString selectedYear = ''.obs;
  final Dio dio = Dio();

  RxBool _downloading = false.obs;
  RxString _filePath = ''.obs;

  bool get downloading => _downloading.value;
  String get filePath => _filePath.value;

  // final Rx<PermissionStatus> storagePermissionStatus =
  //     Rx<PermissionStatus>(PermissionStatus.denied);

  RxBool storagePermissionStatus = false.obs;

  @override
  void onInit() async {
    super.onInit();

    print("monthList: ${monthList}");
    print("yearList: ${yearList}");

    selectedYear.value = yearList[0];

    setCurrentMonth();

    await requestStoragePermission();

    await fetchEmployeeData();
    await fetchedToken();

    selectedDownloadType.value = 'excel';
  }

  void setCurrentMonth() {
    String currentMonthKey = DateFormat('MM').format(DateTime.now());
    String? currentMonthName = getMonthNameFromKey(currentMonthKey);
    if (currentMonthName != null) {
      selectedMonthName.value = currentMonthName;
      selectedMonthKey.value = currentMonthKey;
      print("selectedMonthName: ${selectedMonthName}");
      print("selectedMonthKey: ${selectedMonthKey}");
    }
  }

  String? getMonthNameFromKey(String key) {
    return monthList.firstWhere(
      (element) => element['key'] == key,
      orElse: () => {'value': null},
    )['value'];
  }

  void setSelectedMonth(String monthName) {
    selectedMonthName.value = monthName;
    selectedMonthKey.value = getKeyFromValue(monthName, monthList) ?? '';
  }

  @override
  void onClose() {
    super.onClose();
  }

  String? getKeyFromValue(
      String selectedType, List<Map<String, dynamic>> arrayList) {
    try {
      final name = arrayList.firstWhere(
        (name) => name['value'] == selectedType,
      );
      return name['key'];
    } catch (e) {
      if (kDebugMode) {
        print('Error in getReportKeyFromValue: $e');
      }
      return null;
    }
  }

  // Function to fetch user data from shared preferences
  Future<Map<String, dynamic>?> getUserData() async {
    String? userDataString =
        await SharedPrefManager.instance.getString(ConstantValues.userData) ??
            "";
    if (userDataString != null) {
      return Map<String, dynamic>.from(
        Map<String, dynamic>.fromIterable(
          userDataString.replaceAll('{', '').replaceAll('}', '').split(','),
          key: (e) => e.split(':')[0].trim(),
          value: (e) => e.split(':')[1].trim(),
        ),
      );
    } else {
      return null;
    }
  }

  String generateUUID() {
    var uuid = Uuid();
    return uuid.v4();
  }

  Future<String?> fetchEmployeeData() async {
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.employee) ??
            "";
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];
      employeeCode.value = empCode!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> fetchedToken() async {
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
    if (employeeDataJson != null) {
      employeeToken.value = employeeDataJson;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> requestStoragePermission() async {
    final DeviceInfoPlugin info =
        DeviceInfoPlugin(); 
    final AndroidDeviceInfo androidInfo = await info.androidInfo;
    print('releaseVersion : ${androidInfo.version.release}');
    final String androidVersion = "${androidInfo.version.release}";
    print("androidVersion: ${androidVersion}");
    bool havePermission = false;

    int comparison = compareAndroidVersions(
        androidVersion, "11"); 
    print("comparison: ${comparison}");

    if (comparison > 0) {
      final request = await [
        Permission.manageExternalStorage,
      ].request();

      havePermission =
          request.values.every((status) => status == PermissionStatus.granted);
      print("havePermission: ${havePermission}");
      storagePermissionStatus.value = havePermission;
    } else {
      final status = await Permission.storage.request();
      print("else status: ${status}");
      havePermission = status.isGranted;
      print("havePermission: ${havePermission}");
      storagePermissionStatus.value = havePermission;
      print("storagePermissionStatus: ${storagePermissionStatus}");
    }

    if (!havePermission) {
      // if no permission then open app-setting
      // await openAppSettings();
      Get.dialog(
        AlertDialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.transparent,
          title: Text(
            'Permission Denied',
            style: CustomTextStyles.titleMediumPrimary.copyWith(
              color: theme.colorScheme.primary,
            ),
          ),
          content: Text('Please allow permission to download report'),
          actions: [
            TextButton(
              onPressed: () async {
                await openAppSettings();
                Get.back();
              },
              child: Text(
                'Open Settings',
                style: CustomTextStyles.bodyMediumPoppinsBluegray900.copyWith(
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  int compareAndroidVersions(String version1, String version2) {
    // Split version strings into their components
    List<String> components1 = version1.split('.');
    List<String> components2 = version2.split('.');

    // Parse major version numbers
    int majorVersion1 = int.parse(components1[0]);
    int majorVersion2 = int.parse(components2[0]);

    // Assume minor and maintenance release versions are zero for single-number version
    int minorVersion1 = components1.length > 1 ? int.parse(components1[1]) : 0;
    int minorVersion2 = components2.length > 1 ? int.parse(components2[1]) : 0;
    int maintenanceVersion1 =
        components1.length > 2 ? int.parse(components1[2]) : 0;
    int maintenanceVersion2 =
        components2.length > 2 ? int.parse(components2[2]) : 0;

    // Compare major version numbers
    if (majorVersion1 < majorVersion2) {
      return -1;
    } else if (majorVersion1 > majorVersion2) {
      return 1;
    }

    // If major version numbers are equal, compare minor version numbers
    if (minorVersion1 < minorVersion2) {
      return -1;
    } else if (minorVersion1 > minorVersion2) {
      return 1;
    }

    // If minor version numbers are equal, compare maintenance release version numbers
    if (maintenanceVersion1 < maintenanceVersion2) {
      return -1;
    } else if (maintenanceVersion1 > maintenanceVersion2) {
      return 1;
    }

    // If all version numbers are equal, versions are the same
    return 0;
  }

  Future<void> getReport() async {
    _downloading.value = true;
    print("storagePermissionStatus: ${storagePermissionStatus.value}");
    if (storagePermissionStatus.value == true) {
      try {
        Map<String, dynamic> headers = {
          'Authorization': 'Bearer ${employeeToken.value}',
        };

        print("EasyLoading.show");
        EasyLoading.show(status: 'Loading...');
        Map<String, dynamic> data = {
          "report": selectedReportName.value,
          "r_type": selectedReportType.value, //brand_wise,product_wise
          "month": selectedMonthKey.value,
          "year": selectedYear.value,
          "search_employee": employeeCode.value,
          "prima_secon": selectedTargetType.value, //both,primary,secondary
          "d_type": selectedDownloadType.value, // excel
        };
        print("data: ${data}");
        final response = await ApiClient.dioClient
            .post(
          ApiClient.getUrl(ApiClient.getReportEndpoint),
          options: Options(headers: headers, responseType: ResponseType.bytes),
          data: data,
        )
            .timeout(Duration(seconds: 60), onTimeout: () {
          print('API call timed out');
          throw TimeoutException("API call exceeded the 60 seconds timeout");
        });
        print("response.statusCode: ${response.statusCode}");
        if (response.statusCode == 200) {
          try {
            // Get temporary directory path

            print('response.data: $response.data');

            // Create SFM folder if it doesn't exist
            Directory sfmDir = Directory('storage/emulated/0/SFM');
            if (!await sfmDir.exists()) {
              await sfmDir.create();
            }
            print("sfmDir: ${sfmDir}");

            // Create Report folder inside SFM if it doesn't exist
            Directory reportDir = Directory('${sfmDir.path}/Report');
            if (!await reportDir.exists()) {
              await reportDir.create();
            }
            print("reportDir: ${reportDir}");

            String fileName = "";

            String reportName = "";

            reportName = trimTrailingSlash(selectedReportName.value);
            print("reportName: ${reportName}");

            if (selectedDownloadType.value == "pdf") {
              fileName =
                  "${employeeCode.value}_${selectedMonthKey.value}_${selectedYear.value}_${selectedReportName.value}.pdf";
            } else {
              fileName =
                  "${employeeCode.value}_${selectedMonthKey.value}_${selectedYear.value}_${selectedReportName.value}.xlsx";
            }

            // Define file path for the downloaded file
            String filePath = '${reportDir.path}/$fileName';

            // Write file bytes to the file
            File file = File(filePath);
            await file.writeAsBytes(response.data);
            print("file: ${file}");

            // File saved successfully
            print('File saved to: $filePath');

            Get.dialog(
              AlertDialog(
                backgroundColor: Colors.white,
                surfaceTintColor: Colors.transparent,
                title: Text(
                  'File Saved',
                  style: CustomTextStyles.titleMediumPrimary.copyWith(
                    color: theme.colorScheme.primary,
                  ),
                ),
                content:
                    Text('The file has been saved to SFM/Report/${fileName}'),
                actions: [
                  TextButton(
                    onPressed: () {
                      print("reportDir.path: ${reportDir.path}");
                      OpenFile.open(filePath);
                      Get.back();
                    },
                    child: Text(
                      'Open File',
                      style: CustomTextStyles.bodyMediumPoppinsBluegray900
                          .copyWith(
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ),
                ],
              ),
            );
          } catch (e) {
            print('Error saving file: $e');
          }
        } else {
          print('Response data is null');
          showToastMessage("${response.statusMessage}");
        }
        EasyLoading.dismiss();
      } on DioException catch (e) {
        if (e.response != null) {
          print(
              'Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        }
      } finally {
        EasyLoading.dismiss();
      }
    } else {
      print('Storage permission not granted. Requesting again...');
      await requestStoragePermission();
    }
  }

  String trimTrailingSlash(String input) {
    return input.replaceAll(RegExp(r'\/+$'), '');
  }
}
