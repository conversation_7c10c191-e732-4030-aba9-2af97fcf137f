import 'package:sfm_new/core/utils/webview_image_load.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';

import 'controller/view_payment_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

// ignore_for_file: must_be_immutable
class ViewPaymentScreen extends GetWidget<ViewPaymentController> {
  const ViewPaymentScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(),
        body: Container(
          margin: EdgeInsets.fromLTRB(14.h, 15.v, 18.h, 5.v),
          decoration: AppDecoration.outlineGray.copyWith(
            borderRadius: BorderRadiusStyle.roundedBorder10,
          ),
          child: Obx(
            () {
              if (controller.paymentList.length > 0) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(height: 8.v),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: EdgeInsets.only(left: 16.h),
                        child: Text(
                          controller.getCustomerNameFromTypeID(
                                  controller.paymentList[0].cmCode!) ??
                              "",
                          style: CustomTextStyles.titleMediumBluegray900Bold_1,
                        ),
                      ),
                    ),
                    SizedBox(height: 8.v),
                    Visibility(
                      visible:
                          controller.paymentList[0].type != 1 ? true : false,
                      child: Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.h),
                            child: _buildClaimType(
                              title: "Bank",
                              textValue: controller.getBankNameFromTypeID(
                                      controller.paymentList[0].bankId ?? 0) ??
                                  "",
                            ),
                          ),
                          SizedBox(height: 8.v),
                        ],
                      ),
                    ),
                    Visibility(
                      visible:
                          controller.paymentList[0].type != 1 ? true : false,
                      child: Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.h),
                            child: _buildClaimType(
                              title: "Account Number",
                              textValue:
                                  controller.paymentList[0].accountNo ?? "",
                            ),
                          ),
                          SizedBox(height: 8.v),
                        ],
                      ),
                    ),
                    Visibility(
                      visible:
                          controller.paymentList[0].type == 3 ? true : false,
                      child: Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.h),
                            child: _buildClaimType(
                              title: "UTR Number",
                              textValue:
                                  controller.paymentList[0].chequeNo ?? "",
                            ),
                          ),
                          SizedBox(height: 8.v),
                        ],
                      ),
                    ),
                    Visibility(
                      visible:
                          controller.paymentList[0].type == 2 ? true : false,
                      child: Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.h),
                            child: _buildClaimType(
                              title: "Cheque Number",
                              textValue:
                                  controller.paymentList[0].chequeNo ?? "",
                            ),
                          ),
                          SizedBox(height: 8.v),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                        title: "Amount",
                        textValue:
                            "₹${controller.paymentList[0].amount ?? 0.0}",
                      ),
                    ),
                    SizedBox(height: 8.v),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                        title: "lbl_note".tr,
                        textValue: "${controller.paymentList[0].note}",
                      ),
                    ),
                    SizedBox(height: 8.v),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.h),
                      child: _buildClaimType(
                        title: "Status",
                        textValue: controller.paymentList[0].status == 0
                            ? "Pending"
                            : controller.paymentList[0].status == 1
                                ? "Approved"
                                : "Rejected",
                      ),
                    ),
                    SizedBox(height: 16.v),
                    Visibility(
                      visible:
                          controller.paymentList[0].type != 1 ? true : false,
                      child: Column(
                        children: [
                          CustomElevatedButton(
                            height: 50.v,
                            text: "View payment photo",
                            buttonStyle: CustomButtonStyles.fillPrimary,
                            buttonTextStyle: CustomTextStyles
                                .labelLargeOpenSansOnErrorContainerExtraBold,
                            onPressed: () {
                              print("payment button tapped");
                              String? myString =
                                  controller.paymentList[0].chequePhoto;
                              int imgLength = myString?.length ?? 0;

                              if (imgLength < 75) {
                                openImageDialog(
                                    "Payment Image",
                                    "${ApiClient.imgPaymentCollectionBaseURL}${controller.paymentList[0].chequePhoto}",
                                    1);
                              } else {
                                openImageDialog(
                                    "Payment Image",
                                    "${controller.paymentList[0].chequePhoto}",
                                    2);
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              } else {
                return Center(
                  child: CircularProgressIndicator(),
                );
              }
            },
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "Payment",
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildClaimType({
    required String title,
    required String textValue,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 1.v),
          child: Text(
            title,
            style: CustomTextStyles.titleSmallPoppinsGray60002.copyWith(
              color: appTheme.gray60002,
            ),
          ),
        ),
        SizedBox(width: 20),
        Expanded(
          child: Text(
            textValue,
            textAlign: TextAlign.end,
            style: CustomTextStyles.bodyMediumPoppinsBluegray900.copyWith(
              color: appTheme.blueGray900,
            ),
            maxLines: 10,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
