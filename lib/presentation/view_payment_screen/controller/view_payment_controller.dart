import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/bank_model.dart';
import 'package:sfm_new/sfmDatabase/models/customer_model.dart';
import 'package:sfm_new/sfmDatabase/models/payment_collection_model.dart';
import 'package:sfm_new/sfmDatabase/models/payment_type_model.dart';

class ViewPaymentController extends GetxController {
  String ccCode = "";
  RxList<Payment> paymentList = <Payment>[].obs;

  RxList<PaymentType> paymentTypeList = <PaymentType>[].obs;
  RxList<Customer> customerList = <Customer>[].obs;
  RxList<Bank> bankList = <Bank>[].obs;

  String? pcCode = '';

  @override
  void onInit() async {
    super.onInit();

    pcCode =
        await SharedPrefManager.instance.getString(ConstantValues.customerCode);
    EasyLoading.show(status: 'Loading...');
    await fetchPaymentType();
    await fetchBanks();
    await fetchPayments();
    await fetchCustomers();
  }


  Future<void> fetchPayments() async {
    final db = await DatabaseProvider.database;
    print("fetched data from DB: ${db}");
    final List<Map<String, dynamic>> maps = await db.query(
        "${TableValues.tablePaymentCollection}",
        where: "${TableValues.pcCode} = ?",
        whereArgs: ["${pcCode}"]);

    paymentList.value = List.generate(maps.length, (index) {
      return Payment.fromMap(maps[index]);
    });
    print("paymentList View screen: ${paymentList}");
    EasyLoading.dismiss();
  }

  Future<void> fetchPaymentType() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tablePaymentType}");
    paymentTypeList.value = List.generate(maps.length, (index) {
      return PaymentType.fromMap(maps[index]);
    });
  }

  Future<void> fetchCustomers() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableCustomer}");
    print(maps);
    customerList.value = List.generate(maps.length, (index) {
      return Customer.fromMap(maps[index]);
    });
    await EasyLoading.dismiss();
  }

  Future<void> fetchBanks() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableBank}");
    print(maps);
    bankList.value = List.generate(maps.length, (index) {
      return Bank.fromMap(maps[index]);
    });
    print(bankList);
  }

 

  String? getCustomerNameFromTypeID(String selectedType) {
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmCode == selectedType,
        orElse: () => throw "No data found for this ID2",
      );
      return customerType.cmName;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerNameFromTypeID: $e');
      }
      return null;
    }
  }

  String? getBankNameFromTypeID(int selectedType) {
    try {
      final bank = bankList.firstWhere(
        (bank) => bank.bank_id == selectedType,
        orElse: () => throw "No data found for this ID2",
      );
      return bank.bank_name;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerNameFromTypeID: $e');
      }
      return null;
    }
  }

  String? getCustomerTypeFromCMCode(String selectedType) {
    try {
      final customerType = customerList.firstWhere(
        (ccType) => ccType.cmCode == selectedType,
        orElse: () => throw "No data found for this ID3",
      );
      String custTypeString = "";
      if (customerType.cmType == 1) {
        custTypeString = "Company";
      } else if (customerType.cmType == 2) {
        custTypeString = "SS";
      } else if (customerType.cmType == 3) {
        custTypeString = "Distributor";
      } else if (customerType.cmType == 4) {
        custTypeString = "Dealer";
      } else if (customerType.cmType == 5) {
        custTypeString = "Retailer";
      }
      return custTypeString;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerNameFromTypeID: $e');
      }
      return null;
    }
  }

// 1-company, 2-ss, 3-dist, 4-dealer, 5-retailer

  String getClientType(int clientType) {
    String clientTypeText;

    if (clientType == 1) {
      clientTypeText = "Company";
    } else if (clientType == 2) {
      clientTypeText = "SS";
    } else if (clientType == 3) {
      clientTypeText = "Distributor";
    } else if (clientType == 4) {
      clientTypeText = "Dealer";
    } else {
      clientTypeText = "Retailer";
    }
    return clientTypeText;
  }

  String formatDate(String dateString) {
    // Parse the input date string
    DateTime dateTime = DateTime.parse(dateString);

    // Define the date format you want
    DateFormat formatter = DateFormat('dd MMM yyyy');

    // Format the date using the defined format
    String formattedDate = formatter.format(dateTime);

    return formattedDate;
  }
}
