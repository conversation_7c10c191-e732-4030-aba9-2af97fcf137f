// ignore_for_file: unused_local_variable

import 'dart:async';
import 'dart:convert';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/add_leave_screen/models/add_leave_model.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/presentation/leave_screen/controller/leave_controller.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/leave_request_model.dart';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';



class AddLeaveController extends GetxController {
  TextEditingController dateController = TextEditingController();
  TextEditingController fromDateController = TextEditingController();
  TextEditingController toDateController = TextEditingController();
  TextEditingController reasonController = TextEditingController();

  Rx<AddLeaveModel> addLeaveModelObj = AddLeaveModel().obs;

  SelectionPopupModel? selectedDropDownValue;

  RxBool isFullLeave = false.obs;

  RxInt leaveTypeID = 0.obs;

  final selectedFromDate = Rx<DateTime?>(null);
  final selectedToDate = Rx<DateTime?>(null);
  RxInt totalDays = RxInt(0);

  RxString employeeCode = ''.obs;

  RxString employeeToken = "".obs;

  String strFromDate = "";
  String strToDate = "";

  List<String> leaveType = [
    'Full',
    'Half',
  ];

  @override
  void onInit() async {
    super.onInit();

    await fetchEmployeeData();

    await fetchedToken();
  }

  @override
  void onClose() {
    super.onClose();
    fromDateController.dispose();
    toDateController.dispose();
    reasonController.dispose();
  }

  onSelected(dynamic value) {
    for (var element in addLeaveModelObj.value.dropdownItemList.value) {
      element.isSelected = false;
      if (element.id == value.id) {
        element.isSelected = true;
      }
    }
    addLeaveModelObj.value.dropdownItemList.refresh();
  }

  Future<void> selectFromDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (pickedDate != null) {
      print("pickedDate: ${pickedDate}");
      selectedFromDate.value = pickedDate;
    }

    if (selectedFromDate.value != null && selectedToDate.value != null) {
      calculateTotalDays();
    }
  }

  String formatDate(DateTime date) {
    String formattedDate = DateFormat('dd MMM yyyy').format(date);
    return formattedDate;
    //"${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  String formatDateToDB(DateTime date) {
    String formattedDate = DateFormat('yyyy-MM-dd').format(date);
    return formattedDate;
    //"${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  void selectToDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (pickedDate != null) {
      print("pickedDate: ${pickedDate}");
      selectedToDate.value = pickedDate;
    }

    if (selectedFromDate.value != null && selectedToDate.value != null) {
      calculateTotalDays();
    }
  }

  void calculateTotalDays() {
    if (selectedFromDate.value != null && selectedToDate.value != null) {
      Duration difference =
          selectedToDate.value!.difference(selectedFromDate.value!);
      totalDays.value = difference.inDays + 1;
      print("totalDays.value : ${totalDays.value}");
    } else {
      totalDays.value = 0;
    }
  }

  String generateUUID() {
    var uuid = Uuid();
    return uuid.v4();
  }

  Future<String?> fetchEmployeeData() async {
    final String? employeeDataJson =
        await SharedPrefManager.instance.getString(ConstantValues.employee) ??
            "";
    if (employeeDataJson != null) {
      final Map<String, dynamic> employeeData = json.decode(employeeDataJson);
      final String? empCode = employeeData['emp_code'];
      employeeCode.value = empCode!;
      return empCode;
    } else {
      throw Exception('Employee data not found in SharedPreferences');
    }
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  Future<void> addLeaveToDB() async {
    final db = await DatabaseProvider.database;

    String formattedDateTime =
        DateFormat("yyyy-MM-ddTHH:mm:ss.000000'Z'").format(DateTime.now());
    print(formattedDateTime);

    DateTime now = DateTime.now();
    String dateString = DateFormat('yyyy-MM-dd HH:mm:ss').format(now);
    print("dateString: ${dateString}");

    if (selectedFromDate.value != null) {
      strFromDate = formatDateToDB(selectedFromDate.value!);
    }

    if (selectedToDate.value != null) {
      strToDate = formatDateToDB(selectedToDate.value!);
    }

    String uuid = generateUUID();
    var leaveRequest = Leave(
      code: uuid,
      typeId: leaveTypeID.value,
      fromDate: strFromDate,
      toDate: strToDate,
      totalDays: totalDays.value,
      approvedStatus: 1,
      reason: reasonController.text,
      employeeCode: employeeCode.value,
      notificationStatus: 0,
      approvedBy: null,
      createdAt: dateString,
      syncStatus: 0,
    );

    try {
      await db.insert(
        '${TableValues.tableLeaveRequest}',
        leaveRequest.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      final expense = Get.find<LeaveController>();
      await expense.checkInternetConnectivity();

      showToastMessage("Leave added successfully");

      Get.back();
    } catch (e) {
      print("Error adding leave to database: $e");

      // Log the error to a log file
      await ApiLogger.logError(
        apiName: 'addLeaveToDB',
        apiRequestData: leaveRequest.toMap(),
        apiResponse: 'Error: $e',
      );

      showToastMessage("Failed to add leave. Please try again.");
    }
  }
}
