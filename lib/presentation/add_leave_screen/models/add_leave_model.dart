import '../../../core/app_export.dart';

class AddLeaveModel {
  Rx<List<SelectionPopupModel>> dropdownItemList = Rx([
    SelectionPopupModel(
      id: 1,
      title: "Item One",
      isSelected: true,
    ),
    SelectionPopupModel(
      id: 2,
      title: "Item Two",
    ),
    SelectionPopupModel(
      id: 3,
      title: "Item Three",
    )
  ]);
}

class LeaveModel {
  final int type;
  final String reason;
  final DateTime date;

  LeaveModel({
    required this.type,
    required this.reason,
    required this.date,
  });
}
