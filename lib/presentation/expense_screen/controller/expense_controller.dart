// ignore_for_file: unused_catch_stack, invalid_use_of_protected_member, dead_code, unused_local_variable

import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sfm_new/data/api_logger.dart';
import 'package:intl/intl.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/expense_model.dart';
import 'package:sfm_new/sfmDatabase/models/expense_type_model.dart';
import 'package:dio/dio.dart';
import 'package:permission_handler/permission_handler.dart';

enum ExpenseStatus { all, pending, approved, rejected }

class ExpenseController extends GetxController {
  final searchText = ''.obs;

  RxList<Expense> expenseList = <Expense>[].obs;
  RxList<Expense> filteredExpenseList = <Expense>[].obs;
  RxList<ExpenseType> expenseTypeList = <ExpenseType>[].obs;

  RxSet<ExpenseStatus> currentExpenseStatuses =
      <ExpenseStatus>{ExpenseStatus.all}.obs;

  var selectedOptions = <String>[].obs;

  late Rx<DateTime> fromDate;
  late Rx<DateTime> toDate;

  RxString formattedFromDate = ''.obs;
  RxString formattedToDate = ''.obs;

  RxString filterFromDate = ''.obs;
  RxString filterToDate = ''.obs;

  RxString selectedExpenseCode = ''.obs;

  static ExpenseController get instance => Get.find();

  final Connectivity _connectivity = Connectivity();

  RxString employeeToken = "".obs;

  RxDouble totalPendingAmount = 0.0.obs;
  RxDouble totalApprovedAmount = 0.0.obs;

  bool isExpenseAPISuccess = false;

  var isLoading = true.obs;

  RxBool _downloading = false.obs;
  RxString _filePath = ''.obs;

  bool get downloading => _downloading.value;
  String get filePath => _filePath.value;

  RxBool storagePermissionStatus = false.obs;

  @override
  void onInit() async {
    super.onInit();

    fromDate = DateTime.now().obs;
    toDate = (DateTime.now().add(Duration(days: 0))).obs;

    formattedFromDate.value = formatDateTime(fromDate.toString());
    formattedToDate.value = formatDateTime(toDate.toString());

    await fetchedToken();
    await fetchExpenseType();
    await fetchExpenses();
    await checkInternetConnectivity();
  }

  Future<void> fetchExpenses() async {
    isLoading.value = true;
    final db = await DatabaseProvider.database;
    print("fetched data from DB: ${db}");
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableExpense}");
    expenseList.value = List.generate(maps.length, (index) {
      return Expense.fromMap(maps[index]);
    });
    print("expenseList: ${expenseList}");

    filteredExpenseList.value = expenseList.value
        .where((expense) => expense.expenseDetail!
            .toLowerCase()
            .contains(searchText.value.toLowerCase()))
        .toList();

    filteredExpenseList.value.sort((a, b) => b.date!.compareTo(a.date!));

    await fetchAmounts();
  }

  Future<void> requestStoragePermission() async {
    final DeviceInfoPlugin info =
        DeviceInfoPlugin(); 
    final AndroidDeviceInfo androidInfo = await info.androidInfo;
    print('releaseVersion : ${androidInfo.version.release}');
    final String androidVersion = "${androidInfo.version.release}";
    print("androidVersion: ${androidVersion}");
    bool havePermission = false;

    int comparison = compareAndroidVersions(
        androidVersion, "11"); 
    print("comparison: ${comparison}");

    if (comparison > 0) {
      final request = await [
        Permission.manageExternalStorage,
      ].request();

      havePermission =
          request.values.every((status) => status == PermissionStatus.granted);
      print("havePermission: ${havePermission}");
      storagePermissionStatus.value = havePermission;
    } else {
      final status = await Permission.storage.request();
      print("else status: ${status}");
      havePermission = status.isGranted;
      print("havePermission: ${havePermission}");
      storagePermissionStatus.value = havePermission;
      print("storagePermissionStatus: ${storagePermissionStatus}");
    }

    if (!havePermission) {
      Get.dialog(
        AlertDialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.transparent,
          title: Text(
            'Permission Denied',
            style: CustomTextStyles.titleMediumPrimary.copyWith(
              color: theme.colorScheme.primary,
            ),
          ),
          content: Text('Please allow permission to create log file'),
          actions: [
            TextButton(
              onPressed: () async {
                await openAppSettings();
                Get.back();
              },
              child: Text(
                'Open Settings',
                style: CustomTextStyles.bodyMediumPoppinsBluegray900.copyWith(
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  int compareAndroidVersions(String version1, String version2) {
    // Split version strings into their components
    List<String> components1 = version1.split('.');
    List<String> components2 = version2.split('.');

    // Parse major version numbers
    int majorVersion1 = int.parse(components1[0]);
    int majorVersion2 = int.parse(components2[0]);

    // Assume minor and maintenance release versions are zero for single-number version
    int minorVersion1 = components1.length > 1 ? int.parse(components1[1]) : 0;
    int minorVersion2 = components2.length > 1 ? int.parse(components2[1]) : 0;
    int maintenanceVersion1 =
        components1.length > 2 ? int.parse(components1[2]) : 0;
    int maintenanceVersion2 =
        components2.length > 2 ? int.parse(components2[2]) : 0;

    // Compare major version numbers
    if (majorVersion1 < majorVersion2) {
      return -1;
    } else if (majorVersion1 > majorVersion2) {
      return 1;
    }

    // If major version numbers are equal, compare minor version numbers
    if (minorVersion1 < minorVersion2) {
      return -1;
    } else if (minorVersion1 > minorVersion2) {
      return 1;
    }

    // If minor version numbers are equal, compare maintenance release version numbers
    if (maintenanceVersion1 < maintenanceVersion2) {
      return -1;
    } else if (maintenanceVersion1 > maintenanceVersion2) {
      return 1;
    }

    // If all version numbers are equal, versions are the same
    return 0;
  }

  Future<void> fetchFilteredExpenses() async {
    filteredExpenseList.value = expenseList.value
        .where((expense) =>
            '${getExpenseTypeNameFromID(expense.expenseType ?? 0)}'
                .toLowerCase()
                .contains(searchText.value.toLowerCase()))
        .toList();


    filteredExpenseList.value.sort((a, b) => b.date!.compareTo(a.date!));

    await fetchAmounts();
  }

  void updateSearchText(String newText) {
    searchText.value = newText;
    fetchFilteredExpenses(); 
  }

  Future<void> fetchExpenseType() async {
    final db = await DatabaseProvider.database;
    print("fetched data from DB: ${db}");
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableExpenseType}");
    expenseTypeList.value = List.generate(maps.length, (index) {
      return ExpenseType.fromMap(maps[index]);
    });
  }

  Future<void> fetchAmounts() async {
    totalPendingAmount.value = 0.0;
    totalApprovedAmount.value = 0.0;

    for (final expense in filteredExpenseList) {
      if (expense.expenseStatus == 1) {
        totalApprovedAmount.value += expense.amount;
      } else {
        totalPendingAmount.value += expense.amount;
      }
    }
    isLoading.value = false;
  }

  Future<bool> checkInternetConnectivity() async {
    var connectivityResult = await _connectivity.checkConnectivity();
    if (connectivityResult != ConnectivityResult.none) {
      syncExpensesWithServer();
    }
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> syncExpensesWithServer() async {
    // EasyLoading.show(status: 'Loading...');

    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> unsyncedExpenses = await db.query(
        '${TableValues.tableExpense}',
        where: '${TableValues.expenseSyncStatus} = ?',
        whereArgs: [0]);

    if (unsyncedExpenses.isNotEmpty) {
      print('Syncing expenses with the server...');
      print("unsyncedExpenses: ${unsyncedExpenses}");
      for (final expense in unsyncedExpenses) {
        // final Map<String, dynamic> expenseData;
        Expense expenseData = Expense.fromMap(expense);

        print("expenseData: ${expenseData}");

        if (expenseData.billPicture!.isEmpty) {
          expenseData.billPicture = "Picture not added";
        } else {
          expenseData.billPicture = "Picture added";
        }

        print("expenseData: ${expenseData}");

        try {
          await addExpenseAPI(expense);

          if (isExpenseAPISuccess == true) {
            await db.update('${TableValues.tableExpense}',
                {'${TableValues.expenseSyncStatus}': 1},
                where: '${TableValues.expenseCode} = ?',
                whereArgs: [expense['${TableValues.expenseCode}']]);
            print(
                'Expense with ID ${expense['${TableValues.expenseCode}']} synced successfully');

            await ApiLogger.logError(
              apiName: "Add expense sync",
              apiRequestData: expenseData.toMap(),
              apiResponse:
                  'Expense with ID ${expense['${TableValues.expenseCode}']} synced successfully',
            );
          } else {
            print(
                'Error syncing expense with ID ${expense['${TableValues.expenseCode}']}');

            await ApiLogger.logError(
              apiName: "Add expense sync",
              apiRequestData: expenseData.toMap(),
              apiResponse:
                  'Error syncing expense with ID ${expense['${TableValues.expenseCode}']}',
            );
          }
        } catch (error) {
          print(
              'Error syncing expense with ID ${expense['${TableValues.expenseCode}']}: $error');

          await ApiLogger.logError(
            apiName: "Add expense sync with error",
            apiRequestData: expenseData.toMap(),
            apiResponse:
                'Error syncing expense with ID ${expense['${TableValues.expenseCode}']}: $error',
          );
        }
      }
      await fetchExpenses();
      EasyLoading.dismiss();
    } else {
      print('No expenses to sync.');
      EasyLoading.dismiss();
    }
  }

  Future<void> addExpenseAPI(Map<String, dynamic> expense) async {
    try {
      Map<String, dynamic> data = {};

      data = {
        "em_expense_type": "${expense['em_expense_type']}",
        "em_code": "${expense['em_code']}",
        "em_expense_detail": "${expense['em_expense_detail']}",
        "em_amount": "${expense['em_amount']}",
        "em_approved_amount": "${expense['em_approved_amount']}",
        "em_bill_picture": "${expense['em_bill_picture']}",
        "file_extension": "${expense['file_extension']}",
        "latitude": "${expense['em_lat']}",
        "longitude": "${expense['em_long']}",
        "emp_code": "${expense['emp_code']}",
        "em_expense_status": "${expense['em_expense_status']}",
        "em_date": "${expense['em_date']}",
        "em_km": "${expense['em_km']}",
      };

      print("data: ${data}");

      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${employeeToken.value}',
      };

      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.postAddExpenseEndpoint),
        data: expense,
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.data);

      if (response.data != null && response.statusCode == 200) {
        isExpenseAPISuccess = true;
        EasyLoading.dismiss();
      } else {
        isExpenseAPISuccess = false;
        print('Response data is null');
        EasyLoading.dismiss();
      }
    } on DioException catch (e) {
      isExpenseAPISuccess = false;
      String errorMessage;

      if (e.response != null) {
        errorMessage =
            'Error ${e.response!.statusCode}: ${e.response!.statusMessage}';
        print(errorMessage);
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
        }
      } else {
        errorMessage = 'Network error: ${e.message}';
        print(errorMessage);
      }

      await ApiLogger.logError(
        apiName: ApiClient.postAddExpenseEndpoint,
        apiRequestData: expense,
        apiResponse: errorMessage,
      );
      EasyLoading.dismiss();
    } on TimeoutException catch (e) {
      // Handle timeout exception
      print('Request timed out: $e');
      EasyLoading.dismiss();

      await ApiLogger.logError(
        apiName: ApiClient.postAddExpenseEndpoint,
        apiRequestData: expense,
        apiResponse: "TimeoutException: $e",
      );
    } finally {
      EasyLoading.dismiss();
    }
  }

  Future<void> fetchedToken() async {
    employeeToken.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
  }

  String formatDate(String dateString) {
    if (dateString.isNotEmpty) {
      DateTime dateTime = DateTime.parse(dateString);
      String formattedDate = DateFormat('dd MMM yyyy').format(dateTime);
      return formattedDate;
    } else {
      return "";
    }
  }

  String formatFilterDateTime(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(dateTime);
  }

  void toggleOption(String option) {
    if (selectedOptions.contains(option)) {
      selectedOptions.remove(option);
    } else {
      selectedOptions.add(option);
    }
  }

  String formatDateTime(String dateTimeString) {
    DateTime dateTime = DateTime.parse(dateTimeString);
    final DateFormat formatter = DateFormat('dd MMM');
    return formatter.format(dateTime);
  }

  Future<void> selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isFromDate ? fromDate.value : toDate.value,
      firstDate: DateTime(2015, 8),
      lastDate: DateTime(2101),
    );
    if (picked != null) {
      if (isFromDate) {
        fromDate.value = picked;
      } else {
        toDate.value = picked;
      }
    }
  }

  String? getExpenseTypeNameFromID(int selectedID) {
    try {
      final expenseData = expenseTypeList.firstWhere(
        (exType) => exType.id == selectedID,
      );
      return expenseData.expenseName ?? "";
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Error in getClaimComplainIdFromTypeName: $e');
      }
      return null;
    }
  }

  void applyDateRange() {
    print('Selected Date Range: ${fromDate.value} to ${toDate.value}');
    Get.back();
  }

  void filterExpense(String fromDateStr, String toDateStr,
      Set<ExpenseStatus> expenseStatuses) {
    filteredExpenseList.clear();

    DateTime fromDate = DateTime.tryParse(fromDateStr) ?? DateTime.now();
    print("filterExpense fromDate: ${fromDate}");
    DateTime toDate = DateTime.tryParse(toDateStr) ?? DateTime.now();
    toDate = DateTime(toDate.year, toDate.month, toDate.day, 23, 59, 59);
    print("filterExpense toDate: ${toDate}");
    bool isWithinDateRange = false;
    bool isDateSelected = false;

    List<Expense> filteredExpenses = expenseList.where((expense) {
      if (!fromDateStr.isEmpty || !toDateStr.isEmpty) {
        isDateSelected = true;

        DateTime expenseDate = DateTime.parse(expense.date ?? "");
        print("expenseDate: ${expenseDate}");

        DateTime expenseDateOnly =
            DateTime(expenseDate.year, expenseDate.month, expenseDate.day);
        DateTime fromDateOnly =
            DateTime(fromDate.year, fromDate.month, fromDate.day);
        DateTime toDateOnly = DateTime(toDate.year, toDate.month, toDate.day);

        isWithinDateRange = expenseDateOnly.isAtSameMomentAs(fromDateOnly) ||
            expenseDateOnly.isAtSameMomentAs(toDateOnly) ||
            (expenseDateOnly.isAfter(fromDateOnly) &&
                expenseDateOnly.isBefore(toDateOnly));
      } else {
        isDateSelected = false;
      }

      bool matchesComplainStatus =
          expenseStatuses.contains(ExpenseStatus.all) ||
              (expenseStatuses.contains(ExpenseStatus.pending) &&
                  expense.expenseStatus == 0) ||
              (expenseStatuses.contains(ExpenseStatus.approved) &&
                  expense.expenseStatus == 1) ||
              (expenseStatuses.contains(ExpenseStatus.rejected) &&
                  expense.expenseStatus == 3);

      // Debugging information
      print("Expense Code: ${expense.expenseCode}");
      print("isWithinDateRange: $isWithinDateRange");
      print("matchesComplainStatus: $matchesComplainStatus");

      if (isDateSelected) {
        if (isWithinDateRange && (matchesComplainStatus)) {
          print("data added in filteredComplainList");
          filteredExpenseList.add(expense);
        }
      } else {
        if (isWithinDateRange || (matchesComplainStatus)) {
          print("data added in filteredComplainList");
          filteredExpenseList.add(expense);
        }
      }

      if (filteredExpenseList.length > 0) {
        filteredExpenseList.value.sort((a, b) => b.date!.compareTo(a.date!));
      }

      fetchAmounts();
      return isWithinDateRange && matchesComplainStatus;
    }).toList();
  }
}

class DateRangeDialog extends StatelessWidget {
  final ExpenseController controller = Get.put(ExpenseController());

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Select Date Range'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Row(
            children: <Widget>[
              Text('From:'),
              TextButton(
                onPressed: () => controller.selectDate(context, true),
                child: Obx(() => Text(DateFormat('yyyy-MM-dd')
                    .format(controller.fromDate.value))),
              ),
            ],
          ),
          Row(
            children: <Widget>[
              Text('To:'),
              TextButton(
                onPressed: () => controller.selectDate(context, false),
                child: Obx(() => Text(
                    DateFormat('yyyy-MM-dd').format(controller.toDate.value))),
              ),
            ],
          ),
        ],
      ),
      actions: <Widget>[
        TextButton(
          onPressed: () {
            Get.back(); 
          },
          child: SizedBox(
            width: MediaQuery.of(context).size.width *
                0.3, 
            height: 50, 
            child: Center(child: Text('Cancel')),
          ),
        ),
        ElevatedButton(
          onPressed: () => controller.applyDateRange(),
          child: SizedBox(
            width: MediaQuery.of(context).size.width *
                0.3, 
            height: 50, 
            child: Center(child: Text('Apply')),
          ),
        ),
      ],
    );
  }
}
