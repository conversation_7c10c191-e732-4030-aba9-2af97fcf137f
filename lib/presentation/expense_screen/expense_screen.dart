// ignore_for_file: invalid_use_of_protected_member, invalid_use_of_protected_member,duplicate_ignore

import 'package:sfm_new/presentation/expense_filter_screen/expense_filter_screen.dart';
import 'package:sfm_new/sfmDatabase/models/expense_model.dart';

import '../../data/filter_option.dart';
import 'controller/expense_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';
import 'package:sfm_new/widgets/custom_icon_button.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';

// ignore_for_file: must_be_immutable
class ExpenseScreen extends GetWidget<ExpenseController> {
  ExpenseFilterScreen expenseFilterController = Get.put(ExpenseFilterScreen());

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(context),
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(horizontal: 0.h),
          child: Column(
            children: [
              SizedBox(height: 15.v),
              CustomSearchBar(),
              SizedBox(height: 8.v),
              _buildExpenseList(),
              SizedBox(height: 12.v),
              _buildBottomDetails(),
            ],
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(left: 8.h, top: 18.v, bottom: 18.v),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "lbl_expense".tr,
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildExpenseList() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 4.h),
        child: Obx(
          () {
            if (controller.isLoading.value) {
              return Center(
                child: CircularProgressIndicator(),
              );
            } else if (controller.filteredExpenseList.isEmpty) {
              return Center(
                child: Text(
                  'No expense found',
                  style: TextStyle(fontSize: 16),
                ),
              );
            } else {
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.h),
                child: ListView.separated(
                  itemCount: controller.filteredExpenseList.length,
                  physics: BouncingScrollPhysics(),
                  shrinkWrap: true,
                  separatorBuilder: (
                    context,
                    index,
                  ) {
                    return SizedBox(
                      height: 10.v,
                    );
                  },
                  itemBuilder: (context, index) => CustomExpenseListItem(
                    expense: controller.filteredExpenseList[index],
                  ),
                ),
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildBottomDetails() {
    return Obx(
      () => Container(
        color: Colors.transparent,
        height: 107.v,
        width: double.maxFinite,
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                height: 60.v,
                width: double.maxFinite,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(10.h),
                  ),
                ),
              ),
            ),
            Align(
              alignment: Alignment.center,
              child: Container(
                width: double.maxFinite,
                margin: EdgeInsets.only(top: 40.v),
                padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                //padding: EdgeInsets.only(top: 10, bottom: 0, left: 16),
                decoration: AppDecoration.outlineBlack9007,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Pending",
                          style: CustomTextStyles.titleSmallOnErrorContainerBold
                              .copyWith(
                            color: theme.colorScheme.onErrorContainer
                                .withValues(alpha: 1),
                          ),
                        ),
                        Text(
                          '₹${controller.totalPendingAmount.value}',
                          style: CustomTextStyles.titleSmallOnErrorContainerBold
                              .copyWith(
                            color: theme.colorScheme.onErrorContainer
                                .withValues(alpha: 1),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(width: 30),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Approved",
                          style: CustomTextStyles.titleSmallOnErrorContainerBold
                              .copyWith(
                            color: theme.colorScheme.onErrorContainer
                                .withValues(alpha: 1),
                          ),
                        ),
                        Text(
                          '₹${controller.totalApprovedAmount.value}',
                          style: CustomTextStyles.titleSmallOnErrorContainerBold
                              .copyWith(
                            color: theme.colorScheme.onErrorContainer
                                .withValues(alpha: 1),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            Align(
              alignment: Alignment.topRight,
              child: Container(
                height: 70.adaptSize,
                width: 70.adaptSize,
                margin: EdgeInsets.only(right: 18.h),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Align(
                      alignment: Alignment.center,
                      child: Container(
                        height: 70.adaptSize,
                        width: 70.adaptSize,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          borderRadius: BorderRadius.circular(
                            35.h,
                          ),
                          border: Border.all(
                            color: theme.colorScheme.onErrorContainer
                                .withValues(alpha: 1),
                            width: 6.h,
                          ),
                        ),
                      ),
                    ),
                    CustomIconButton(
                      height: 70.adaptSize,
                      width: 70.adaptSize,
                      padding: EdgeInsets.all(19.h),
                      decoration: IconButtonStyleHelper.outlineOnErrorContainer,
                      alignment: Alignment.center,
                      child: CustomImageView(
                        imagePath: ImageConstant.imgGroup29,
                      ),
                      onTap: () {
                        print("Plus button pressed");
                        Get.toNamed(AppRoutes.addExpenseScreen);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomSearchBar extends StatelessWidget {
  // DateRangeDialog controllerDateRange = Get.put(DateRangeDialog());
  ExpenseController controller = Get.put(ExpenseController());

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 12, right: 12, top: 4, bottom: 4),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.5),
              spreadRadius: 2,
              blurRadius: 7,
              offset: Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                style: CustomTextStyles.bodyMediumBluegray700,
                onChanged: (value) {
                  print('Search value: $value');
                  controller.updateSearchText(value);
                },
                decoration: InputDecoration(
                  hintText: 'Search Expense',
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 10.0),
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(10.0),
                  bottomRight: Radius.circular(10.0),
                ),
              ),
              child: IconButton(
                icon: Container(
                  child: CustomImageView(
                    svgPath: ImageConstant.imgFilterIcon,
                    height: 20.adaptSize,
                    width: 20.adaptSize,
                  ),
                ),
                onPressed: () {
                  print('Filter button pressed');
                  FocusManager.instance.primaryFocus?.unfocus();
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return CustomDialogFilter();
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomExpenseListItem extends StatelessWidget {
  final Expense expense;
  final ExpenseController controller = Get.put(ExpenseController());

  CustomExpenseListItem({
    required this.expense,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // controller.onNotificationTap(notification);
        // Get.offNamed(AppRoutes.viewExpenseDetailsScreen);
        ExpenseController.instance.selectedExpenseCode.value =
            "${expense.expenseCode}";
        print(
            "selectedExpenseCode: ${ExpenseController.instance.selectedExpenseCode.value}");
        Get.toNamed(AppRoutes.viewExpenseDetailsScreen);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 9.h,
          vertical: 6.v,
        ),
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 4.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text:
                              '${controller.getExpenseTypeNameFromID(expense.expenseType ?? 0)}', //"Expense Type : ",
                          style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                        ),
                      ],
                    ),
                    textAlign: TextAlign.left,
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 0.v, left: 8),
                    child: Text(
                      controller.formatDate(expense.date ?? ""),
                      style: CustomTextStyles.bodySmallOpenSansGray500,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 4.v),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  children: [
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: "Amount : ",
                            style: CustomTextStyles.labelLargeOpenSansff0b2e40,
                          ),
                          TextSpan(
                            text: '₹${expense.amount.toString()}',
                            style: CustomTextStyles.labelLargeOpenSansff8e8e8e,
                          ),
                        ],
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ],
                ),
                Spacer(),
                Row(
                  children: [
                    Visibility(
                      visible: expense.syncStatus == 0 ? true : false,
                      child: CustomImageView(
                        svgPath: ImageConstant.imgNotSynced,
                        color: Colors.red,
                        height: 15.adaptSize,
                        width: 15.adaptSize,
                        margin: EdgeInsets.only(bottom: 2.v),
                      ),
                    ),
                    SizedBox(width: 6),
                    CustomImageView(
                      svgPath: ImageConstant.imgArrowRight,
                      height: 15.adaptSize,
                      width: 15.adaptSize,
                      margin: EdgeInsets.only(bottom: 2.v),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 4.v),
            // 0-pending, 1-conform, 2-rejected, 3-partial approved
            Text(
              expense.expenseStatus == 0
                  ? "Pending"
                  : expense.expenseStatus == 1
                      ? "Approved"
                      : expense.expenseStatus == 2
                          ? "Rejected"
                          : "Partial Approved",
              style: CustomTextStyles.labelLargeOpenSansBluegray400.copyWith(
                  color: expense.expenseStatus == 0
                      ? Colors.orange
                      : expense.expenseStatus == 1
                          ? Colors.green
                          : expense.expenseStatus == 2
                              ? Colors.red
                              : Colors.blue),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomDialogFilter extends StatelessWidget {
  CustomDialogFilter({
    Key? key,
  }) : super(key: key);

  ExpenseController controller = Get.put(ExpenseController());

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      insetPadding: EdgeInsets.all(12),
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Filters",
              style: CustomTextStyles.titleLargeGray900,
            ),
            SizedBox(height: 8.v),
            filterExpenseByStatus(),
            SizedBox(height: 16.v),
            _buildDate(context),
            SizedBox(height: 16.v),
            Container(
              width: MediaQuery.of(context).size.width * 0.75,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CustomElevatedButton(
                    // height: 48.v,
                    width: MediaQuery.of(context).size.width * 0.35,
                    text: "lbl_cancel".tr,
                    buttonStyle: CustomButtonStyles.fillOnErrorContainer,
                    buttonTextStyle: CustomTextStyles.titleSmallPoppinsOnError,
                    onPressed: () {
                      Get.back();
                    },
                  ),
                  CustomElevatedButton(
                    // height: 48.v,
                    width: MediaQuery.of(context).size.width * 0.35,
                    text: "lbl_apply".tr,
                    margin: EdgeInsets.only(left: 16.h),
                    buttonStyle: CustomButtonStyles.fillPrimaryTL8,
                    buttonTextStyle:
                        CustomTextStyles.titleSmallPoppinsOnErrorContainer,
                    onPressed: () {
                      print(
                          "controller.filterFromDate.value: ${controller.filterFromDate.value}");
                      print(
                          "controller.filterToDate.value: ${controller.filterToDate.value}");
                      print(
                          "controller.currentExpenseStatuses.value: ${controller.currentExpenseStatuses.value}");
                      controller.filterExpense(
                          controller.filterFromDate.value,
                          controller.filterToDate.value,
                          controller.currentExpenseStatuses.value);
                      Get.back();
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget filterExpenseByStatus() {
    return Obx(
      () => Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          FilterOption(
            text: "All",
            isSelected:
                controller.currentExpenseStatuses.contains(ExpenseStatus.all)
                    ? true
                    : false,
            onTap: () {
              if (controller.currentExpenseStatuses
                  .contains(ExpenseStatus.all)) {
                controller.currentExpenseStatuses.remove(ExpenseStatus.all);
              } else {
                controller.currentExpenseStatuses.add(ExpenseStatus.all);
              }

              if (controller.currentExpenseStatuses
                  .contains(ExpenseStatus.pending)) {
                controller.currentExpenseStatuses.remove(ExpenseStatus.pending);
              }

              if (controller.currentExpenseStatuses
                  .contains(ExpenseStatus.approved)) {
                controller.currentExpenseStatuses
                    .remove(ExpenseStatus.approved);
              }

              if (controller.currentExpenseStatuses
                  .contains(ExpenseStatus.rejected)) {
                controller.currentExpenseStatuses
                    .remove(ExpenseStatus.rejected);
              }

              print(
                  "controller.currentExpenseStatuses: ${controller.currentExpenseStatuses}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Pending",
            isSelected: controller.currentExpenseStatuses
                    .contains(ExpenseStatus.pending)
                ? true
                : false,
            onTap: () {
              if (controller.currentExpenseStatuses
                  .contains(ExpenseStatus.pending)) {
                controller.currentExpenseStatuses.remove(ExpenseStatus.pending);
              } else {
                controller.currentExpenseStatuses.add(ExpenseStatus.pending);
              }

              if (controller.currentExpenseStatuses
                  .contains(ExpenseStatus.all)) {
                controller.currentExpenseStatuses.remove(ExpenseStatus.all);
              }

              print(
                  "controller.currentExpenseStatuses: ${controller.currentExpenseStatuses}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Approved",
            isSelected: controller.currentExpenseStatuses
                    .contains(ExpenseStatus.approved)
                ? true
                : false,
            onTap: () {
              if (controller.currentExpenseStatuses
                  .contains(ExpenseStatus.approved)) {
                controller.currentExpenseStatuses
                    .remove(ExpenseStatus.approved);
              } else {
                controller.currentExpenseStatuses.add(ExpenseStatus.approved);
              }

              if (controller.currentExpenseStatuses
                  .contains(ExpenseStatus.all)) {
                controller.currentExpenseStatuses.remove(ExpenseStatus.all);
              }

              print(
                  "controller.currentExpenseStatuses: ${controller.currentExpenseStatuses}");
            },
          ),
          SizedBox(width: 8),
          FilterOption(
            text: "Rejected",
            isSelected: controller.currentExpenseStatuses
                    .contains(ExpenseStatus.rejected)
                ? true
                : false,
            onTap: () {
              if (controller.currentExpenseStatuses
                  .contains(ExpenseStatus.rejected)) {
                controller.currentExpenseStatuses
                    .remove(ExpenseStatus.rejected);
              } else {
                controller.currentExpenseStatuses.add(ExpenseStatus.rejected);
              }

              if (controller.currentExpenseStatuses
                  .contains(ExpenseStatus.all)) {
                controller.currentExpenseStatuses.remove(ExpenseStatus.all);
              }

              print(
                  "controller.currentExpenseStatuses: ${controller.currentExpenseStatuses}");
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDate(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "lbl_date".tr,
          style: CustomTextStyles.titleLargeGray900,
        ),
        SizedBox(height: 9.v),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                  builder: (context, child) {
                    return Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: ColorScheme.light(
                          primary: theme.colorScheme.primary,
                        ),
                      ),
                      child: child!,
                    );
                  },
                ).then((value) {
                  if (value != null) {
                    print("fromdate value123: $value");
                    String dateValue = value.toString();
                    controller.formattedFromDate.value =
                        controller.formatDateTime(dateValue);
                    controller.filterFromDate.value =
                        controller.formatFilterDateTime(dateValue);
                    print(controller.formattedFromDate.value);
                    controller.fromDate.value = value;
                  }
                });
              },
              child: Obx(
                () => Container(
                  width: 115.h,
                  decoration: AppDecoration.outlineBlack90013,
                  child: Row(
                    children: [
                      _buildDateInputColored(
                        dateText: controller.formattedFromDate.value,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 8.h,
                right: 8.h,
                // top: 11.v,
                // bottom: 7.v,
              ),
              child: Text(
                "to",
                style: CustomTextStyles.bodyMediumOpenSansBlack900,
              ),
            ),
            GestureDetector(
              onTap: () {
                showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                  builder: (context, child) {
                    return Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: ColorScheme.light(
                          primary: theme.colorScheme.primary,
                        ),
                      ),
                      child: child!,
                    );
                  },
                ).then((value) {
                  if (value != null) {
                    print("todate value456: $value");
                    String dateValue = value.toString();
                    controller.formattedToDate.value =
                        controller.formatDateTime(dateValue);
                    controller.filterToDate.value =
                        controller.formatFilterDateTime(dateValue);
                    print(controller.formattedToDate.value);
                    controller.toDate.value = value;
                  }
                });
              },
              child: Obx(
                () => Container(
                  width: 115.h,
                  decoration: AppDecoration.outlineBlack90013,
                  child: Row(
                    children: [
                      _buildDateInputColored(
                        dateText: controller.formattedToDate.value,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateInputColored({required String dateText}) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 14.h,
        vertical: 9.v,
      ),
      decoration: AppDecoration.outlineBlack90014.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder10,
      ),
      child: Row(
        children: [
          CustomImageView(
            svgPath: ImageConstant.imgCalendarPrimary,
            height: 13.v,
            width: 12.h,
            margin: EdgeInsets.symmetric(vertical: 3.v),
          ),
          Padding(
            padding: EdgeInsets.only(
              left: 10.h,
              top: 2.v,
            ),
            child: Text(
              dateText,
              style: CustomTextStyles.bodySmallOpenSansGray700.copyWith(
                color: appTheme.gray700,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
