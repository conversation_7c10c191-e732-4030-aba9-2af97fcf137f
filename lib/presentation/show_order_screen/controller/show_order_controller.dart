import 'package:flutter/foundation.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/product_screen/controller/product_controller.dart';
import 'package:sfm_new/presentation/product_screen/models/product_model.dart';
import 'package:sfm_new/presentation/show_order_screen/models/show_order_model.dart';
import 'package:sfm_new/presentation/super_stockist_screen/controller/super_stockist_controller.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';
import 'package:sfm_new/sfmDatabase/models/product_type_model.dart';


class ShowOrderController extends GetxController {
  Rx<ShowOrderModel> showOrderModelObj = ShowOrderModel().obs;

  final ProductController cartController = Get.find<ProductController>();
  RxList<ProductType> productTypeList = <ProductType>[].obs;

  RxList<AddProduct> confirmCartItems = <AddProduct>[].obs;

  final custTypeController = Get.find<SuperStockistController>();

  RxInt selectedCustType = 0.obs;

  @override
  void onInit() async {
    super.onInit();

    selectedCustType.value = await SharedPrefManager.instance
            .getInt(ConstantValues.selectedCustType) ??
        0;
    print("selectedCustType: ${selectedCustType}");

    await fetchProductType();
  }

  Future<void> fetchProductType() async {
    final db = await DatabaseProvider.database;
    final List<Map<String, dynamic>> maps =
        await db.query("${TableValues.tableProductType}");
    productTypeList.value = List.generate(maps.length, (index) {
      return ProductType.fromMap(maps[index]);
    });
    print("productTypeList: ${productTypeList}");
  }

  String? fetchTypeName(int productTypeId) {
    try {
      final productType = productTypeList.firstWhere(
        (ccType) => ccType.type_id == productTypeId,
      );
      return productType.type_name;
    } catch (e) {
      if (kDebugMode) {
        print('Error in getCustomerCodeFromTypeName: $e');
      }
      return null;
    }
  }
}
