import 'package:sfm_new/presentation/product_screen/models/product_model.dart';
import 'controller/show_order_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/widgets/app_bar/appbar_leading_image.dart';
import 'package:sfm_new/widgets/app_bar/appbar_subtitle_four.dart';
import 'package:sfm_new/widgets/app_bar/custom_app_bar.dart';

// ignore_for_file: must_be_immutable
class ShowOrderScreen extends GetWidget<ShowOrderController> {
  const ShowOrderScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(),
        body: SizedBox(
          width: double.maxFinite,
          child: Column(
            children: [
              SizedBox(height: 16.v),
              Obx(
                () => Expanded(
                  child: ListView.builder(
                    physics: ClampingScrollPhysics(),
                    itemCount: controller.cartController.cartList.length,
                    itemBuilder: (context, index) {
                      AddProduct product =
                          controller.cartController.cartList[index];
                      return CustomProductListItem(
                        product: product,
                      );
                    },
                  ),
                ),
              ),
              SizedBox(height: 10.v),
            ],
          ),
        ),
        bottomNavigationBar: _buildVIEWCART(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      height: 49.v,
      leadingWidth: 45.h,
      leading: AppbarLeadingImage(
        svgPath: ImageConstant.imgArrowDown,
        margin: EdgeInsets.only(
          left: 8.h,
          top: 18.v,
          bottom: 18.v,
        ),
        onTap: () {
          Get.back();
        },
      ),
      centerTitle: true,
      title: AppbarSubtitleFour(
        text: "lbl_show_order".tr,
      ),
      styleType: Style.bgShadow,
    );
  }

  Widget _buildVIEWCART() {
    return Obx(
      () => Container(
        height: 70,
        margin: EdgeInsets.only(left: 0.h, right: 0.h, bottom: 0.v, top: 0.v),
        decoration: AppDecoration.outlineBlack9007.copyWith(
          borderRadius: BorderRadiusStyle.customBorderTL10,
        ),
        child: Padding(
          padding: const EdgeInsets.only(
            left: 12.0,
            right: 12.0,
            bottom: 8.0,
            top: 8.0,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "lbl_grand_total".tr,
                    style: CustomTextStyles.labelLargeOpenSansOnErrorContainer,
                  ),
                  SizedBox(height: 1.v),
                  Row(
                    children: [
                      Text(
                        "₹ ${controller.cartController.totalAmount.value.toStringAsFixed(2)}",
                        style: CustomTextStyles.titleSmallOnErrorContainerBold,
                      ),
                    ],
                  ),
                ],
              ),
              Spacer(),
              GestureDetector(
                behavior: HitTestBehavior.translucent,
                onTap: () {
                  FocusManager.instance.primaryFocus?.unfocus();
                  Get.toNamed(AppRoutes.placeOrderScreen);
                },
                child: Container(
                  height: 50.0,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25.0),
                  ),
                  padding: EdgeInsets.only(
                    left: 8.h,
                    right: 8.h,
                    top: 4.h,
                    bottom: 4.h,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomImageView(
                        imagePath: ImageConstant.imgConfirmation1,
                        height: 20.adaptSize,
                        width: 20.adaptSize,
                        color: theme.colorScheme.primary,
                      ),
                      SizedBox(
                        width: 4.h,
                      ),
                      Text(
                        "lbl_confirm_order".tr,
                        style: CustomTextStyles.titleSmallPrimaryBold,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomProductListItem extends StatelessWidget {
  final AddProduct product;
  final ShowOrderController controller = Get.put(ShowOrderController());

  CustomProductListItem({
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        margin: EdgeInsets.only(
          left: 12.h,
          right: 12.h,
          bottom: 8.v,
        ),
        padding: EdgeInsets.symmetric(vertical: 2.v),
        decoration: AppDecoration.outlineGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder10,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: EdgeInsets.only(left: 8.h),
                        child: Text(
                          product.productName!,
                          style: CustomTextStyles.titleMediumBluegray900Bold18,
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: EdgeInsets.only(left: 8.h),
                        child: Text(
                          product.productType ?? "",
                          style: CustomTextStyles.titleMediumGray60002Bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: EdgeInsets.only(left: 4.0, right: 8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 4.h),
                          child: Table(
                            columnWidths: {
                              0: FixedColumnWidth(
                                  MediaQuery.of(context).size.width * 0.2),
                              1: FixedColumnWidth(10),
                              2: FlexColumnWidth(),
                            },
                            children: [
                              _buildTableRow(
                                "lbl_unit_size".tr,
                                "${product.unitSize}",
                                CustomTextStyles.titleSmallBluegray900Bold_1,
                                CustomTextStyles.titleSmallGray60002Bold,
                              ),
                              _buildTableRow(
                                "MRP",
                                "${product.productMrp}",
                                CustomTextStyles.titleSmallBluegray900Bold_1,
                                CustomTextStyles.titleSmallGray60002Bold,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: EdgeInsets.only(left: 0.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 4.h),
                          child: Table(
                            columnWidths: {
                              0: FixedColumnWidth(
                                  MediaQuery.of(context).size.width * 0.2),
                              1: FixedColumnWidth(10),
                              2: FlexColumnWidth(),
                            },
                            children: [
                              _buildTableRow(
                                "Barcode",
                                "${product.barcode!}",
                                CustomTextStyles.titleSmallBluegray900Bold_1,
                                CustomTextStyles.titleSmallGray60002Bold,
                              ),
                              _buildTableRow(
                                controller.selectedCustType.value == 2
                                    ? "PTS"
                                    : controller.selectedCustType.value == 3
                                        ? "Price"
                                        : controller.selectedCustType.value == 4
                                            ? 'Price'
                                            : 'PTR',
                                controller.selectedCustType.value == 2
                                    ? '${product.productPtss}'
                                    : controller.selectedCustType.value == 3
                                        ? '${product.productPtdistributor}'
                                        : controller.selectedCustType.value == 4
                                            ? '${product.productPtdealer}'
                                            : '${product.productPtretailer}',
                                CustomTextStyles.titleSmallBluegray900Bold_1,
                                CustomTextStyles.titleSmallGray60002Bold,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.v),
            Container(
              height: 1,
              color: appTheme.black900.withValues(alpha: 0.11),
            ),
            SizedBox(height: 2.v),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (product.pcs!.value != 0)
                    Row(
                      children: [
                        Text(
                          "lbl_pcs".tr,
                          style: CustomTextStyles.titleSmallBluegray900Bold_1,
                        ),
                        SizedBox(width: 4.v),
                        Text(
                          ":",
                          style: CustomTextStyles.titleSmallBluegray900Bold_1,
                        ),
                        SizedBox(width: 4.v),
                        Container(
                          child: Text(
                            "${product.pcs!.value == 0 ? '0' : '${product.pcs!.value}'}",
                            style: CustomTextStyles.titleSmallGray60002Bold,
                          ),
                        ),
                      ],
                    ),
                  Spacer(),
                  if (product.bunch!.value != 0)
                    Row(
                      children: [
                        Text(
                          "lbl_bunch".tr,
                          style: CustomTextStyles.titleSmallBluegray900Bold_1,
                        ),
                        SizedBox(width: 4.v),
                        Text(
                          ":",
                          style: CustomTextStyles.titleSmallBluegray900Bold_1,
                        ),
                        SizedBox(width: 4.v),
                        Container(
                          child: Text(
                            "${product.bunch!.value == 0 ? '0' : '${product.bunch!.value} x ${product.innerCaseSize!}'}",
                            style: CustomTextStyles.titleSmallGray60002Bold,
                          ),
                        ),
                      ],
                    ),
                  Spacer(),
                  if (product.box!.value != 0)
                    Row(
                      children: [
                        Text(
                          "lbl_box".tr,
                          style: CustomTextStyles.titleSmallBluegray900Bold_1,
                        ),
                        SizedBox(width: 4.v),
                        Text(
                          ":",
                          style: CustomTextStyles.titleSmallBluegray900Bold_1,
                        ),
                        SizedBox(width: 4.v),
                        Container(
                          child: Text(
                            "${product.box!.value == 0 ? '0' : '${product.box!.value} x ${product.outerCaseSize!}'}",
                            style: CustomTextStyles.titleSmallGray60002Bold,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
            SizedBox(height: 2.v),
            Container(
              height: 1,
              color: appTheme.black900.withValues(alpha: 0.11),
            ),
            SizedBox(height: 2.v),
            Padding(
              padding: EdgeInsets.only(
                left: 8.h,
                right: 0.h,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        "Total Qty :",
                        style: CustomTextStyles.titleSmallBluegray900Bold_1,
                      ),
                      SizedBox(width: 8.v),
                      Text(
                        "${product.quantity!.value}",
                        style: CustomTextStyles.titleSmallPrimaryBold,
                      ),
                    ],
                  ),
                  Spacer(),
                  Row(
                    children: [
                      Text(
                        "Total Price :",
                        style: CustomTextStyles.titleSmallBluegray900Bold_1,
                      ),
                      SizedBox(width: 8.v),
                      Text(
                        "₹ ${product.grandTotal!.value.toStringAsFixed(2)}",
                        style: CustomTextStyles.titleSmallPrimaryBold,
                      ),
                    ],
                  ),
                  SizedBox(width: 6.v),
                ],
              ),
            ),
            SizedBox(height: 2.v),
          ],
        ),
      ),
    );
  }

  TableRow _buildTableRow(
    String label,
    String value,
    TextStyle labelStyle,
    TextStyle valueStyle,
  ) {
    return TableRow(
      children: [
        Text(
          label,
          style: labelStyle,
        ),
        Text(
          ":",
          style: labelStyle,
          textAlign: TextAlign.center,
        ),
        Text(
          value,
          style: valueStyle,
        ),
      ],
    );
  }
}

class CustomProductTextField extends StatelessWidget {
  final bool autoFocus;
  final bool enableInteractiveSelection;
  final String hintText;
  final TextStyle? hintStyle;
  final double? width;
  final double? height;
  final BorderRadius borderRadius;
  final void Function(String)? onChanged;
  final String? initialValue;
  final bool isEnabled;

  CustomProductTextField({
    required this.hintText,
    this.hintStyle,
    this.width,
    this.height,
    this.borderRadius = const BorderRadius.all(Radius.circular(0.0)),
    this.onChanged,
    this.initialValue,
    this.autoFocus = false,
    this.enableInteractiveSelection = true,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: borderRadius,
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextFormField(
        style: CustomTextStyles.titleSmallBluegray900,
        textAlign: TextAlign.center,
        textAlignVertical: TextAlignVertical.center,
        keyboardType:
            TextInputType.numberWithOptions(signed: true, decimal: true),
        textInputAction: TextInputAction.done,
        cursorHeight: 14,
        onChanged: onChanged,
        initialValue: initialValue,
        autofocus: autoFocus,
        onEditingComplete: () {
          FocusManager.instance.primaryFocus?.unfocus();
        },
        onTap: () {
          FocusScope.of(context).requestFocus();
        },
        enableInteractiveSelection: enableInteractiveSelection,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: hintStyle ?? CustomTextStyles.titleSmallBluegray900,
          contentPadding: EdgeInsets.only(bottom: 11),
          border: InputBorder.none,
        ),
        enabled: isEnabled,
      ),
    );
  }
}
