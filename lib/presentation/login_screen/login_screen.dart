// ignore_for_file:deprecated_member_use, unnecessary_null_comparison

import 'package:cached_network_image/cached_network_image.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:url_launcher/url_launcher.dart';

import 'controller/login_controller.dart';
import 'package:flutter/material.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/validation_functions.dart';
import 'package:sfm_new/widgets/custom_elevated_button.dart';
import 'package:sfm_new/widgets/custom_text_form_field.dart';

// ignore_for_file: must_be_immutable
class LoginScreen extends GetWidget<LoginController> {
  LoginScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bottom = MediaQuery.of(context).viewInsets.bottom;

    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) {
        print("onPopInvoked called");
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: SingleChildScrollView(
          reverse: true,
          child: Padding(
            padding: EdgeInsets.only(bottom: bottom),
            child: Container(
              width: double.maxFinite,
              padding: EdgeInsets.symmetric(horizontal: 18.h, vertical: 21.v),
              child: Column(
                children: [
                  SizedBox(height: 55.v),
                  Visibility(
                    visible: true,
                    child: Builder(
                      builder: (context) {
                        final imageUrl =
                            "${ApiClient.baseURL + controller.imageUrl + controller.imgAppLogo.value}";
                        print("imageUrl123: ${imageUrl}");

                        if (imageUrl.isEmpty) {
                          // Show placeholder if URL is empty
                          return Image.asset(
                            ImageConstant.imgSplash,
                            height: 100,
                            width: 100,
                            // fit: BoxFit.cover,
                          );
                        } else if (imageUrl.endsWith(".svg")) {
                          // Load SVG image
                          return SvgPicture.network(
                            imageUrl,
                            height: 100,
                            width: 100,
                            alignment: Alignment.center,
                            placeholderBuilder: (context) => Image.asset(
                              ImageConstant.imgSplash,
                              height: 100.v,
                              width: 100.h,
                            ),
                          );
                        } else {
                          // Load PNG/JPG image
                          return CachedNetworkImage(
                            imageUrl: imageUrl,
                            height: 100.v,
                            width: 100.h,
                            alignment: Alignment.center,
                            placeholder: (context, url) =>
                                Image.asset(ImageConstant.imgSplash),
                            errorWidget: (context, url, error) =>
                                Image.asset(ImageConstant.imgSplash),
                          );
                        }
                      },
                    ),
                  ),
                  SizedBox(height: 25.v),
                  Text("lbl_welcome_to".tr,
                      style: CustomTextStyles.titleLargeBluegray90001Regular),
                  Text("lbl_app_name".tr, style: theme.textTheme.displaySmall),
                  SizedBox(height: 60.v),
                  _buildEnterCodeMessage1(),
                  SizedBox(height: 32.v),
                  _buildEnterCodeMessage3(),
                  SizedBox(height: 31.v),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Padding(
                      padding: EdgeInsets.only(left: 9.h),
                      child: Text("lbl_password".tr,
                          style: CustomTextStyles.bodyMediumBluegray90001),
                    ),
                  ),
                  _buildPassword(),
                  SizedBox(height: 6.v),
                  SizedBox(height: 61.v),
                  _buildLOGIN(context),
                  SizedBox(height: 35.v),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      print("product by tapped");
                      _launchURL();
                    },
                    child: RichText(
                        text: TextSpan(children: [
                          TextSpan(
                              text: "Product By :",
                              style: CustomTextStyles.bodyMediumff092c4c),
                          TextSpan(text: " "),
                          TextSpan(
                            text: "NXC Controls Pvt. Ltd.",
                            style: CustomTextStyles.bodyMediumff004e92,
                          ),
                        ]),
                        textAlign: TextAlign.left),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnterCodeMessage() {
    return CustomTextFormField(
        width: 333.h,
        controller: controller.companyCodeController,
        hintText: "lbl_enter_your_code".tr,
        alignment: Alignment.bottomCenter,
        prefix: Container(
            margin: EdgeInsets.only(right: 7.h, bottom: 2.v),
            child: CustomImageView(
                imagePath: ImageConstant.imgUser3, height: 33.v, width: 35.h)),
        prefixConstraints: BoxConstraints(maxHeight: 36.v),
        contentPadding: EdgeInsets.only(top: 7.v, right: 30.h, bottom: 7.v),
        borderDecoration: TextFormFieldStyleHelper.underLineGray,
        filled: false);
  }

  Widget _buildEnterCodeMessage1() {
    return SizedBox(
      height: 57.v,
      width: 333.h,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Padding(
              padding: EdgeInsets.only(left: 6.h),
              child: Text("lbl_company_id".tr,
                  style: CustomTextStyles.bodyMediumBluegray90001),
            ),
          ),
          _buildEnterCodeMessage(),
        ],
      ),
    );
  }

  Widget _buildEnterCodeMessage2() {
    return CustomTextFormField(
        width: 333.h,
        controller: controller.employeeCodeController,
        hintText: "lbl_enter_your_code".tr,
        alignment: Alignment.bottomCenter,
        prefix: Container(
            margin: EdgeInsets.only(right: 7.h, bottom: 2.v),
            child: CustomImageView(
                imagePath: ImageConstant.imgUser3, height: 33.v, width: 35.h)),
        prefixConstraints: BoxConstraints(maxHeight: 36.v),
        contentPadding: EdgeInsets.only(top: 7.v, right: 30.h, bottom: 7.v),
        borderDecoration: TextFormFieldStyleHelper.underLineGray,
        filled: false);
  }

  Widget _buildEnterCodeMessage3() {
    return SizedBox(
      height: 57.v,
      width: 333.h,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Padding(
              padding: EdgeInsets.only(left: 6.h),
              child: Text("lbl_employee_code".tr,
                  style: CustomTextStyles.bodyMediumBluegray90001),
            ),
          ),
          _buildEnterCodeMessage2()
        ],
      ),
    );
  }

  Widget _buildPassword() {
    return Obx(
      () => CustomTextFormField(
        width: 333.h,
        controller: controller.passwordController,
        maxLength: 4,
        hintText: "lbl_enter_your_password".tr,
        textInputAction: TextInputAction.done,
        textInputType: TextInputType.number,
        prefix: Container(
            margin: EdgeInsets.only(right: 7.h, bottom: 2.v),
            child: CustomImageView(
                svgPath: ImageConstant.imgLock, height: 33.v, width: 35.h)),
        prefixConstraints: BoxConstraints(maxHeight: 36.v),
        contentPadding: EdgeInsets.only(top: 7.v, right: 30.h, bottom: 7.v),
        suffix: InkWell(
          onTap: () {
            controller.isShowPassword.value = !controller.isShowPassword.value;
            print(
                "controller.isShowPassword.value: ${controller.isShowPassword.value}");
          },
          child: Container(
            margin: EdgeInsets.fromLTRB(30.h, 6.v, 8.h, 6.v),
            child: CustomImageView(
              svgPath: controller.isShowPassword.value
                  ? ImageConstant.imgEyeVisible
                  : ImageConstant.imgEyeHidden, 
              height: 24.adaptSize,
              width: 24.adaptSize,
              color: theme.colorScheme.primary,
            ),
          ),
        ),
        suffixConstraints: BoxConstraints(maxHeight: 36.v),
        validator: (value) {
          if (value == null || (!isValidPassword(value, isRequired: true))) {
            return "Please enter valid password";
          }
          return null;
        },
        obscureText: controller.isShowPassword.value,
        borderDecoration: TextFormFieldStyleHelper.underLineGray,
        filled: false,
      ),
    );
  }

  Widget _buildLOGIN(BuildContext context) {
    return CustomElevatedButton(
      text: "lbl_login".tr,
      margin: EdgeInsets.symmetric(horizontal: 8.h),
      buttonStyle: CustomButtonStyles.outlineLightBlue,
      buttonTextStyle: CustomTextStyles.titleSmallOnErrorContainer_1,
      onPressed: () async {
        FocusManager.instance.primaryFocus?.unfocus();
        print("Login button called");
        if (controller.companyCodeController.text.isEmpty) {
          showToastMessage("Please enter company ID");
        } else if (controller.employeeCodeController.text.isEmpty) {
          showToastMessage("Please enter company code");
        } else if (controller.passwordController.text.isEmpty) {
          showToastMessage("Please enter password");
        } else if (!controller.arePermissionsGranted.value) {
          // Permissions not granted, request permissions
          await controller.requestPermissions();
        } else {
          final bool arePermissionsGranted =
              controller.arePermissionsGranted.value;
          print("arePermissionsGranted: ${arePermissionsGranted}");

          if (arePermissionsGranted == true) {
            print("arePermissionsGranted: ${arePermissionsGranted}");
            final List<ConnectivityResult> connectivityResult =
                await (Connectivity().checkConnectivity());

            if (connectivityResult.contains(ConnectivityResult.none)) {
              showToastMessage("Please check your internet connection");
            } else {
              controller.login();
            }
          } else {
            showToastMessage("Please allow permissions to login");
          }
        }
      },
    );
  }

  _launchURL() async {
    final Uri url = Uri.parse("https://www.nxccontrols.in/");
    if (!await launchUrl(url)) {
      throw Exception("Could not launch $url");
    }
  }
}
