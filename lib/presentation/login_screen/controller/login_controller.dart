import 'dart:async';
import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:sfm_new/presentation/login_screen/models/login_model.dart';
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:battery_plus/battery_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class LoginController extends GetxController {
  late TextEditingController companyCodeController = TextEditingController();
  late TextEditingController employeeCodeController = TextEditingController();
  late TextEditingController passwordController = TextEditingController();

  Rx<bool> isShowPassword = true.obs;
  RxBool obscureText = true.obs;

  final Dio dio = Dio();
  Rx<UserInfo?> userInfo = Rx<UserInfo?>(null);

  RxMap employeeData = {}.obs;
  RxList<dynamic> teamMembers = [].obs;

  final String _loginDateKey = 'loginDate';
  RxDouble lat = 0.0.obs;
  RxDouble long = 0.0.obs;
  RxString locationMessage = ''.obs;

  RxBool isLocationEnabled = false.obs;
  RxInt batteryLevel = 0.obs;
  RxString internetConnectivity = "".obs;
  RxString devicePlatform = "".obs;
  RxString osVersion = "".obs;
  RxString manufacturerName = "".obs;
  RxString modelName = "".obs;
  RxString deviceUUID = "".obs;

  final Rx<String> fcmToken = Rx<String>('');

  RxBool arePermissionsGranted = false.obs;

  RxString appVersion = ''.obs;
  RxString buildVersion = ''.obs;
  RxString versionCode = ''.obs;

  var imageUrl = 'images/';
  var imgAppLogo = ''.obs;

  var installedVersion = ''.obs;

  RxBool storagePermissionStatus = false.obs;

  @override
  void onInit() async {
    super.onInit();

    getToken();
    getLocation();
    getBatteryLevel();
    fetchDeviceInfo();
    fetchAppVersion();
    await getInstalledVersion();

    await requestStoragePermission();

    imgAppLogo.value = await SharedPrefManager.instance
            .getString(ConstantValues.loginAppLogo) ??
        "";
    print("imgAppLogo.value: ${imgAppLogo.value}");
    print(imgAppLogo.value.isEmpty);
    print(imgAppLogo.value.isNotEmpty);
    print(imgAppLogo.value != "");
  }

  Future<void> getInstalledVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    installedVersion.value = packageInfo.version;
    print("installedVersion: ${installedVersion.value}");
  }

  Future<void> fetchAppVersion() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      appVersion.value = packageInfo.version;
      buildVersion.value = packageInfo.buildNumber;
      versionCode.value = packageInfo.version;
    } catch (error) {
      print('Error fetching app version: $error');
    }
  }

  Future<void> fetchFcmToken() async {
    try {
      await FirebaseMessaging.instance.requestPermission();
      final token = await FirebaseMessaging.instance.getToken();
      fcmToken.value = token ?? '';
      print('FCM Token: $token');
    } catch (error) {
      print('Error fetching FCM token: $error');
    }
  }

  Future<void> requestPermissions() async {
    final notificationsPermissionStatus =
        await Permission.notification.request();
    print("notificationsPermissionStatus: ${notificationsPermissionStatus}");
    final locationPermissionStatus = await Permission.location.request();
    arePermissionsGranted.value = notificationsPermissionStatus.isGranted &&
        locationPermissionStatus.isGranted;

    if (arePermissionsGranted == true) {
      print("arePermissionsGranted: ${arePermissionsGranted}");
      login();
    } else {
      showToastMessage("Please allow permissions to login");
    }
  }

  void getToken() async {
    try {
      String? token = await FirebaseMessaging.instance.getToken();

      if (token != null) {
        print("Token: $token");
        fcmToken.value = token; 
      } else {
        print("Failed to retrieve FCM token");
        fcmToken.value = '';
      }
    } catch (e) {
     
      print("Error retrieving FCM token: $e");
      fcmToken.value =
          '';
    }
  }

  Future<void> requestStoragePermission() async {
    final DeviceInfoPlugin info =
        DeviceInfoPlugin(); 
    final AndroidDeviceInfo androidInfo = await info.androidInfo;
    print('releaseVersion : ${androidInfo.version.release}');
    final String androidVersion = "${androidInfo.version.release}";
    print("androidVersion: ${androidVersion}");
    bool havePermission = false;

    int comparison = compareAndroidVersions(
        androidVersion, "11"); //androidVersion.compareTo("11");
    print("comparison: ${comparison}");

    if (comparison > 0) {
      final request = await [
        Permission.manageExternalStorage,
      ].request();

      havePermission =
          request.values.every((status) => status == PermissionStatus.granted);
      print("havePermission: ${havePermission}");
      storagePermissionStatus.value = havePermission;
    } else {
      final status = await Permission.storage.request();
      print("else status: ${status}");
      havePermission = status.isGranted;
      print("havePermission: ${havePermission}");
      storagePermissionStatus.value = havePermission;
      print("storagePermissionStatus: ${storagePermissionStatus}");
    }

    if (!havePermission) {
      // if no permission then open app-setting
      // await openAppSettings();
      Get.dialog(
        AlertDialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.transparent,
          title: Text(
            'Permission Denied',
            style: CustomTextStyles.titleMediumPrimary.copyWith(
              color: theme.colorScheme.primary,
            ),
          ),
          content: Text('Please allow permission to create log file'),
          actions: [
            TextButton(
              onPressed: () async {
                await openAppSettings();
                Get.back();
              },
              child: Text(
                'Open Settings',
                style: CustomTextStyles.bodyMediumPoppinsBluegray900.copyWith(
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  int compareAndroidVersions(String version1, String version2) {
    // Split version strings into their components
    List<String> components1 = version1.split('.');
    List<String> components2 = version2.split('.');

    // Parse major version numbers
    int majorVersion1 = int.parse(components1[0]);
    int majorVersion2 = int.parse(components2[0]);

    // Assume minor and maintenance release versions are zero for single-number version
    int minorVersion1 = components1.length > 1 ? int.parse(components1[1]) : 0;
    int minorVersion2 = components2.length > 1 ? int.parse(components2[1]) : 0;
    int maintenanceVersion1 =
        components1.length > 2 ? int.parse(components1[2]) : 0;
    int maintenanceVersion2 =
        components2.length > 2 ? int.parse(components2[2]) : 0;

    // Compare major version numbers
    if (majorVersion1 < majorVersion2) {
      return -1;
    } else if (majorVersion1 > majorVersion2) {
      return 1;
    }

    // If major version numbers are equal, compare minor version numbers
    if (minorVersion1 < minorVersion2) {
      return -1;
    } else if (minorVersion1 > minorVersion2) {
      return 1;
    }

    // If minor version numbers are equal, compare maintenance release version numbers
    if (maintenanceVersion1 < maintenanceVersion2) {
      return -1;
    } else if (maintenanceVersion1 > maintenanceVersion2) {
      return 1;
    }

    // If all version numbers are equal, versions are the same
    return 0;
  }

  Future<void> getBatteryLevel() async {
    var battery = Battery();
    print(await battery.batteryLevel);

    batteryLevel.value = await battery.batteryLevel;

    final List<ConnectivityResult> connectivityResult =
        await (Connectivity().checkConnectivity());

    if (connectivityResult.contains(ConnectivityResult.mobile)) {
      internetConnectivity.value = "mobile";
    } else if (connectivityResult.contains(ConnectivityResult.wifi)) {
      internetConnectivity.value = "wifi";
    } else if (connectivityResult.contains(ConnectivityResult.none)) {
      print("No available network types");
    }

    print("batteryLevel: ${batteryLevel.value}");
  }

  Future<void> fetchDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    if (GetPlatform.isAndroid) {
      devicePlatform.value = "Android";

      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      print('model ${androidInfo.model}'); // e.g. "Moto G (4)"
      print('manufacturer ${androidInfo.manufacturer}');
      print('brand ${androidInfo.brand}');
      print('baseOS ${androidInfo.version.baseOS}');
      print('codename ${androidInfo.version.codename}');
      print('release ${androidInfo.version.release}');
      print('serialNumber ${androidInfo.serialNumber}');
      print('deviceId ${androidInfo.id}');

      deviceUUID.value = androidInfo.id;
      manufacturerName.value = androidInfo.brand;
      modelName.value = androidInfo.model;
      osVersion.value = androidInfo.version.release;

      final deviceInfoPlugin = DeviceInfoPlugin();
      final deviceInfo1 = await deviceInfoPlugin.deviceInfo;
      final allInfo = deviceInfo1.data;
      print("allInfo: ${allInfo}");

    } else if (GetPlatform.isIOS) {
      devicePlatform.value = "iOS";

      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      print("iosInfo: ${iosInfo}");

      deviceUUID.value = iosInfo.identifierForVendor ?? "";
      manufacturerName.value = iosInfo.systemName;
      modelName.value = iosInfo.systemName;
      osVersion.value = iosInfo.utsname.version;
    }

    print("devicePlatform: ${devicePlatform.value}");
  }

  Future<void> getLocation() async {
    Position position;
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();

    isLocationEnabled.value = serviceEnabled;

    if (!serviceEnabled) {
      locationMessage.value = "Location services are disabled";
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        locationMessage.value = 'Location permissions are denied.';
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      locationMessage.value =
          'Location permissions are permanently denied, we cannot request permissions.';
      return;
    }

    if (permission == LocationPermission.unableToDetermine) {}

    position = await Geolocator.getCurrentPosition();
    locationMessage.value =
        'Latitude: ${position.latitude}, Longitude: ${position.longitude}';

    lat.value = position.latitude;
    long.value = position.longitude;
  }

  @override
  void onClose() {
    super.onClose();
    companyCodeController.dispose();
    employeeCodeController.dispose();
    passwordController.dispose();
  }

  @override
  void onReady() {
  }

  void toggleObsureText() {
    obscureText.toggle();
  }

  Future<void> login() async {
    try {
      print("EasyLoading.show");
      EasyLoading.show(status: 'Loading...');

      Map<String, dynamic> deviceInfo = {
        'manufacturer': manufacturerName.value,
        'model': modelName.value,
        'device_type': devicePlatform.value,
        'os_version': osVersion.value,
        'uuid': deviceUUID.value,
        'fcm_token': fcmToken.value,
        'location': isLocationEnabled.value,
        'internet': internetConnectivity.value,
        'battery': batteryLevel.value,
        'buildVersion': buildVersion.value,
        'appVersion': installedVersion.value,
      };

      print("deviceInfo: ${deviceInfo}");

      String jsonDeviceInfo = jsonEncode(deviceInfo);

      Map<String, dynamic> data = {
        'company_code': '${companyCodeController.text}',
        'emp_code': '${employeeCodeController.text}',
        'emp_pin': '${passwordController.text}',
        'device_info': jsonDeviceInfo,
      };

      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.loginEndpoint),
        data: data,
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.statusCode);
      if (response.statusCode == 200) {
        UserInfo user = UserInfo.fromJson(response.data);
        print("Login Response ${user}");

        await storeUserInfoInSharedPreferences(response.data);
       
        await storeLoginDate();

        EasyLoading.dismiss();

        if (user.isCheckedIn) {
          await Get.toNamed(AppRoutes.syncDataScreen);
        } else {
          await Get.toNamed(AppRoutes.attendanceScreen);
        }
      } else {
        print('Response data is null');
        showToastMessage("${response.statusMessage}");
      }
      EasyLoading.dismiss();
    } on DioException catch (e) {
      EasyLoading.dismiss();
      if (e.response != null) {
        print('Error ${e.response!.statusCode}: ${e.response!.statusMessage}');
        if (e.response!.statusCode == 422) {
          print(e.response!.data["message"] ?? "No data found");
          showToastMessage(e.response!.data["message"] ?? "No data found");
        } else {
          showToastMessage(
              '${e.response!.statusCode}: ${e.response!.statusMessage}');
        }
      }
    } finally {
      EasyLoading.dismiss();
    }
  }

  // Method to store today's date in SharedPreferences
  Future<void> storeLoginDate() async {
    print("storeLoginDate called");
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final DateTime now = DateTime.now();
    print("Now: ${now}");
    final String formattedDate = '${now.year}-${now.month}-${now.day}';
    await prefs.setString(_loginDateKey, formattedDate);
  }

  String getTokenFromInput(String input) {
    List<String> parts = input.split('|');
    if (parts.length > 1) {
      return parts[1];
    } else {
      return input; // If no '|' found, return the original input
    }
  }

  Future<void> storeUserInfoInSharedPreferences(
      Map<String, dynamic> apiResponse) async {
    print("storeUserInfoInSharedPreferences called");

    // Store token
    SharedPrefManager.instance.setString(
        ConstantValues.token, getTokenFromInput(apiResponse['token']));

    SharedPrefManager.instance
        .setBool(ConstantValues.isCheckedIn, apiResponse['isCheckedIn']);

    if (apiResponse['checkInType'] != null) {
      SharedPrefManager.instance
          .setInt(ConstantValues.checkInType, apiResponse['checkInType']);
    } else {
      SharedPrefManager.instance.setInt(ConstantValues.checkInType, 0);
    }

    // Store employee data
    final Map<String, dynamic> employeeData = apiResponse['employee'];
    final String employeeDataJson = json.encode(employeeData);
    SharedPrefManager.instance
        .setString(ConstantValues.employee, employeeDataJson);

    SharedPrefManager.instance.setBool(ConstantValues.isDailyLogOut, false);

    List<dynamic> combinedList = [];

    final List<dynamic> teamMembers = employeeData['team_members'];
    print("teamMembers: ${teamMembers}");

    if (employeeData['reporting_emp'] != null) {
      combinedList.add(employeeData['reporting_emp']);
    }
    combinedList.addAll(teamMembers);

    print("combinedList: ${combinedList}");

    final String teamMembersJson = json.encode(combinedList);
    SharedPrefManager.instance
        .setString(ConstantValues.teamMembers, teamMembersJson);
  }
}
