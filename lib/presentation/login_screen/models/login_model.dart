

import 'dart:convert';

class UserInfo {
  final String token;
  final bool isCheckedIn;
  final EmployeeData employee;

  UserInfo({
    required this.token,
    required this.isCheckedIn,
    required this.employee,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      token: json['token'],
      isCheckedIn: json['isCheckedIn'],
      employee: EmployeeData.from<PERSON><PERSON>(json['employee']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'isCheckedIn': isCheckedIn,
      'employee': employee.toJson(),
    };
  }

  @override
  String toString() {
    return 'UserInfo { token: $token, isCheckedIn: $isCheckedIn, employee: $employee }';
  }
}

class EmployeeData {
  final String? empCode;
  final String? empName;
  final String? empMobile;
  final String? empEmail;
  final String? reportingEmpCode;
  final int? designationId;
  final int? marketType;
  final int? empStatus;
  final int? empPinStatus;
  final String? deviceInfo;
  final String? expHq;
  final String? expExhq;
  final String? expNighthold;
  final String? createdAt;
  final String? updatedAt;
  final bool? checkIn;
  final List<TeamMember> teamMembers;

  EmployeeData({
    this.empCode,
    this.empName,
    this.empMobile,
    this.empEmail,
    this.reportingEmpCode,
    this.designationId,
    this.marketType,
    this.empStatus,
    this.empPinStatus,
    this.deviceInfo,
    this.expHq,
    this.expExhq,
    this.expNighthold,
    this.createdAt,
    this.updatedAt,
    this.checkIn,
    required this.teamMembers,
  });

  factory EmployeeData.fromJson(Map<String, dynamic> json) {
    return EmployeeData(
      empCode: json['emp_code'] ?? "",
      empName: json['emp_name'] ?? "",
      empMobile: json['emp_mobile'] ?? "",
      empEmail: json['emp_email'] ?? "",
      reportingEmpCode: json['reporting_emp_code'] ?? '',
      designationId: json['designation_id'] ?? 0,
      marketType: json['market_type'] ?? 0,
      empStatus: json['emp_status'] ?? 0,
      empPinStatus: json['emp_pin_status'] ?? 0,
      deviceInfo: json['device_info'] ?? "",
      expHq: json['device_info'] ?? "",
      expExhq: json['device_info'] ?? "",
      expNighthold: json['device_info'] ?? "",
      createdAt: json['created_at'] ?? "",
      updatedAt: json['updated_at'] ?? "",
      // checkIn: json['checkIn'] ?? false,
      teamMembers: json['team_members'] != null
          ? List<TeamMember>.from(json['team_members']
              .map((teamMemberJson) => TeamMember.fromJson(teamMemberJson)))
          : [],


    );
  }

  Map<String, dynamic> toJson() {
    // final Map<String,dynamic> empData = new Map<String, dynamic>();

    return {
      'emp_code': empCode,
      'emp_name': empName,
      'emp_mobile': empMobile,
      'emp_email': empEmail,
      'reporting_emp_code': reportingEmpCode,
      'designation_id': designationId,
      'market_type': marketType,
      'emp_status': empStatus,
      'emp_pin_status': empPinStatus,
      'device_info': deviceInfo,
      'exp_hq': expHq,
      'exp_exhq': expExhq,
      'exp_nighthold': expNighthold,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'checkIn': checkIn,
      'teamMembers': List<dynamic>.from(teamMembers.map((e) => e.toJson())),
      // 'teamMembers': teamMembers.toJson(),
      // 'team_members': teamMembers.map((x) => x.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return json.encode(toJson());
  }

}


class TeamMember {
  final String? empCode;
  final String? empName;
  final String? empMobile;
  final String? empEmail;
  final String? reportingEmpCode;
  final int? designationId;
  final int? marketType;
  final int? empStatus;
  final int? empPinStatus;
  final String? deviceInfo;
  final String? expHq;
  final String? expExhq;
  final String? expNighthold;


  TeamMember({
    this.empCode,
    this.empName,
    this.empMobile,
    this.empEmail,
    this.reportingEmpCode,
    this.designationId,
    this.marketType,
    this.empStatus,
    this.empPinStatus,
    this.deviceInfo,
    this.expHq,
    this.expExhq,
    this.expNighthold,

  });

  factory TeamMember.fromJson(Map<String, dynamic> json) {
    return TeamMember(
      empCode: json['emp_code'] ?? "",
      empName: json['emp_name'] ?? "",
      empMobile: json['emp_mobile'] ?? "",
      empEmail: json['emp_email'] ?? "",
      reportingEmpCode: json['reporting_emp_code'] ?? "",
      designationId: json['designation_id'] ?? 0,
      marketType: json['market_type'] ?? 0,
      empStatus: json['emp_status'] ?? 0,
      empPinStatus: json['emp_pin_status'] ?? 0,
      deviceInfo: json['device_info'] ?? "",
      expHq: json['exp_hq'] ?? "",
      expExhq: json['exp_exhq'] ?? "",
      expNighthold: json['exp_nighthold'] ?? "",

    );
  }

  Map<String, dynamic> toJson() {
    return {
      'emp_code': empCode,
      'emp_name': empName,
      'emp_mobile': empMobile,
      'emp_email': empEmail,
      'reporting_emp_code': reportingEmpCode,
      'designation_id': designationId,
      'market_type': marketType,
      'emp_status': empStatus,
      'emp_pin_status': empPinStatus,
      'device_info': deviceInfo,
      'exp_hq': expHq,
      'exp_exhq': expExhq,
      'exp_nighthold': expNighthold,

    };
  }


  @override
  String toString() {
    return json.encode(toJson());
  }
}
