// ignore_for_file: duplicate_import, prefer_const_constructors

import 'package:sfm_new/presentation/add_beat_screen/add_beat_screen.dart';
import 'package:sfm_new/presentation/add_beat_screen/binding/add_beat_binding.dart';
import 'package:sfm_new/presentation/add_dealer_screen/add_dealer_screen.dart';
import 'package:sfm_new/presentation/add_dealer_screen/binding/add_dealer_binding.dart';
import 'package:sfm_new/presentation/add_payment_screen/add_payment_screen.dart';
import 'package:sfm_new/presentation/add_payment_screen/binding/add_payment_binding.dart';
import 'package:sfm_new/presentation/add_retailer_screen/add_retailer_screen.dart';
import 'package:sfm_new/presentation/add_retailer_screen/binding/add_retailer_binding.dart';
import 'package:sfm_new/presentation/add_route_plan_screen/add_route_plan_screen.dart';
import 'package:sfm_new/presentation/add_route_plan_screen/binding/add_route_plan_binding.dart';
import 'package:sfm_new/presentation/beat_screen/beat_screen.dart';
import 'package:sfm_new/presentation/beat_screen/binding/beat_binding.dart';
import 'package:sfm_new/presentation/download_report_screen/binding/download_report_binding.dart';
import 'package:sfm_new/presentation/download_report_screen/download_report_screen.dart';
import 'package:sfm_new/presentation/help_support_screen/help_support_screen.dart';
import 'package:sfm_new/presentation/home_screen/add_new_beat_screen.dart';
import 'package:sfm_new/presentation/home_screen/home_screen.dart';
import 'package:sfm_new/presentation/home_screen/binding/home_binding.dart';
import 'package:sfm_new/presentation/order_history_detail_screen/binding/order_history_detail_binding.dart';
import 'package:sfm_new/presentation/order_history_detail_screen/order_history_detail_screen.dart';
import 'package:sfm_new/presentation/order_history_screen/order_view_invoice_screen.dart';
import 'package:sfm_new/presentation/report_one_screen/binding/report_one_binding.dart';
import 'package:sfm_new/presentation/report_one_screen/report_one_screen.dart';
import 'package:sfm_new/presentation/report_screen/report_screen.dart';
import 'package:sfm_new/presentation/report_screen/binding/report_binding.dart';
import 'package:sfm_new/presentation/login_screen/login_screen.dart';
import 'package:sfm_new/presentation/login_screen/binding/login_binding.dart';
import 'package:sfm_new/presentation/route_plan_list_screen/binding/route_plan_list_binding.dart';
import 'package:sfm_new/presentation/route_plan_list_screen/route_plan_list_screen.dart';
import 'package:sfm_new/presentation/super_stockist_screen/super_stockist_screen.dart';
import 'package:sfm_new/presentation/super_stockist_screen/binding/super_stockist_binding.dart';
import 'package:sfm_new/presentation/product_screen/product_screen.dart';
import 'package:sfm_new/presentation/product_screen/binding/product_binding.dart';
import 'package:sfm_new/presentation/order_history_screen/order_history_screen.dart';
import 'package:sfm_new/presentation/order_history_screen/binding/order_history_binding.dart';
import 'package:sfm_new/presentation/show_order_screen/show_order_screen.dart';
import 'package:sfm_new/presentation/show_order_screen/binding/show_order_binding.dart';
import 'package:sfm_new/presentation/place_order_screen/place_order_screen.dart';
import 'package:sfm_new/presentation/place_order_screen/binding/place_order_binding.dart';
import 'package:sfm_new/presentation/no_order_screen/no_order_screen.dart';
import 'package:sfm_new/presentation/no_order_screen/binding/no_order_binding.dart';
import 'package:sfm_new/presentation/complain_screen/complain_screen.dart';
import 'package:sfm_new/presentation/complain_screen/binding/complain_binding.dart';
import 'package:sfm_new/presentation/team_report_screen/binding/team_report_binding.dart';
import 'package:sfm_new/presentation/team_report_screen/team_report_screen.dart';
import 'package:sfm_new/presentation/view_complain_screen/view_complain_screen.dart';
import 'package:sfm_new/presentation/view_complain_screen/binding/view_complain_binding.dart';
import 'package:sfm_new/presentation/add_complain_screen/add_complain_screen.dart';
import 'package:sfm_new/presentation/add_complain_screen/binding/add_complain_binding.dart';
import 'package:sfm_new/presentation/leave_screen/leave_screen.dart';
import 'package:sfm_new/presentation/leave_screen/binding/leave_binding.dart';
import 'package:sfm_new/presentation/add_leave_screen/add_leave_screen.dart';
import 'package:sfm_new/presentation/add_leave_screen/binding/add_leave_binding.dart';
import 'package:sfm_new/presentation/expense_screen/expense_screen.dart';
import 'package:sfm_new/presentation/expense_screen/binding/expense_binding.dart';
import 'package:sfm_new/presentation/payment_screen/payment_screen.dart';
import 'package:sfm_new/presentation/payment_screen/binding/payment_binding.dart';

import 'package:sfm_new/presentation/view_expense_details_screen/view_expense_details_screen.dart';
import 'package:sfm_new/presentation/view_expense_details_screen/binding/view_expense_details_binding.dart';
import 'package:sfm_new/presentation/add_expense_screen/add_expense_screen.dart';
import 'package:sfm_new/presentation/add_expense_screen/binding/add_expense_binding.dart';

import 'package:sfm_new/presentation/add_payment_screen/add_payment_screen.dart';
import 'package:sfm_new/presentation/add_payment_screen/binding/add_payment_binding.dart';

import 'package:sfm_new/presentation/login_one_screen/login_one_screen.dart';
import 'package:sfm_new/presentation/login_one_screen/binding/login_one_binding.dart';
import 'package:sfm_new/presentation/attendance_screen/attendance_screen.dart';
import 'package:sfm_new/presentation/attendance_screen/binding/attendance_binding.dart';
import 'package:sfm_new/presentation/help_support_screen/help_support_screen.dart';
import 'package:sfm_new/presentation/help_support_screen/binding/help_support_binding.dart';
import 'package:sfm_new/presentation/sync_data_screen/sync_data_screen.dart';
import 'package:sfm_new/presentation/sync_data_screen/binding/sync_data_binding.dart';
import 'package:sfm_new/presentation/expense_filter_screen/expense_filter_screen.dart';
import 'package:sfm_new/presentation/expense_filter_screen/binding/expense_filter_binding.dart';
import 'package:sfm_new/presentation/inventory_screen/inventory_screen.dart';
import 'package:sfm_new/presentation/inventory_screen/binding/inventory_binding.dart';
import 'package:sfm_new/presentation/notification_screen/notification_screen.dart';
import 'package:sfm_new/presentation/notification_screen/binding/notification_binding.dart';
import 'package:sfm_new/presentation/app_navigation_screen/app_navigation_screen.dart';
import 'package:sfm_new/presentation/app_navigation_screen/binding/app_navigation_binding.dart';
import 'package:get/get.dart';
import 'package:sfm_new/presentation/view_payment_screen/binding/view_payment_binding.dart';
import 'package:sfm_new/presentation/view_payment_screen/view_payment_screen.dart';

class AppRoutes {
  static const String homeScreen = '/home_screen';

  static const String reportScreen = '/report_screen';

  static const String reportThreeScreen = '/report_three_screen';

  static const String reportTwoScreen = '/report_two_screen';

  static const String syncDataSixScreen = '/sync_data_six_screen';

  static const String syncDataTwoScreen = '/sync_data_two_screen';

  static const String reportOneScreen = '/report_one_screen';

  static const String downloadReportScreen = '/download_report_screen';

  static const String teamReportScreen = '/team_report_screen';

  static const String placePaymentTwoScreen = '/place_payment_two_screen';

  static const String syncDataSevenScreen = '/sync_data_seven_screen';

  static const String loginScreen = '/login_screen';

  static const String superStockistScreen = '/super_stockist_screen';

  static const String distributorsScreen = '/distributors_screen';

  static const String retailerPage = '/retailer_page';

  static const String retailerTabContainerScreen =
      '/retailer_tab_container_screen';

  static const String paymentPage = '/payment_page';

  static const String shopDetailsPage = '/shop_details_page';

  static const String shopDetailsTabContainerScreen =
      '/shop_details_tab_container_screen';

  static const String cashPaymentPage = '/cash_payment_page';

  static const String chequePaymentOnePage = '/cheque_payment_one_page';

  static const String chequePaymentOneTabContainerScreen =
      '/cheque_payment_one_tab_container_screen';

  static const String netbankingPaymentPage = '/netbanking_payment_page';

  static const String productScreen = '/product_screen';

  static const String orderHistoryScreen = '/order_history_screen';

  static const String orderViewInvoiceScreen = '/order_view_invoice_screen';

  static const String addNewBeatScreen = '/add_new_beat_screen';

  static const String routePlanListScreen = '/route_plan_list_screen';

  static const String orderDetailsScreen = '/order_details_screen';

  static const String showOrderScreen = '/show_order_screen';

  static const String placeOrderScreen = '/place_order_screen';

  static const String orderHistoryDetailScreen = '/order_history_detail_screen';

  static const String noOrderScreen = '/no_order_screen';

  static const String complainScreen = '/complain_screen';

  static const String viewComplainScreen = '/view_complain_screen';

  static const String addComplainScreen = '/add_complain_screen';

  static const String leaveScreen = '/leave_screen';

  static const String addLeaveScreen = '/add_leave_screen';

  static const String expenseScreen = '/expense_screen';

  static const String paymentScreen = '/payment_screen';

  static const String addPaymentScreen = '/add_payment_screen';

  static const String addRetailerScreen = '/add_retailer_screen';

  static const String addDealerScreen = '/add_dealer_screen';

  static const String addBeatScreen = '/add_beat_screen';

  static const String addRoutePlanScreen = '/add_route_plan_screen';

  static const String beatScreen = '/beat_screen';

  static const String viewExpenseDetailsScreen = '/view_expense_details_screen';

  static const String viewPaymentScreen = '/view_payment_screen';

  static const String addExpenseScreen = '/add_expense_screen';

  static const String loginOneScreen = '/login_one_screen';

  static const String attendanceScreen = '/attendance_screen';

  static const String homeOneScreen = '/home_one_screen';

  static const String graphScreen = '/graph_screen';

  static const String syncDataFiveScreen = '/sync_data_five_screen';

  static const String syncDataFourScreen = '/sync_data_four_screen';

  static const String placePaymentOneScreen = '/place_payment_one_screen';

  static const String menuOneScreen = '/menu_one_screen';

  static const String syncDataThreeScreen = '/sync_data_three_screen';

  static const String syncDataScreen = '/sync_data_screen';

  static const String syncDataOneScreen = '/sync_data_one_screen';

  static const String menuScreen = '/menu_screen';

  static const String paymentFilterScreen = '/payment_filter_screen';

  static const String complainFilterScreen = '/complain_filter_screen';

  static const String leaveFilterScreen = '/leave_filter_screen';

  static const String expenseFilterScreen = '/expense_filter_screen';

  static const String paymentFilterOneScreen = '/payment_filter_one_screen';

  static const String chequePaymentScreen = '/cheque_payment_screen';

  static const String rtgsNeftImpsPaymentScreen =
      '/rtgs_neft_imps_payment_screen';

  static const String cashPaymentOneScreen = '/cash_payment_one_screen';

  static const String inventoryScreen = '/inventory_screen';

  static const String menuTwoScreen = '/menu_two_screen';

  static const String retailerShopListScreen = '/retailer_shop_list_screen';

  static const String retailerShopDetailsScreen =
      '/retailer_shop_details_screen';

  static const String syncDataEightScreen = '/sync_data_eight_screen';

  static const String reportFourScreen = '/report_four_screen';

  static const String notificationScreen = '/notification_screen';

  static const String notificatonTwoScreen = '/notificaton_two_screen';

  static const String notificationThreeScreen = '/notification_three_screen';

  static const String paymentOnePage = '/payment_one_page';

  static const String appNavigationScreen = '/app_navigation_screen';

  static const String helpSupportScreen = '/help_support_screen';

  static const String initialRoute = '/initialRoute';

  static List<GetPage> pages = [
    GetPage(
      name: homeScreen,
      page: () => HomeScreen(),
      bindings: [
        HomeBinding(),
      ],
    ),
    GetPage(
      name: reportScreen,
      page: () => ReportScreen(),
      bindings: [
        ReportBinding(),
      ],
    ),
    GetPage(
      name: reportOneScreen,
      page: () => ReportOneScreen(),
      bindings: [
        ReportOneBinding(),
      ],
    ),
    GetPage(
      name: downloadReportScreen,
      page: () => DownloadReportScreen(),
      bindings: [
        DownloadReportBinding(),
      ],
    ),
    GetPage(
      name: teamReportScreen,
      page: () => TeamReportScreen(),
      bindings: [
        TeamReportBinding(),
      ],
    ),
    GetPage(
      name: loginScreen,
      page: () => LoginScreen(),
      bindings: [
        LoginBinding(),
      ],
    ),
    GetPage(
      name: superStockistScreen,
      page: () => SuperStockistScreen(),
      bindings: [
        SuperStockistBinding(),
      ],
    ),
    GetPage(
      name: productScreen,
      page: () => ProductScreen(),
      bindings: [
        ProductBinding(),
      ],
    ),
    GetPage(
      name: orderHistoryScreen,
      page: () => OrderHistoryScreen(),
      bindings: [
        OrderHistoryBinding(),
      ],
    ),
    GetPage(
      name: addNewBeatScreen,
      page: () => AddNewBeatScreen(),
      bindings: [
        HomeBinding(),
      ],
    ),

    GetPage(
      name: routePlanListScreen,
      page: () => RoutePlanListScreen(),
      bindings: [
        RoutePlanListBinding(),
      ],
    ),
    GetPage(
      name: orderViewInvoiceScreen,
      page: () => OrderViewInvoiceScreen(),
      bindings: [
        OrderHistoryBinding(),
      ],
    ),
    GetPage(
      name: showOrderScreen,
      page: () => ShowOrderScreen(),
      bindings: [
        ShowOrderBinding(),
      ],
    ),
    GetPage(
      name: placeOrderScreen,
      page: () => PlaceOrderScreen(),
      bindings: [
        PlaceOrderBinding(),
      ],
    ),
    GetPage(
      name: orderHistoryDetailScreen,
      page: () => OrderHistoryDetailScreen(),
      bindings: [
        OrderHistoryDetailBinding(),
      ],
    ),
    GetPage(
      name: noOrderScreen,
      page: () => NoOrderScreen(),
      bindings: [
        NoOrderBinding(),
      ],
    ),
    GetPage(
      name: complainScreen,
      page: () => ComplainScreen(),
      bindings: [
        ComplainBinding(),
      ],
    ),
    GetPage(
      name: viewComplainScreen,
      page: () => ViewComplainScreen(),
      bindings: [
        ViewComplainBinding(),
      ],
    ),
    GetPage(
      name: addComplainScreen,
      page: () => AddComplainScreen(),
      bindings: [
        AddComplainBinding(),
      ],
    ),
    GetPage(
      name: leaveScreen,
      page: () => LeaveScreen(),
      bindings: [
        LeaveBinding(),
      ],
    ),
    GetPage(
      name: addLeaveScreen,
      page: () => AddLeaveScreen(),
      bindings: [
        AddLeaveBinding(),
      ],
    ),
    GetPage(
      name: expenseScreen,
      page: () => ExpenseScreen(),
      bindings: [
        ExpenseBinding(),
      ],
    ),
    GetPage(
      name: paymentScreen,
      page: () => PaymentScreen(),
      bindings: [
        PaymentBinding(),
      ],
    ),
    GetPage(
      name: addPaymentScreen,
      page: () => AddPaymentScreen(),
      bindings: [
        AddPaymentBinding(),
      ],
    ),
    GetPage(
      name: addDealerScreen,
      page: () => AddDealerScreen(),
      bindings: [
        AddDealerBinding(),
      ],
    ),
    GetPage(
      name: addBeatScreen,
      page: () => AddBeatScreen(),
      bindings: [
        AddBeatBinding(),
      ],
    ),

    GetPage(
      name: addRoutePlanScreen,
      page: () => AddRoutePlanScreen(),
      bindings: [
        AddRoutePlanBinding(),
      ],
    ),

    GetPage(
      name: addRetailerScreen,
      page: () => AddRetailerScreen(),
      bindings: [
        AddRetailerBinding(),
      ],
    ),
    GetPage(
      name: beatScreen,
      page: () => BeatScreen(),
      bindings: [
        BeatBinding(),
      ],
    ),
    GetPage(
      name: viewPaymentScreen,
      page: () => ViewPaymentScreen(),
      bindings: [
        ViewPaymentBinding(),
      ],
    ),
    GetPage(
      name: viewExpenseDetailsScreen,
      page: () => ViewExpenseDetailsScreen(),
      bindings: [
        ViewExpenseDetailsBinding(),
      ],
    ),
    GetPage(
      name: addExpenseScreen,
      page: () => AddExpenseScreen(),
      bindings: [
        AddExpenseBinding(),
      ],
    ),
    GetPage(
      name: loginOneScreen,
      page: () => LoginOneScreen(),
      bindings: [
        LoginOneBinding(),
      ],
    ),
    GetPage(
      name: attendanceScreen,
      page: () => AttendanceScreen(),
      bindings: [
        AttendanceBinding(),
      ],
    ),

    GetPage(
      name: syncDataScreen,
      page: () => SyncDataScreen(),
      bindings: [
        SyncDataBinding(),
      ],
    ),
    GetPage(
      name: expenseFilterScreen,
      page: () => ExpenseFilterScreen(),
      bindings: [
        ExpenseFilterBinding(),
      ],
    ),
    GetPage(
      name: inventoryScreen,
      page: () => InventoryScreen(),
      bindings: [
        InventoryBinding(),
      ],
    ),
    GetPage(
      name: notificationScreen,
      page: () => NotificationScreen(),
      bindings: [
        NotificationBinding(),
      ],
    ),
    GetPage(
      name: appNavigationScreen,
      page: () => AppNavigationScreen(),
      bindings: [
        AppNavigationBinding(),
      ],
    ),
    GetPage(
      name: helpSupportScreen,
      page: () => HelpSupportScreen(),
      bindings: [
        HelpSupportBinding(),
      ],
    ),
    GetPage(
      name: initialRoute,
      page: () => LoginScreen(),
      bindings: [
        LoginBinding(),
      ],
    )
  ];
}
