import 'package:flutter/material.dart';
import '../../core/app_export.dart';

/// Helper class for managing themes and colors.
class ThemeHelper {
  // The current app theme
  var _appTheme = PrefUtils().getThemeData();

// A map of custom color themes supported by the app
  Map<String, PrimaryColors> _supportedCustomColor = {
    'primary': PrimaryColors()
  };

// A map of color schemes supported by the app
  Map<String, ColorScheme> _supportedColorScheme = {
    'primary': ColorSchemes.primaryColorScheme
  };

  /// Changes the app theme to [_newTheme].
  void changeTheme(String _newTheme) {
    PrefUtils().setThemeData(_newTheme);
    Get.forceAppUpdate();
  }

  /// Returns the primary colors for the current theme.
  PrimaryColors _getThemeColors() {
    //throw exception to notify given theme is not found or not generated by the generator
    if (!_supportedCustomColor.containsKey(_appTheme)) {
      throw Exception(
          "$_appTheme is not found.Make sure you have added this theme class in JSON Try running flutter pub run build_runner");
    }
    //return theme from map

    return _supportedCustomColor[_appTheme] ?? PrimaryColors();
  }

  /// Returns the current theme data.
  ThemeData _getThemeData() {
    //throw exception to notify given theme is not found or not generated by the generator
    if (!_supportedColorScheme.containsKey(_appTheme)) {
      throw Exception(
          "$_appTheme is not found.Make sure you have added this theme class in JSON Try running flutter pub run build_runner");
    }
    //return theme from map

    var colorScheme =
        _supportedColorScheme[_appTheme] ?? ColorSchemes.primaryColorScheme;
    return ThemeData(
      visualDensity: VisualDensity.standard,
      colorScheme: colorScheme,
      textTheme: TextThemes.textTheme(colorScheme),
      scaffoldBackgroundColor:
          colorScheme.onErrorContainer.withValues(alpha: 1),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.h),
          ),
          shadowColor: appTheme.black900.withValues(alpha: 0.25),
          elevation: 4,
          visualDensity: const VisualDensity(
            vertical: -4,
            horizontal: -4,
          ),
          padding: EdgeInsets.zero,
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          backgroundColor: Colors.transparent,
          side: BorderSide(
            color: appTheme.blue400,
            width: 1.h,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(17.h),
          ),
          visualDensity: const VisualDensity(
            vertical: -4,
            horizontal: -4,
          ),
          padding: EdgeInsets.zero,
        ),
      ),
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return appTheme.gray5001;
          }
          return colorScheme.onSurface;
        }),
        visualDensity: const VisualDensity(
          vertical: -4,
          horizontal: -4,
        ),
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return appTheme.gray5001;
          }
          return colorScheme.onSurface;
        }),
        side: BorderSide(
          width: 1,
        ),
        visualDensity: const VisualDensity(
          vertical: -4,
          horizontal: -4,
        ),
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: colorScheme.primary,
      ),
      dividerTheme: DividerThemeData(
        thickness: 216,
        space: 216,
        color: appTheme.indigo200,
      ),
    );
  }

  /// Returns the primary colors for the current theme.
  PrimaryColors themeColor() => _getThemeColors();

  /// Returns the current theme data.
  ThemeData themeData() => _getThemeData();
}

/// Class containing the supported text theme styles.
class TextThemes {
  static TextTheme textTheme(ColorScheme colorScheme) => TextTheme(
        bodyLarge: TextStyle(
          color: colorScheme.onErrorContainer.withValues(alpha: 1),
          fontSize: 18.fSize,
          fontFamily: 'Noto Sans Tamil',
          fontWeight: FontWeight.w400,
        ),
        bodyMedium: TextStyle(
          color: appTheme.blueGray40001,
          fontSize: 15.fSize,
          fontFamily: 'OpenSans',
          fontWeight: FontWeight.w400,
        ),
        bodySmall: TextStyle(
          color: appTheme.black900,
          fontSize: 12.fSize,
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w400,
        ),
        displaySmall: TextStyle(
          color: appTheme.lightBlue900,
          fontSize: 34.fSize,
          fontFamily: 'OpenSans',
          fontWeight: FontWeight.w800,
        ),
        headlineLarge: TextStyle(
          color: appTheme.lightBlue900,
          fontSize: 30.fSize,
          fontFamily: 'OpenSans',
          fontWeight: FontWeight.w800,
        ),
        headlineMedium: TextStyle(
          color: colorScheme.onPrimary,
          fontSize: 28.fSize,
          fontFamily: 'OpenSans',
          fontWeight: FontWeight.w700,
        ),
        headlineSmall: TextStyle(
          color: colorScheme.onPrimaryContainer,
          fontSize: 25.fSize,
          fontFamily: 'OpenSans',
          fontWeight: FontWeight.w600,
        ),
        labelLarge: TextStyle(
          color: appTheme.gray60002,
          fontSize: 12.fSize,
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w600,
        ),
        labelMedium: TextStyle(
          color: appTheme.gray50002,
          fontSize: 10.fSize,
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
        ),
        labelSmall: TextStyle(
          color: colorScheme.onErrorContainer.withValues(alpha: 1),
          fontSize: 9.fSize,
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w600,
        ),
        titleLarge: TextStyle(
          color: appTheme.black900,
          fontSize: 20.fSize,
          fontFamily: 'OpenSans',
          fontWeight: FontWeight.w600,
        ),
        titleMedium: TextStyle(
          color: appTheme.blueGray40001,
          fontSize: 16.fSize,
          fontFamily: 'OpenSans',
          fontWeight: FontWeight.w600,
        ),
        titleSmall: TextStyle(
          color: appTheme.blueGray700,
          fontSize: 14.fSize,
          fontFamily: 'OpenSans',
          fontWeight: FontWeight.w600,
        ),
      );
}

/// Class containing the supported color schemes.
class ColorSchemes {
  static final primaryColorScheme = ColorScheme.light(
    // Primary colors
    primary: Color(0XFF0752AD),
    primaryContainer: Color(0XFFF2F3F6),
    secondaryContainer: Color(0X3F0752AD),

    // Error colors
    errorContainer: Color(0XFF343232),
    onError: Color(0XFFE93535),
    onErrorContainer: Color(0X87FFFFFF),

    // On colors(text colors)
    onPrimary: Color(0XFF0A0A0A),
    onPrimaryContainer: Color(0XFF1B2C57),
  );
}

/// Class containing custom colors for a primary theme.
class PrimaryColors {
  // Amber
  Color get amberA400 => Color(0XFFFFC200);
  Color get amberA700 => Color(0XFFEAA900);

  // Black
  Color get black900 => Color(0XFF000000);

  // Blue
  Color get blue100 => Color(0XFFAECFF6);
  Color get blue10001 => Color(0XFFADCEF6);
  Color get blue400 => Color(0XFF52ABE2);
  Color get blue500 => Color(0XFF2196F3);

  // BlueGray
  Color get blueGray100 => Color(0XFFC8C9CF);
  Color get blueGray10001 => Color(0XFFDDCFCC);
  Color get blueGray200 => Color(0XFFABB3BB);
  Color get blueGray20001 => Color(0XFFB2B9CC);
  Color get blueGray400 => Color(0XFF8E8E8E);
  Color get blueGray40001 => Color(0XFF77838F);
  Color get blueGray50 => Color(0XFFE8EFF3);
  Color get blueGray700 => Color(0XFF525252);
  Color get blueGray70001 => Color(0XFF535151);
  Color get blueGray800 => Color(0XFF363B64);
  Color get blueGray80001 => Color(0XFF2C406E);
  Color get blueGray80002 => Color(0XFF2F4858);
  Color get blueGray900 => Color(0XFF0B2E40);
  Color get blueGray90001 => Color(0XFF092C4C);
  Color get blueGray90014 => Color(0X142F2F2F);

  // BlueGrayf
  Color get blueGray2003f => Color(0X3FB6C1C8);

  // DeepOrange
  Color get deepOrange300 => Color(0XFFEEA35E);
  Color get deepOrange400 => Color(0XFFFF6B53);
  Color get deepOrange600 => Color(0XFFE06927);
  Color get deepOrange60001 => Color(0XFFE06A27);
  Color get deepOrangeA100 => Color(0XFFFE9874);
  Color get deepOrangeA700 => Color(0XFFF41616);

  // DeepPurple
  Color get deepPurpleA700 => Color(0XFF7120D9);

  // Gray
  Color get gray100 => Color(0XFFF4F6FB);
  Color get gray200 => Color(0XFFE7E7E7);
  Color get gray300 => Color(0XFFE0E0E0);
  Color get gray400 => Color(0XFFC4C4C4);
  Color get gray50 => Color(0XFFF9F9F9);
  Color get gray500 => Color(0XFFA2A2A2);
  Color get gray50001 => Color(0XFF979797);
  Color get gray50002 => Color(0XFFA098AE);
  Color get gray5001 => Color(0XFFFCFCFC);
  Color get gray600 => Color(0XFF7C7E87);
  Color get gray60001 => Color(0XFF737588);
  Color get gray60002 => Color(0XFF7E7B7B);
  Color get gray60003 => Color(0XFF7A7A7A);
  Color get gray60004 => Color(0XFF7C7F88);
  Color get gray700 => Color(0XFF5D5D5D);
  Color get gray800 => Color(0XFF4A4A4A);
  Color get gray80001 => Color(0XFF4E4444);
  Color get gray900 => Color(0XFF1E2022);
  Color get gray90001 => Color(0XFF161A35);
  Color get gray90002 => Color(0XFF1E1D1F);

  // Green
  Color get green400 => Color(0XFF64C882);

  // Indigo
  Color get indigo200 => Color(0XFF9BABC5);
  Color get indigo50 => Color(0XFFE8EAF1);

  // LightBlue
  Color get lightBlue900 => Color(0XFF004E92);
  Color get lightBlueA400 => Color(0XFF04B4FF);

  // Red
  Color get red400 => Color(0XFFE2574C);
  Color get redA100 => Color(0XFFFE8289);

  // Teal
  Color get teal300 => Color(0XFF4CBC9A);
  Color get tealA700 => Color(0XFF00B59C);

  // White
  Color get whiteA700 => Color(0XFFFDFEFE);

  // Yellow
  Color get yellow900 => Color(0XFFE59010);
  Color get yellow90001 => Color(0XFFE68B20);
}

PrimaryColors get appTheme => ThemeHelper().themeColor();
ThemeData get theme => ThemeHelper().themeData();
