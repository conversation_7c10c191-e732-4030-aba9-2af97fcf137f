import 'package:flutter/material.dart';
import 'package:sfm_new/data/constant.dart';
import '../core/app_export.dart';

/// A collection of pre-defined text styles for customizing text appearance,
/// categorized by different font families and weights.
/// Additionally, this class includes extensions on [TextStyle] to easily apply specific font families to text.

class CustomTextStyles {
  static TextStyle get bodyLargeOpenSans =>
      theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        fontSize: 15,
      );

  static get bodyMediumOpenSansBlack900 => theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.black900,
      );

  static get bodyMediumBluegray200 => theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray200,
      );

  static get bodyMediumBluegray700 => theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray700,
      );
  static get bodyMediumBluegray90001 => theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray90001,
      );
  static get bodyMediumGray60002 => theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
      );
  static get bodyMediumGray6000213 => theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
        fontSize: 13.fSize,
      );
  static get bodyMediumGray6000214 => theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
        fontSize: 13.fSize,
      );
  static get bodyMediumGray6000214_1 => theme.textTheme.bodySmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
        fontSize: 11.fSize,
      );
  static get bodyMediumGray6000214_2 => theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
        fontSize: 13.fSize,
      );
  static get bodyMediumLightblue900 => theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.lightBlue900,
        fontSize: 13.fSize,
      );
  static get bodyMediumNotoSansTamilBlack900 =>
      theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.black900,
        fontSize: 13.fSize,
      );
  static get bodyMediumNotoSansTamilOnErrorContainer =>
      theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontSize: 13.fSize,
      );
  static get bodyMediumOpenSansBluegray80002 =>
      theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray80002,
      );
  static get bodyMediumOnErrorContainer => theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
      );
  static get bodyMediumPoppinsBlack900 => theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.black900,
        fontSize: 13.fSize,
      );
  static get bodyMediumPoppinsBluegray900 =>
      theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontSize: 13.fSize,
      );
  static get bodyMediumff004e92 => theme.textTheme.bodyMedium!.copyWith(
      fontFamily: ConstantValues.OpenSans,
      fontWeight: FontWeight.w700,
      color: Color(0XFF004E92),
      decoration: TextDecoration.underline);
  static get bodyMediumff092c4c => theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: Color(0XFF092C4C),
      );
  static get bodyMediumff9e0c21 => theme.textTheme.bodyMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: Color(0XFF9E0C21),
      );
  static get bodySmallGray50002 => theme.textTheme.bodySmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray50002,
      );
  static get bodySmallGray5000210 => theme.textTheme.bodySmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray50002,
        fontSize: 10.fSize,
      );
  static get bodySmallGray90002 => theme.textTheme.bodySmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray90002,
        fontSize: 10.fSize,
      );
  static get bodySmallOpenSans => theme.textTheme.bodySmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
      );

  static get bodySmallOpenSansBlue400 => theme.textTheme.bodySmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blue400,
      );
  static get bodySmallOpenSansBluegray200 =>
      theme.textTheme.bodySmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray200,
      );
  static get bodySmallOpenSansGray500 => theme.textTheme.bodySmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray500,
      );
  static get bodySmallOpenSansGray50001 => theme.textTheme.bodySmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray50001,
      );
  static get bodySmallOpenSansGray60001 => theme.textTheme.bodySmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60001,
        fontSize: 10.fSize,
      );
  static get bodySmallOpenSansGray700 => theme.textTheme.bodySmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray700,
      );
  // Label text style
  static get labelLargeLatoOnErrorContainer =>
      theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontSize: 11.fSize,
        fontWeight: FontWeight.w300,
      );
  static get labelLargeOpenSans => theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
      );
  static get labelLargeOpenSansBluegray400 =>
      theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray400,
        fontSize: 13.fSize,
      );
  static get labelLargeOpenSansBluegray40013 =>
      theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray400,
        fontSize: 13.fSize,
      );
  static get labelLargeOpenSansBluegray900 =>
      theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontSize: 13.fSize,
      );
  static get labelLargeOpenSansBluegray900Bold =>
      theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontWeight: FontWeight.w500,
      );
  static get labelLargeOpenSansGray600 => theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray600,
      );
  static get labelLargeOpenSansGray60004 =>
      theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60004,
      );
  static get labelLargeOpenSansGray800 => theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray800,
      );
  static get labelLargeOpenSansGray900 => theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray900,
        fontWeight: FontWeight.w900,
      );
  static get labelLargeOpenSansOnErrorContainer =>
      theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get labelLargeOpenSansOnErrorContainerExtraBold =>
      theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontSize: 13.fSize,
        fontWeight: FontWeight.w800,
      );
  static get labelLargeOpenSansOnPrimaryContainer =>
      theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onPrimaryContainer,
        fontSize: 13.fSize,
      );
  static get labelLargeOpenSansPrimary => theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.primary,
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get labelLargeOpenSansff0b2e40 => theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: Color(0XFF0B2E40),
        fontSize: 13.fSize,
      );
  static get labelLargeOpenSansff8e8e8e => theme.textTheme.labelLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: Color(0XFF8E8E8E),
        fontSize: 13.fSize,
      );
  static get labelMediumBluegray800 => theme.textTheme.labelMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray800,
        fontWeight: FontWeight.w700,
      );
  static get labelMediumOpenSansGray90001 =>
      theme.textTheme.labelMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray90001,
        fontWeight: FontWeight.w900,
      );
  static get labelMediumSemiBold => theme.textTheme.labelMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        fontWeight: FontWeight.w600,
      );
  static get labelSmallBold => theme.textTheme.labelSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        fontWeight: FontWeight.w700,
      );
  static get labelSmallOpenSans => theme.textTheme.labelSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        fontSize: 8.fSize,
      );
  // Poppins text style
  static get poppinsBlack900 => TextStyle(
        color: appTheme.black900,
        fontSize: 7.fSize,
        fontWeight: FontWeight.w400,
      ).OpenSans;
  static get poppinsGray90002 => TextStyle(
        color: appTheme.gray90002,
        fontSize: 5.fSize,
        fontWeight: FontWeight.w600,
      ).OpenSans;
  static get poppinsGray90002Regular => TextStyle(
        color: appTheme.gray90002,
        fontSize: 5.fSize,
        fontWeight: FontWeight.w400,
      ).OpenSans;
  static get poppinsOnErrorContainer => TextStyle(
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontSize: 5.fSize,
        fontWeight: FontWeight.w400,
      ).OpenSans;
  static get poppinsOnErrorContainerSemiBold => TextStyle(
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontSize: 5.fSize,
        fontWeight: FontWeight.w600,
      ).OpenSans;
  // Title text style
  static get titleLargeBlack900 => theme.textTheme.titleLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.black900.withValues(alpha: 0.8),
        fontWeight: FontWeight.w700,
      );
  static get titleLargeBluegray90001 => theme.textTheme.titleLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray90001,
        fontWeight: FontWeight.w400,
      );
  static get titleLargeBluegray90001Regular =>
      theme.textTheme.titleLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray90001,
        fontSize: 22.fSize,
        fontWeight: FontWeight.w400,
      );
  static get titleLargeGray60002 => theme.textTheme.titleLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
        fontWeight: FontWeight.w700,
      );
  static get titleLargeGray900 => theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        fontSize: 13.fSize,
        color: appTheme.gray900,
        fontWeight: FontWeight.w700,
      );
  static get titleLargeLight => theme.textTheme.titleLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        fontWeight: FontWeight.w300,
        fontSize: 14.fSize,
      );
  static get titleLargePrimary => theme.textTheme.titleLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.primary,
        fontWeight: FontWeight.w700,
      );
  static get titleLargePrimaryBold => theme.textTheme.titleLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.primary,
        fontWeight: FontWeight.w700,
      );
  static get titleLargeRegular => theme.textTheme.titleLarge!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        fontWeight: FontWeight.w400,
      );
  static get titleMediumBluegray70001 => theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray70001,
        fontSize: 15.fSize,
      );
  static get titleMediumBluegray900 => theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
      );
  static get titleMediumBluegray900Bold =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontSize: 15.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleMediumBluegray900Bold18 =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontSize: 14.fSize,
        fontWeight: FontWeight.w600,
      );
  static get titleMediumBluegray900Bold_1 =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontSize: 14.fSize,
        fontWeight: FontWeight.w600,
      );
  static get titleMediumBluegray900Bold_2 =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontWeight: FontWeight.w700,
      );
  static get titleMediumBluegray900Bold_3 =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontWeight: FontWeight.w700,
      );
  static get titleMediumBluegray900Bold_4 =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontWeight: FontWeight.w700,
      );
  static get titleMediumBluegray900_1 => theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
      );
  static get titleMediumErrorContainer => theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.errorContainer.withValues(alpha: 0.73),
        fontSize: 15.fSize,
      );
  static get titleMediumErrorContainer17 =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.errorContainer,
        fontSize: 15.fSize,
      );
  static get titleMediumGray60002 => theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
        fontSize: 15.fSize,
      );
  static get titleMediumGray60002Bold => theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
        fontSize: 14.fSize,
        fontWeight: FontWeight.w600,
      );
  static get titleMediumGray60002Bold17 =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
        fontSize: 15.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleMediumGray60002Bold18 =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
        fontSize: 15.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleMediumOnErrorContainer =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontSize: 15.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleMediumOnErrorContainer18 =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontSize: 15.fSize,
      );
  static get titleMediumOnErrorContainerBold =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontWeight: FontWeight.w700,
      );
  static get titleMediumOnErrorContainer_1 =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
      );
  static get titleMediumOnPrimaryContainer =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onPrimaryContainer,
        fontSize: 13.fSize,
      );
  static get titleMediumOnPrimaryContainer18 =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onPrimaryContainer,
        fontSize: 15.fSize,
      );
  static get titleMediumOnPrimaryContainerBold =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onPrimaryContainer,
        fontSize: 15.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleMediumOnPrimaryContainer_1 =>
      theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onPrimaryContainer,
      );
  static get titleMediumPrimary => theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.primary,
        fontSize: 15.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleMediumPrimary17 => theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.primary,
        fontSize: 15.fSize,
      );
  static get titleMediumPrimary18 => theme.textTheme.titleMedium!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.primary,
        fontSize: 15.fSize,
      );
  static get titleSmall15 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        fontSize: 13.fSize,
      );
  static get titleSmallBlack900 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.black900,
        fontSize: 13.fSize,
      );
  static get titleSmallBlack90015 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.black900.withValues(alpha: 0.7),
        fontSize: 12.fSize,
      );
  static get titleSmallBlack900Bold => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.black900,
        fontWeight: FontWeight.w700,
        fontSize: 13.fSize,
      );
  static get titleSmallBlack900_1 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.black900,
      );
  static get titleSmallBluegray400 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray400,
      );
  static get titleSmallBluegray40001 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray40001,
        fontSize: 13.fSize,
      );
  static get titleSmallBluegray900 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontSize: 13.fSize,
      );
  static get titleSmallBluegray900logout =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.whiteA700,
        fontSize: 13.fSize,
        // fontWeight: FontWeight.w700,
      );
  static get titleSmallBluegray90015 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontSize: 13.fSize,
      );
  static get titleSmallBluegray90015_1 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontSize: 12.fSize,
      );
  static get titleSmallBluegray900Bold => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallBluegray900Bold15 =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallBluegray900Bold_1 =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray900,
        fontWeight: FontWeight.w400,
        fontSize: 12.fSize,
      );
  static get titleSmallGray60002 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallGray6000215 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
        fontSize: 13.fSize,
      );
  static get titleSmallGray60002Bold => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallGray60002_1 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
      );
  static get titleSmallGray60003 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60003,
        fontWeight: FontWeight.w800,
      );
  static get titleSmallLatoBluegray80001 =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray80001,
      );
  static get titleSmallLatoBluegray80001Medium =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray80001,
        fontWeight: FontWeight.w500,
      );
  static get titleSmallLatoOnErrorContainer =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
      );
  static get titleSmallLatoOnErrorContainerMedium =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontWeight: FontWeight.w500,
      );
  static get titleSmallLatoPrimary => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.primary,
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallLightblue900 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.lightBlue900,
      );
  static get titleSmallLightblue90015 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.lightBlue900,
        fontSize: 13.fSize,
      );
  static get titleSmallLightblue900Bold => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.lightBlue900,
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallOpenSansBlack900 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.black900,
        fontSize: 13.fSize,
      );
  static get titleSmallOpenSansGray60002 =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallOpenSansff000000 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: Color(0XFF000000),
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallOpenSansff0b2e40 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: Color(0XFF0B2E40),
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallOpenSansff2f4858 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: Color(0XFF2F4858),
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallOpenSansff2f485815 =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: Color(0XFF2F4858),
        fontSize: 13.fSize,
      );
  static get titleSmallOpenSansff2f4858ExtraBold =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: Color(0XFF2F4858),
        fontSize: 13.fSize,
        fontWeight: FontWeight.w800,
      );
  static get titleSmallOnErrorContainer => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
      );
  static get titleSmallOnErrorContainer15 =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontSize: 13.fSize,
      );
  static get titleSmallOnErrorContainerBold =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallOnErrorContainerExtraBold =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontWeight: FontWeight.w800,
      );
  static get titleSmallOnErrorContainer_1 =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
      );
  static get titleSmallOnErrorContainer_2 =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
      );
  static get titleSmallOnPrimaryContainer =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onPrimaryContainer,
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallOnPrimaryContainerBold =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onPrimaryContainer,
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallPoppinsBluegray800 =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray800,
        fontSize: 13.fSize,
      );
  static get titleSmallPoppinsBluegray800Bold =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.blueGray800,
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallPoppinsGray60002 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: appTheme.gray60002,
      );
  static get titleSmallPoppinsOnError => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onError,
      );
  static get titleSmallPoppinsOnErrorContainer =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
      );
  static get titleSmallPoppinsOnErrorContainer15 =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontSize: 13.fSize,
      );
  static get titleSmallPoppinsOnErrorContainerBold =>
      theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.onErrorContainer.withValues(alpha: 1),
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallPrimary => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.primary,
        fontSize: 13.fSize,
      );
  static get titleSmallPrimary15 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.primary,
        fontSize: 13.fSize,
      );
  static get titleSmallPrimaryBold => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.primary,
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallPrimaryBold15 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.primary,
        fontSize: 13.fSize,
        fontWeight: FontWeight.w700,
      );
  static get titleSmallPrimary_1 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: theme.colorScheme.primary,
      );
  static get titleSmallff000000 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: Color(0XFF000000),
        fontWeight: FontWeight.w700,
      );
  static get titleSmallff0b2e40 => theme.textTheme.titleSmall!.copyWith(
        fontFamily: ConstantValues.OpenSans,
        color: Color(0XFF0B2E40),
        fontWeight: FontWeight.w700,
      );
}

extension on TextStyle {
  TextStyle get OpenSans {
    return copyWith(
      fontFamily: 'OpenSans',
    );
  }
}
