// ignore_for_file: unused_local_variable

import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:geolocator/geolocator.dart';
import 'package:sfm_new/core/app_export.dart';
import 'package:sfm_new/core/utils/flutter_toast_message.dart';
import 'package:sfm_new/data/apiClient/api_client.dart';
import 'package:sfm_new/data/constant.dart';
import 'package:sfm_new/data/notification_service.dart';
import 'package:sfm_new/data/shared_pref_manager.dart';
import 'package:dio/dio.dart' as dio_package;
import 'package:sfm_new/firebase_options.dart';
import 'package:dio/dio.dart';
import 'package:sfm_new/localization/en_us/en_us_translations.dart';
import 'package:sfm_new/sfmDatabase/DatabaseHelper/database_helper.dart';

Future _firebaseBackgroundMessage(RemoteMessage message) async {
  if (message.notification != null) {
    print("Some notification received in background");
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light);

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  await PushNotifications.init();
  await PushNotifications.localNotiInit();
  FirebaseMessaging.onBackgroundMessage(_firebaseBackgroundMessage);
  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    String payloadData = jsonEncode(message.data);
    print("payloadData: ${payloadData}");
    print("Got a message in foreground");
    if (message.notification != null) {
      PushNotifications.showSimpleNotification(
          title: message.notification!.title!,
          body: message.notification!.body!,
          payload: payloadData);
    }
  });

  final notificationController = NotificationController();
  final String initialRoute = await notificationController.getInitialRoute();

  Map<String, String> initialTranslations = {};

  final db = await DatabaseProvider.database;
  final List<Map<String, dynamic>> maps =
      await db.query('${TableValues.tableLanguage}');

  if (maps.isNotEmpty) {
    for (var map in maps) {
      initialTranslations[map['${TableValues.langTranslateKey}']] =
          map['${TableValues.langTranslateValue}'];
    }
  } else {
    initialTranslations = LocalizationService().enUs;
  }

  // Set localization data in the app
  runApp(MyApp(
    initialRoute: initialRoute,
    initialTranslations: initialTranslations,
  ));
}

class MyApp extends StatelessWidget {
  final String initialRoute;
  final Map<String, String> initialTranslations;

  MyApp({required this.initialRoute, required this.initialTranslations});

  @override
  Widget build(BuildContext context) {
    return Sizer(builder: (context, orientation, deviceType) {
      return GetMaterialApp(
        debugShowCheckedModeBanner: false,
        theme: theme,
        translations: AppLocalization(initialTranslations),
        locale: Locale('en', 'US'),
        supportedLocales: [
          Locale('en', 'US'),
        ],
        localizationsDelegates: [
        ],
        fallbackLocale: Locale('en', 'US'),
        title: 'sl',
        initialBinding: InitialBindings(),
        initialRoute: initialRoute,
        getPages: AppRoutes.pages,
        builder: EasyLoading.init(),
      );
    });
  }
}

class NotificationController extends GetxController {
  final Dio dio = Dio();
  RxDouble lat = 0.0.obs;
  RxDouble long = 0.0.obs;
  RxDouble accuracy = 0.0.obs;
  RxString token = ''.obs;

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> updateFCMToken() async {
    final messaging = FirebaseMessaging.instance;
    final newToken = await messaging.getToken();
    print("Initial FCM Token: $newToken");

    messaging.onTokenRefresh.listen((newToken) async {
      print("FCM Token refreshed: $newToken");

      final employeeToken =
          await SharedPrefManager.instance.getString(ConstantValues.token);

      if (employeeToken != null) {
        await updateFCMTokenAPI(employeeToken, newToken);
      }
    });
  }

  Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
    // Handle background message
    print("Handling a background message: ${message.notification?.title}");

    // Initialize notification controller
    final notificationController = NotificationController();

    // Show notification
    await notificationController.showNotification(message);
  }

  Future<void> getLocation() async {
    Position position;
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();

    if (!serviceEnabled) {
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return;
    }

    if (permission == LocationPermission.unableToDetermine) {}

    position = await Geolocator.getCurrentPosition();

    lat.value = position.latitude;
    long.value = position.longitude;

    print("lat: ${lat.value}");
    print("long: ${long.value}");
  }

  Future<bool> checkout() async {
    bool isSuccess = false;
    token.value =
        await SharedPrefManager.instance.getString(ConstantValues.token) ?? "";
    print("token.value: ${token.value}");
    print("accuracy checkout main: ${accuracy.value}");
    try {
      Map<String, dynamic> headers = {
        'Authorization': 'Bearer ${token.value}',
      };
      final response = await ApiClient.dioClient
          .post(
        ApiClient.getUrl(ApiClient.checkoutEndpoint),
        data: {
          'latitude': '${lat.value}',
          'longitude': '${long.value}',
        },
        options: Options(headers: headers),
      )
          .timeout(Duration(seconds: 60), onTimeout: () {
        print('API call timed out');
        throw TimeoutException("API call exceeded the 60 seconds timeout");
      });
      print(response.data);
      try {
        if (response.data != null) {
          if (response.statusCode == 200) {
            SharedPrefManager.instance
                .setBool(ConstantValues.isDailyLogOut, true);

            showToastMessage("Logged out sucessfully");

            isSuccess = true;
          }
        } else {
          print('Response data is null');
        }
      } catch (e) {
        print('Error parsing response data: $e');
      }
    } catch (e) {
      print(e);
    } finally {
      return isSuccess;
    }
  }

  Future<String> getInitialRoute() async {
    final String? token =
        await SharedPrefManager.instance.getString(ConstantValues.token);
    final bool? isCheckedIn =
        await SharedPrefManager.instance.getBool(ConstantValues.isCheckedIn);
    final bool? isSyncSuccess = await SharedPrefManager.instance
            .getBool(ConstantValues.isSyncSuccessful) ??
        false;
    final String? todayDate = await getTodayDate();
    final String? loginDate =
        await SharedPrefManager.instance.getString(ConstantValues.loginDate);

    final bool? isTodayLogout =
        await SharedPrefManager.instance.getBool(ConstantValues.isDailyLogOut);

    await getLocation();

    SharedPrefManager.instance.setInt(ConstantValues.syncDays, 7);

    final double? accuracyData = await SharedPrefManager.instance
            .getDouble(ConstantValues.requiredAccuracy) ??
        500;

    if (token != null) {
      if (loginDate != null && todayDate != loginDate) {
        bool checkoutSuccess = await checkout();
        if (checkoutSuccess) {
          return AppRoutes.loginOneScreen;
        } else {
          return AppRoutes.loginOneScreen;
        }
      } else {
        if (isCheckedIn != null && isCheckedIn) {
          if (isSyncSuccess == true) {
            if (isTodayLogout == true) {
              return AppRoutes.loginOneScreen;
            } else {
              return AppRoutes.homeScreen;
            }
          } else {
            if (isTodayLogout == true) {
              return AppRoutes.loginOneScreen;
            } else {
              return AppRoutes.syncDataScreen;
            }
          }
        } else {
          return AppRoutes.attendanceScreen;
        }
      }
    } else {
      return AppRoutes.loginScreen;
    }
  }

  @override
  void onInit() {
    super.onInit();
    _initializeLocalNotifications();
  }

  Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
    );

    await flutterLocalNotificationsPlugin.initialize(initializationSettings);
  }

  Future<void> showNotification(RemoteMessage message) async {
    AndroidNotificationChannel channel = AndroidNotificationChannel(
        message.notification!.android!.channelId.toString(),
        message.notification!.android!.channelId.toString(),
        importance: Importance.max,
        showBadge: true,
        playSound: true,
        sound: const RawResourceAndroidNotificationSound('jetsons_doorbell'));
    print("AndroidNotificationChannel: ${channel}");

    AndroidNotificationDetails androidNotificationDetails =
        AndroidNotificationDetails(
            channel.id.toString(), channel.name.toString(),
            channelDescription: '',
            importance: Importance.high,
            priority: Priority.high,
            playSound: true,
            ticker: 'ticker',
            sound: channel.sound);
    print("AndroidNotificationDetails: ${androidNotificationDetails}");

    const DarwinNotificationDetails darwinNotificationDetails =
        DarwinNotificationDetails(
            presentAlert: true, presentBadge: true, presentSound: true);

    NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails, iOS: darwinNotificationDetails);

    flutterLocalNotificationsPlugin.show(
      0,
      message.notification!.title.toString(),
      message.notification!.body.toString(),
      notificationDetails,
    );
  }
}

Future<String?> getTodayDate() async {
  final DateTime now = DateTime.now(); 
  final String formattedDate = '${now.year}-${now.month}-${now.day}';
  return formattedDate;
}

Future<void> updateFCMTokenAPI(String employeeToken, String fcmToken) async {
  try {
    final dio_package.Dio dioClient = dio_package.Dio();

    final data = {"fcm_token": fcmToken};
    final headers = {'Authorization': 'Bearer $employeeToken'};

    final response = await ApiClient.dioClient
        .post(
      ApiClient.getUrl(ApiClient.postAddExpenseEndpoint),
      data: data,
      options: dio_package.Options(headers: headers),
    )
        .timeout(Duration(seconds: 60), onTimeout: () {
      print('API call timed out');
      throw TimeoutException("API call exceeded the 60 seconds timeout");
    });

    if (response.statusCode == 200) {
      EasyLoading.dismiss();
    }
  } catch (e) {
    print('Error updating FCM token: $e');
  }
}

Future<Map<String, String>> fetchLocalizationData() async {
  final db = await DatabaseProvider.database;
  final List<Map<String, dynamic>> maps =
      await db.query("${TableValues.tableLanguage}");

  Map<String, String> localizationData = {};
  for (var map in maps) {
    localizationData[map['${TableValues.langTranslateKey}']] =
        map['${TableValues.langTranslateValue}'];
  }

  return localizationData;
}
